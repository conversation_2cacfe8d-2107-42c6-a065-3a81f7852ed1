[{"cell": null, "code": "F821", "end_location": {"column": 32, "row": 494}, "filename": "D:\\Coding\\CLEAR\\apps\\infrastructure\\views\\visual_query_builder_views.py", "fix": null, "location": {"column": 17, "row": 494}, "message": "Undefined name `ValidationError`", "noqa_row": 494, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 53, "row": 690}, "filename": "D:\\Coding\\CLEAR\\apps\\knowledge\\views.py", "fix": null, "location": {"column": 44, "row": 690}, "message": "Undefined name `HTTPError`", "noqa_row": 690, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 30, "row": 122}, "filename": "D:\\Coding\\CLEAR\\apps\\knowledge\\views\\ai_dashboard_views.py", "fix": null, "location": {"column": 17, "row": 122}, "message": "Undefined name `DatabaseError`", "noqa_row": 122, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 46, "row": 122}, "filename": "D:\\Coding\\CLEAR\\apps\\knowledge\\views\\ai_dashboard_views.py", "fix": null, "location": {"column": 32, "row": 122}, "message": "Undefined name `IntegrityError`", "noqa_row": 122, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 64, "row": 122}, "filename": "D:\\Coding\\CLEAR\\apps\\knowledge\\views\\ai_dashboard_views.py", "fix": null, "location": {"column": 48, "row": 122}, "message": "Undefined name `OperationalError`", "noqa_row": 122, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 61, "row": 174}, "filename": "D:\\Coding\\CLEAR\\apps\\messaging\\services\\notifications.py", "fix": null, "location": {"column": 52, "row": 174}, "message": "Undefined name `HTTPError`", "noqa_row": 174, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 22, "row": 51}, "filename": "D:\\Coding\\CLEAR\\apps\\messaging\\views\\htmx\\comment_htmx.py", "fix": null, "location": {"column": 2, "row": 51}, "message": "Undefined name `require_http_methods`", "noqa_row": 51, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 22, "row": 119}, "filename": "D:\\Coding\\CLEAR\\apps\\messaging\\views\\htmx\\comment_htmx.py", "fix": null, "location": {"column": 2, "row": 119}, "message": "Undefined name `require_http_methods`", "noqa_row": 119, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 22, "row": 217}, "filename": "D:\\Coding\\CLEAR\\apps\\messaging\\views\\htmx\\comment_htmx.py", "fix": null, "location": {"column": 2, "row": 217}, "message": "Undefined name `require_http_methods`", "noqa_row": 217, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 22, "row": 262}, "filename": "D:\\Coding\\CLEAR\\apps\\messaging\\views\\htmx\\comment_htmx.py", "fix": null, "location": {"column": 2, "row": 262}, "message": "Undefined name `require_http_methods`", "noqa_row": 262, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 22, "row": 322}, "filename": "D:\\Coding\\CLEAR\\apps\\messaging\\views\\htmx\\comment_htmx.py", "fix": null, "location": {"column": 2, "row": 322}, "message": "Undefined name `require_http_methods`", "noqa_row": 322, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 22, "row": 368}, "filename": "D:\\Coding\\CLEAR\\apps\\messaging\\views\\htmx\\comment_htmx.py", "fix": null, "location": {"column": 2, "row": 368}, "message": "Undefined name `require_http_methods`", "noqa_row": 368, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 22, "row": 407}, "filename": "D:\\Coding\\CLEAR\\apps\\messaging\\views\\htmx\\comment_htmx.py", "fix": null, "location": {"column": 2, "row": 407}, "message": "Undefined name `require_http_methods`", "noqa_row": 407, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 22, "row": 463}, "filename": "D:\\Coding\\CLEAR\\apps\\messaging\\views\\htmx\\comment_htmx.py", "fix": null, "location": {"column": 2, "row": 463}, "message": "Undefined name `require_http_methods`", "noqa_row": 463, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 22, "row": 509}, "filename": "D:\\Coding\\CLEAR\\apps\\messaging\\views\\htmx\\comment_htmx.py", "fix": null, "location": {"column": 2, "row": 509}, "message": "Undefined name `require_http_methods`", "noqa_row": 509, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 22, "row": 578}, "filename": "D:\\Coding\\CLEAR\\apps\\messaging\\views\\htmx\\comment_htmx.py", "fix": null, "location": {"column": 2, "row": 578}, "message": "Undefined name `require_http_methods`", "noqa_row": 578, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 22, "row": 619}, "filename": "D:\\Coding\\CLEAR\\apps\\messaging\\views\\htmx\\comment_htmx.py", "fix": null, "location": {"column": 2, "row": 619}, "message": "Undefined name `require_http_methods`", "noqa_row": 619, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 22, "row": 660}, "filename": "D:\\Coding\\CLEAR\\apps\\messaging\\views\\htmx\\comment_htmx.py", "fix": null, "location": {"column": 2, "row": 660}, "message": "Undefined name `require_http_methods`", "noqa_row": 660, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 22, "row": 729}, "filename": "D:\\Coding\\CLEAR\\apps\\messaging\\views\\htmx\\comment_htmx.py", "fix": null, "location": {"column": 2, "row": 729}, "message": "Undefined name `require_http_methods`", "noqa_row": 729, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 22, "row": 772}, "filename": "D:\\Coding\\CLEAR\\apps\\messaging\\views\\htmx\\comment_htmx.py", "fix": null, "location": {"column": 2, "row": 772}, "message": "Undefined name `require_http_methods`", "noqa_row": 772, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 22, "row": 851}, "filename": "D:\\Coding\\CLEAR\\apps\\messaging\\views\\htmx\\comment_htmx.py", "fix": null, "location": {"column": 2, "row": 851}, "message": "Undefined name `require_http_methods`", "noqa_row": 851, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 22, "row": 923}, "filename": "D:\\Coding\\CLEAR\\apps\\messaging\\views\\htmx\\comment_htmx.py", "fix": null, "location": {"column": 2, "row": 923}, "message": "Undefined name `require_http_methods`", "noqa_row": 923, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 40, "row": 539}, "filename": "D:\\Coding\\CLEAR\\apps\\messaging\\views\\webrtc_views.py", "fix": null, "location": {"column": 39, "row": 539}, "message": "Undefined name `F`", "noqa_row": 539, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 65, "row": 95}, "filename": "D:\\Coding\\CLEAR\\apps\\projects\\management\\commands\\find_duplicate_persons.py", "fix": null, "location": {"column": 57, "row": 95}, "message": "Undefined name `QuerySet`", "noqa_row": 95, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 50, "row": 122}, "filename": "D:\\Coding\\CLEAR\\apps\\projects\\management\\commands\\find_duplicate_persons.py", "fix": null, "location": {"column": 42, "row": 122}, "message": "Undefined name `QuerySet`", "noqa_row": 122, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 43, "row": 396}, "filename": "D:\\Coding\\CLEAR\\apps\\projects\\middleware.py", "fix": null, "location": {"column": 30, "row": 396}, "message": "Undefined name `DatabaseError`", "noqa_row": 396, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 61, "row": 396}, "filename": "D:\\Coding\\CLEAR\\apps\\projects\\middleware.py", "fix": null, "location": {"column": 45, "row": 396}, "message": "Undefined name `OperationalError`", "noqa_row": 396, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 77, "row": 396}, "filename": "D:\\Coding\\CLEAR\\apps\\projects\\middleware.py", "fix": null, "location": {"column": 63, "row": 396}, "message": "Undefined name `InterfaceError`", "noqa_row": 396, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 47, "row": 234}, "filename": "D:\\Coding\\CLEAR\\apps\\projects\\services\\mention_service.py", "fix": null, "location": {"column": 33, "row": 234}, "message": "Undefined name `TaskAssignment`", "noqa_row": 234, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 19, "row": 529}, "filename": "D:\\Coding\\CLEAR\\apps\\projects\\signals.py", "fix": null, "location": {"column": 11, "row": 529}, "message": "Undefined name `pre_save`", "noqa_row": 529, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 13, "row": 259}, "filename": "D:\\Coding\\CLEAR\\apps\\projects\\tests\\test_organization_boundaries.py", "fix": null, "location": {"column": 9, "row": 259}, "message": "Undefined name `User`", "noqa_row": 259, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 30, "row": 311}, "filename": "D:\\Coding\\CLEAR\\apps\\projects\\tests\\test_organization_boundaries.py", "fix": null, "location": {"column": 23, "row": 311}, "message": "Undefined name `Project`", "noqa_row": 311, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 34, "row": 331}, "filename": "D:\\Coding\\CLEAR\\apps\\projects\\tests\\test_organization_boundaries.py", "fix": null, "location": {"column": 27, "row": 331}, "message": "Undefined name `Project`", "noqa_row": 331, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 44, "row": 706}, "filename": "D:\\Coding\\CLEAR\\apps\\projects\\views\\integration_views.py", "fix": null, "location": {"column": 40, "row": 706}, "message": "Undefined name `self`", "noqa_row": 706, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 42, "row": 694}, "filename": "D:\\Coding\\CLEAR\\apps\\projects\\views\\project_views.py", "fix": null, "location": {"column": 38, "row": 694}, "message": "Undefined name `self`", "noqa_row": 694, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 61, "row": 619}, "filename": "D:\\Coding\\CLEAR\\apps\\projects\\views\\stakeholder_views.py", "fix": null, "location": {"column": 52, "row": 619}, "message": "Undefined name `HTTPError`", "noqa_row": 619, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 57, "row": 374}, "filename": "D:\\Coding\\CLEAR\\apps\\projects\\viewsets.py", "fix": null, "location": {"column": 48, "row": 374}, "message": "Undefined name `HTTPError`", "noqa_row": 374, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 30, "row": 260}, "filename": "D:\\Coding\\CLEAR\\apps\\realtime\\apps.py", "fix": null, "location": {"column": 17, "row": 260}, "message": "Undefined name `DatabaseError`", "noqa_row": 260, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 46, "row": 260}, "filename": "D:\\Coding\\CLEAR\\apps\\realtime\\apps.py", "fix": null, "location": {"column": 32, "row": 260}, "message": "Undefined name `IntegrityError`", "noqa_row": 260, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 64, "row": 260}, "filename": "D:\\Coding\\CLEAR\\apps\\realtime\\apps.py", "fix": null, "location": {"column": 48, "row": 260}, "message": "Undefined name `OperationalError`", "noqa_row": 260, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 29, "row": 161}, "filename": "D:\\Coding\\CLEAR\\apps\\versioning\\mixins.py", "fix": null, "location": {"column": 25, "row": 161}, "message": "Undefined name `json`", "noqa_row": 161, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 65, "row": 453}, "filename": "D:\\Coding\\CLEAR\\apps\\versioning\\serializers.py", "fix": null, "location": {"column": 56, "row": 453}, "message": "Undefined name `HTTPError`", "noqa_row": 453, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 40, "row": 850}, "filename": "D:\\Coding\\CLEAR\\apps\\versioning\\serializers.py", "fix": null, "location": {"column": 25, "row": 850}, "message": "Undefined name `ValidationError`", "noqa_row": 850, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 48, "row": 550}, "filename": "D:\\Coding\\CLEAR\\apps\\versioning\\signals.py", "fix": null, "location": {"column": 32, "row": 550}, "message": "Undefined name `UniversalVersion`", "noqa_row": 550, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 23, "row": 559}, "filename": "D:\\Coding\\CLEAR\\apps\\versioning\\signals.py", "fix": null, "location": {"column": 13, "row": 559}, "message": "Undefined name `VersionLog`", "noqa_row": 559, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 50, "row": 45}, "filename": "D:\\Coding\\CLEAR\\clear_cli\\fixers\\code_fixes\\fix_for_loop_empty.py", "fix": null, "location": {"column": 37, "row": 45}, "message": "Undefined name `issue_pattern`", "noqa_row": 45, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 50, "row": 27}, "filename": "D:\\Coding\\CLEAR\\clear_cli\\fixers\\code_fixes\\fix_sr_only.py", "fix": null, "location": {"column": 37, "row": 27}, "message": "Undefined name `issue_pattern`", "noqa_row": 27, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 28, "row": 334}, "filename": "D:\\Coding\\CLEAR\\config\\logging\\filters.py", "fix": null, "location": {"column": 24, "row": 334}, "message": "Undefined name `time`", "noqa_row": 334, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 24, "row": 68}, "filename": "D:\\Coding\\CLEAR\\debug_password_reset_urls.py", "fix": null, "location": {"column": 16, "row": 68}, "message": "Undefined name `settings`", "noqa_row": 68, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 47, "row": 597}, "filename": "D:\\Coding\\CLEAR\\tests\\django\\activity\\test_htmx_integration.py", "fix": null, "location": {"column": 35, "row": 597}, "message": "Undefined name `Organization`", "noqa_row": 597, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 47, "row": 572}, "filename": "D:\\Coding\\CLEAR\\tests\\django\\analytics\\test_htmx_integration.py", "fix": null, "location": {"column": 35, "row": 572}, "message": "Undefined name `Organization`", "noqa_row": 572, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}, {"cell": null, "code": "F821", "end_location": {"column": 47, "row": 504}, "filename": "D:\\Coding\\CLEAR\\tests\\django\\core\\test_htmx_integration.py", "fix": null, "location": {"column": 35, "row": 504}, "message": "Undefined name `Organization`", "noqa_row": 504, "url": "https://docs.astral.sh/ruff/rules/undefined-name"}]