"""Users App Model Compliance Check for Django 5.2

This check validates user-related models across multiple apps against:
- Django 5.2 user management specifications
- Privacy and GDPR compliance
- Security best practices for user data
- Cross-app user data consistency
- User profile and preference management

Since the users app is deprecated, this check analyzes user-related models
across authentication, profiles, activity, feedback, and notes apps.

Paired Script: scripts/compliance/users_model_compliance.py
"""

from django.apps import apps
from django.contrib.auth import get_user_model
from django.core.checks import Error, Info, Tags, Warning, register
from django.db import models, DatabaseError, IntegrityError, OperationalError


@register(Tags.models, Tags.security)
def check_users_model_compliance(app_configs, **kwargs):
    """Quick users ecosystem model compliance check that runs on server startup.

    Focuses on privacy and security-critical issues in user-related models:
    - User profile data protection
    - Privacy consent tracking
    - Cross-app user data consistency
    - User preference security
    - GDPR compliance requirements

    For detailed analysis, run:
    python scripts/compliance/users_model_compliance.py
    """
    errors = []

    # Only check if users app is configured (even deprecated)
    users_app_config = None
    if app_configs:
        for app_config in app_configs:
            if app_config.name == "apps.users" or app_config.label == "users":
                users_app_config = app_config
                break

    if not users_app_config:
        return errors

    # Critical privacy and security checks
    errors.extend(_check_user_profile_privacy())
    errors.extend(_check_gdpr_compliance())
    errors.extend(_check_user_data_consistency())
    errors.extend(_check_preference_security())
    errors.extend(_check_activity_tracking_privacy())
    errors.extend(_check_user_content_ownership())
    errors.extend(_check_cross_app_integration())
    errors.extend(_check_user_data_indexes())

    # Always reference the detailed script if issues found
    if errors:
        errors.append(
            Info(
                f"Found {len(errors)} user model issues. "
                "For comprehensive analysis and automated fixes, run:\n"
                "python scripts/compliance/users_model_compliance.py --fix",
                hint="The compliance script can automatically fix most privacy and security issues",
                id="users_models.I999",
            ),
        )

    return errors


def _check_user_profile_privacy() -> list:
    """Check UserProfile model for privacy compliance."""
    errors = []

    try:
        from apps.authentication.models import UserProfile

        profile_fields = {field.name for field in UserProfile._meta.get_fields()}

        # Check for privacy-critical fields
        privacy_fields = {
            "ui_preferences",
            "notification_preferences",
            "command_palette_preferences",
            "help_system_data",
            "activity_data",
        }

        missing_privacy_fields = privacy_fields - profile_fields
        if missing_privacy_fields:
            errors.append(
                Error(
                    f"UserProfile missing privacy fields: {missing_privacy_fields}",
                    hint="User profiles should track all privacy-sensitive preferences",
                    id="users_models.E001",
                ),
            )

        # Check for proper JSONField usage for preferences
        for field_name in privacy_fields:
            if field_name in profile_fields:
                field = UserProfile._meta.get_field(field_name)
                if not isinstance(field, models.JSONField):
                    errors.append(
                        Warning(
                            f"UserProfile.{field_name} should use JSONField for flexibility",
                            hint="Use JSONField for user preferences and settings",
                            id="users_models.W001",
                        ),
                    )

        # Check for proper data access methods
        required_methods = [
            "get_ui_preference",
            "set_ui_preference",
            "get_notification_preference",
            "set_notification_preference",
            "get_command_palette_preference",
            "set_command_palette_preference",
        ]

        for method_name in required_methods:
            if not hasattr(UserProfile, method_name):
                errors.append(
                    Warning(
                        f"UserProfile missing {method_name} method",
                        hint=f"Implement {method_name} for controlled preference access",
                        id="users_models.W002",
                    ),
                )

    except ImportError:
        errors.append(
            Error(
                "UserProfile model not found in authentication app",
                hint="Implement UserProfile model for user preference management",
                id="users_models.E002",
            ),
        )

    return errors


def _check_gdpr_compliance() -> list:
    """Check models for GDPR compliance requirements."""
    errors = []

    User = get_user_model()
    user_fields = {field.name for field in User._meta.get_fields()}

    # Check for GDPR consent tracking
    gdpr_fields = {
        "privacy_consent",
        "privacy_consent_date",
        "data_processing_consent",
        "marketing_consent",
    }

    missing_gdpr_fields = gdpr_fields - user_fields
    if missing_gdpr_fields:
        errors.append(
            Error(
                f"User model missing GDPR fields: {missing_gdpr_fields}",
                hint="GDPR compliance requires explicit consent tracking",
                id="users_models.E003",
            ),
        )

    # Check for data retention fields
    if "data_retention_policy" not in user_fields:
        errors.append(
            Warning(
                "User model missing data_retention_policy field",
                hint="Consider adding field to track user data retention preferences",
                id="users_models.W003",
            ),
        )

    # Check user-related models for proper GDPR data handling
    User = get_user_model()

    for app_config in apps.get_app_configs():
        try:
            for model in app_config.get_models():
                # Check if model has user relation
                model_fields = {field.name for field in model._meta.get_fields()}
                if "user" in model_fields:
                    # Check for proper deletion cascade
                    user_field = model._meta.get_field("user")
                    if (
                        hasattr(user_field, "on_delete")
                        and hasattr(user_field, "related_model")
                        and user_field.related_model == User
                        and user_field.on_delete != models.CASCADE
                    ):
                        errors.append(
                            Warning(
                                f"{model.__name__}.user field should use CASCADE for GDPR compliance",
                                hint="User data should be automatically deleted when user is deleted",
                                id="users_models.W004",
                            ),
                        )
        except (AttributeError, KeyError, ValueError, TypeError):
            pass  # Skip problematic models

    return errors


def _check_user_data_consistency() -> list:
    """Check consistency of user data across apps."""
    errors = []

    User = get_user_model()

    # Check UserProfile relationship
    try:
        profile_relation = None
        for field in User._meta.get_fields():
            if hasattr(field, "related_model") and field.related_model and "profile" in field.name.lower():
                profile_relation = field
                break

        if not profile_relation:
            errors.append(
                Error(
                    "User model missing profile relationship",
                    hint="User should have OneToOneField relationship with UserProfile",
                    id="users_models.E004",
                ),
            )
        elif not isinstance(profile_relation, models.OneToOneField):
            errors.append(
                Warning(
                    "User-Profile relationship should be OneToOneField",
                    hint="Use OneToOneField for user profile relationship",
                    id="users_models.W005",
                ),
            )

    except (DatabaseError, IntegrityError, OperationalError):
        pass

    # Check for consistent user references across apps
    user_related_models = []
    for app_name in ["profiles", "activity", "feedback", "notes"]:
        try:
            app_config = apps.get_app_config(app_name)
            for model in app_config.get_models():
                model_fields = {field.name for field in model._meta.get_fields()}
                if "user" in model_fields:
                    user_related_models.append(model)
        except LookupError:
            pass

    # Check that all user relations use settings.AUTH_USER_MODEL
    for model in user_related_models:
        user_field = model._meta.get_field("user")
        if hasattr(user_field, "related_model") and user_field.related_model != User:
            errors.append(
                Warning(
                    f"{model.__name__}.user field not using AUTH_USER_MODEL",
                    hint="Use settings.AUTH_USER_MODEL for user foreign keys",
                    id="users_models.W006",
                ),
            )

    return errors


def _check_preference_security() -> list:
    """Check user preference models for security compliance."""
    errors = []

    try:
        from apps.authentication.models import UserProfile

        # Check for secure default preferences
        default_methods = [
            "get_default_ui_preferences",
            "get_default_notification_preferences",
            "get_default_command_palette_preferences",
        ]

        for method_name in default_methods:
            if hasattr(UserProfile, method_name):
                method = getattr(UserProfile, method_name)
                if callable(method):
                    try:
                        defaults = method()
                        # Check for secure defaults
                        if isinstance(defaults, dict):
                            # Check notification defaults for privacy
                            if "notification" in method_name.lower():
                                if defaults.get("email_notifications", True):
                                    errors.append(
                                        Info(
                                            "Default email notifications enabled",
                                            hint="Consider privacy-first defaults (opt-in rather than opt-out)",
                                            id="users_models.I001",
                                        ),
                                    )
                    except (FileNotFoundError, PermissionError, OSError):
                        pass

        # Check for preference validation
        if not hasattr(UserProfile, "validate_preferences"):
            errors.append(
                Warning(
                    "UserProfile missing preference validation",
                    hint="Add validate_preferences method to ensure data integrity",
                    id="users_models.W007",
                ),
            )

    except ImportError:
        pass  # Already handled in _check_user_profile_privacy

    return errors


def _check_activity_tracking_privacy() -> list:
    """Check activity tracking models for privacy compliance."""
    errors = []

    try:
        from apps.activity.models import UserActivity, UserPresence

        # Check UserActivity model
        activity_fields = {field.name for field in UserActivity._meta.get_fields()}

        # Check for privacy-sensitive tracking
        if "ip_address" in activity_fields:
            ip_field = UserActivity._meta.get_field("ip_address")
            if not ip_field.null:
                errors.append(
                    Warning(
                        "UserActivity.ip_address should be nullable for privacy",
                        hint="Make IP address optional to respect user privacy",
                        id="users_models.W008",
                    ),
                )

        # Check for data retention considerations
        if not any("retention" in field.name for field in UserActivity._meta.get_fields()):
            errors.append(
                Info(
                    "UserActivity missing data retention fields",
                    hint="Consider adding retention_policy or expires_at field",
                    id="users_models.I002",
                ),
            )

        # Check UserPresence model for session privacy
        presence_fields = {field.name for field in UserPresence._meta.get_fields()}

        if "session_id" in presence_fields:
            session_field = UserPresence._meta.get_field("session_id")
            if hasattr(session_field, "max_length") and session_field.max_length > 255:
                errors.append(
                    Info(
                        "UserPresence.session_id might be too long",
                        hint="Consider shorter session identifiers for privacy",
                        id="users_models.I003",
                    ),
                )

    except ImportError:
        errors.append(
            Warning(
                "Activity tracking models not found",
                hint="Activity models are important for user experience analytics",
                id="users_models.W009",
            ),
        )

    return errors


def _check_user_content_ownership() -> list:
    """Check user content models for proper ownership and privacy."""
    errors = []

    content_models = []

    # Check notes models
    try:
        from apps.notes.models import NotebookEntry, UserNote

        content_models.extend([UserNote, NotebookEntry])
    except ImportError:
        pass

    # Check feedback models
    try:
        from apps.feedback.models import FeatureRequest, FeatureVote

        content_models.extend([FeatureRequest, FeatureVote])
    except ImportError:
        pass

    for model in content_models:
        model_fields = {field.name for field in model._meta.get_fields()}

        # Check for user ownership
        if "user" not in model_fields:
            errors.append(
                Error(
                    f"{model.__name__} missing user ownership field",
                    hint="User content models must have clear ownership",
                    id="users_models.E005",
                ),
            )

        # Check for privacy controls
        if "is_private" not in model_fields and "privacy_level" not in model_fields:
            errors.append(
                Warning(
                    f"{model.__name__} missing privacy controls",
                    hint="User content should have privacy level controls",
                    id="users_models.W010",
                ),
            )

        # Check for proper UUIDs in user content
        if not isinstance(model._meta.pk, models.UUIDField):
            errors.append(
                Warning(
                    f"{model.__name__} not using UUIDField as primary key",
                    hint="Use UUIDs for better privacy in user content models",
                    id="users_models.W011",
                ),
            )

    return errors


def _check_cross_app_integration() -> list:
    """Check integration between user-related apps."""
    errors = []

    # Check if skills integration works
    try:
        from apps.profiles.models import UserSkill

        skill_fields = {field.name for field in UserSkill._meta.get_fields()}

        # Check for proper user relationship
        if "user" in skill_fields:
            user_field = UserSkill._meta.get_field("user")
            if not hasattr(user_field, "related_name") or not user_field.related_name:
                errors.append(
                    Warning(
                        "UserSkill.user field missing related_name",
                        hint='Add related_name="skills" for better reverse lookup',
                        id="users_models.W012",
                    ),
                )

        # Check for skill verification
        if "is_verified" in skill_fields and "verified_by" in skill_fields:
            verified_by_field = UserSkill._meta.get_field("verified_by")
            if hasattr(verified_by_field, "on_delete") and verified_by_field.on_delete != models.SET_NULL:
                errors.append(
                    Info(
                        "UserSkill.verified_by should use SET_NULL on delete",
                        hint="Preserve skill records when verifier is deleted",
                        id="users_models.I004",
                    ),
                )

    except ImportError:
        errors.append(
            Info(
                "UserSkill model not found in profiles app",
                hint="Skills integration enhances user profiles",
                id="users_models.I005",
            ),
        )

    # Check collaboration settings integration
    try:
        from django.db import DatabaseError, IntegrityError, OperationalError

        from apps.authentication.models import CollaborationSettings

        collab_fields = {field.name for field in CollaborationSettings._meta.get_fields()}

        # Check for comprehensive notification preferences
        notification_fields = [
            "email_notifications",
            "push_notifications",
            "desktop_notifications",
        ]

        missing_notification_fields = [field for field in notification_fields if field not in collab_fields]
        if missing_notification_fields:
            errors.append(
                Warning(
                    f"CollaborationSettings missing notification fields: {missing_notification_fields}",
                    hint="Provide comprehensive notification control options",
                    id="users_models.W013",
                ),
            )

    except ImportError:
        errors.append(
            Warning(
                "CollaborationSettings model not found",
                hint="Collaboration preferences improve user experience",
                id="users_models.W014",
            ),
        )

    return errors


def _check_user_data_indexes() -> list:
    """Check for proper indexing on user-related fields."""
    errors = []

    # Check models across user-related apps
    user_related_apps = ["authentication", "profiles", "activity", "feedback", "notes"]

    for app_name in user_related_apps:
        try:
            app_config = apps.get_app_config(app_name)
            for model in app_config.get_models():
                model_fields = {field.name for field in model._meta.get_fields()}

                # Check user field indexing
                if "user" in model_fields:
                    user_field = model._meta.get_field("user")
                    if not user_field.db_index:
                        errors.append(
                            Warning(
                                f"{model.__name__}.user field not indexed",
                                hint="Add db_index=True to user foreign key fields",
                                id="users_models.W015",
                            ),
                        )

                # Check for timestamp indexing in user content
                timestamp_fields = [
                    "created_at",
                    "updated_at",
                    "timestamp",
                    "last_activity",
                ]
                for field_name in timestamp_fields:
                    if field_name in model_fields:
                        field = model._meta.get_field(field_name)
                        if not field.db_index and not any(field_name in idx.fields for idx in model._meta.indexes):
                            errors.append(
                                Info(
                                    f"{model.__name__}.{field_name} field could benefit from indexing",
                                    hint="Consider adding index for better query performance",
                                    id="users_models.I006",
                                ),
                            )
                            break  # Only report once per model

        except LookupError:
            pass  # App not found, skip

    return errors
