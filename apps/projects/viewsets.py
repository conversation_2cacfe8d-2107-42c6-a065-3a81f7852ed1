"""Project Management API ViewSets for Projects App

from requests.exceptions import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ConnectionError, TimeoutError
from celery.decorators import task
from datetime import date
from datetime import timedelta
from django.contrib.auth import get_user_model
User = get_user_model()
from django.db import models
from django.db.models import Prefetch
from django.db.models import Q
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import authentication
from rest_framework import permissions
from rest_framework import serializers
from rest_framework import status
from rest_framework import viewsets
from rest_framework.response import Response
from typing import List
import logging
import logging
logger = logging.getLogger(__name__)
import time

This module provides comprehensive REST API endpoints for projects, tasks, and related models
with organization-based data isolation and role-based access control.

ViewSets:
    - ProjectViewSet: Comprehensive project management API
    - TaskViewSet: Task management with dependencies and time tracking
    - PersonViewSet: Person/stakeholder management API
    - ProjectTemplateViewSet: Project template management
    - MeetingViewSet: Meeting scheduling and management
"""

import logging

from django.contrib.auth import get_user_model
from django.db.models import Prefetch, Q
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from django_filters.rest_framework import DjangoFilterBackend
from drf_spectacular.utils import OpenApiParameter, extend_schema, extend_schema_view
from rest_framework import filters, status, viewsets
from rest_framework.decorators import action
from rest_framework.response import Response

from apps.analytics.models import Activity
from apps.api.pagination import StandardResultsSetPagination
from apps.api.permissions import IsAuthenticatedOr401
from apps.authentication.models import UserRole

from .models import Meeting, Person, Project, ProjectMember, ProjectTemplate, Task
from .serializers import (
    AddProjectMemberSerializer,
    BulkTaskUpdateSerializer,
    MeetingSerializer,
    PersonCreateSerializer,
    PersonSerializer,
    PersonUpdateSerializer,
    ProjectCreateSerializer,
    ProjectDetailSerializer,
    ProjectListSerializer,
    ProjectMemberSerializer,
    ProjectStatusUpdateSerializer,
    ProjectTemplateSerializer,
    ProjectUpdateSerializer,
    TaskCreateSerializer,
    TaskSerializer,
    TaskUpdateSerializer,
    UpdateProjectMemberRoleSerializer,
)

User = get_user_model()
logger = logging.getLogger(__name__)


class OrganizationAccessViewSetMixin:
    """Mixin to enforce organization-based access control for ViewSets."""

    def get_user_organization(self):
        """Get the current user's organization."""
        user = self.request.user
        if hasattr(user, "organization") and user.organization:
            return user.organization
        return None

    def filter_by_organization(self, queryset):
        """Filter queryset by user's organization."""
        organization = self.get_user_organization()
        if not organization:
            return queryset.none()
        return queryset.filter(organization=organization)

    def check_organization_access(self, obj):
        """Check if user has access to object's organization."""
        organization = self.get_user_organization()
        if not organization:
            return False

        if hasattr(obj, "organization"):
            return obj.organization == organization
        if hasattr(obj, "project") and hasattr(obj.project, "organization"):
            return obj.project.organization == organization

        return False


@extend_schema_view(
    list=extend_schema(
        summary="List projects",
        description="Get a paginated list of projects accessible to the current user",
        parameters=[
            OpenApiParameter(name="status", description="Filter by project status"),
            OpenApiParameter(
                name="search",
                description="Search projects by name, client, or description",
            ),
            OpenApiParameter(name="ordering", description="Order results by field"),
        ],
    ),
    create=extend_schema(
        summary="Create project",
        description="Create a new project in the user's organization",
    ),
    retrieve=extend_schema(
        summary="Get project details",
        description="Get detailed information about a specific project",
    ),
    update=extend_schema(summary="Update project", description="Update project information"),
    partial_update=extend_schema(
        summary="Partially update project",
        description="Partially update project information",
    ),
    destroy=extend_schema(
        summary="Delete project",
        description="Delete a project (requires appropriate permissions)",
    ),
)
class ProjectViewSet(OrganizationAccessViewSetMixin, viewsets.ModelViewSet):
    """ViewSet for comprehensive project management.

    Provides CRUD operations for projects with team management,
    status updates, activity tracking, and organization isolation.
    """

    permission_classes = [IsAuthenticatedOr401]
    pagination_class = StandardResultsSetPagination
    filter_backends = [
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    ]
    filterset_fields = ["status", "client", "project_priority", "utility_coordinator"]
    search_fields = ["name", "client", "description", "client_job_number"]
    ordering_fields = ["created_at", "updated_at", "start_date", "end_date", "name"]
    ordering = ["-created_at"]

    def get_queryset(self):
        """Get projects accessible to the current user with optimizations."""
        organization = self.get_user_organization()
        if not organization:
            return Project.objects.none()

        # Base queryset with optimization
        queryset = (
            Project.objects.select_related(
                "organization",
                "created_by",
                "utility_coordinator",
            )
            .prefetch_related(
                Prefetch("members", queryset=ProjectMember.objects.select_related("user")),
                "tasks",
            )
            .filter(organization=organization)
        )

        # Apply status filters
        status_param = self.request.query_params.get("status")
        if status_param == "active":
            queryset = queryset.filter(status__in=["active", "planning", "in_progress"])
        elif status_param == "completed":
            queryset = queryset.filter(status="completed")
        elif status_param == "my_projects":
            user = self.request.user
            queryset = queryset.filter(
                Q(created_by=user) | Q(members__user=user, members__is_active=True) | Q(utility_coordinator=user),
            ).distinct()

        # Apply role-based filtering
        user_role = self._get_user_role()
        if user_role and user_role.role.level > 50:  # Utility Coordinator level or lower
            # Limit access to projects where user is involved
            user = self.request.user
            queryset = queryset.filter(
                Q(created_by=user) | Q(members__user=user, members__is_active=True) | Q(utility_coordinator=user),
            ).distinct()

        return queryset

    def get_serializer_class(self):
        """Use different serializers for different actions."""
        if self.action == "list":
            return ProjectListSerializer
        if self.action == "create":
            return ProjectCreateSerializer
        if self.action in ["update", "partial_update"]:
            return ProjectUpdateSerializer
        if self.action == "retrieve":
            return ProjectDetailSerializer
        return ProjectDetailSerializer

    def perform_create(self, serializer):
        """Create project with organization and creator information."""
        organization = self.get_user_organization()
        if not organization:
            raise PermissionError(_("No organization access"))

        project = serializer.save(organization=organization, created_by=self.request.user)

        # Add creator as project manager
        ProjectMember.objects.create(project=project, user=self.request.user, role="manager", is_active=True)

        # Create activity log
        Activity.objects.create(
            user=self.request.user,
            action="created",
            target_model="Project",
            target_id=project.id,
            target_name=project.name,
            description=f"Created project: {project.name}",
        )

    def perform_update(self, serializer):
        """Update project with activity logging."""
        old_values = {}
        new_values = {}

        # Track changes
        for field in serializer.validated_data:
            old_values[field] = getattr(serializer.instance, field, None)
            new_values[field] = serializer.validated_data[field]

        project = serializer.save()

        # Create activity log
        if serializer.validated_data:
            changes = ", ".join(serializer.validated_data.keys())
            Activity.objects.create(
                user=self.request.user,
                action="updated",
                target_model="Project",
                target_id=project.id,
                target_name=project.name,
                description=f"Updated project fields: {changes}",
            )

    def perform_destroy(self, instance):
        """Delete project with activity logging."""
        # Create activity log before deletion
        Activity.objects.create(
            user=self.request.user,
            action="deleted",
            target_model="Project",
            target_id=instance.id,
            target_name=instance.name,
            description=f"Deleted project: {instance.name}",
        )

        super().perform_destroy(instance)

    def _get_user_role(self) -> UserRole | None:
        """Get user's role in their organization."""
        organization = self.get_user_organization()
        if not organization:
            return None

        return UserRole.objects.filter(user=self.request.user, organization=organization).select_related("role").first()

    def _check_project_permission(self, project, permission_level: str) -> bool:
        """Check if user has specific permission level for project."""
        user = self.request.user

        # Check if user is project creator
        if project.created_by == user:
            return True

        # Check if user is utility coordinator
        if project.utility_coordinator == user:
            return True

        # Check project membership
        membership = ProjectMember.objects.filter(project=project, user=user, is_active=True).first()

        if membership and (
            permission_level == "view"
            or (permission_level == "edit" and membership.role in ["manager", "coordinator"])
            or (permission_level == "manage" and membership.role == "manager")
        ):
            return True

        # Check organization role
        user_role = self._get_user_role()
        return bool(
            user_role
            and (
                (permission_level == "view" and user_role.role.level <= 100)
                or (permission_level == "edit" and user_role.role.level <= 50)
                or (permission_level == "manage" and user_role.role.level <= 30)
            )
        )

    @extend_schema(
        request=AddProjectMemberSerializer,
        responses={200: ProjectMemberSerializer},
        summary="Add project member",
        description="Add a new member to the project team",
    )
    @action(detail=True, methods=["post"], url_path="members/add")
    def add_member(self, request, pk=None):
        """Add a member to the project."""
        project = self.get_object()

        # Check permissions
        if not self._check_project_permission(project, "manage"):
            return Response(
                {"error": _("You do not have permission to manage project members")},
                status=status.HTTP_403_FORBIDDEN,
            )

        serializer = AddProjectMemberSerializer(data=request.data, context={"request": request})
        serializer.is_valid(raise_exception=True)

        try:
            user = User.objects.get(id=serializer.validated_data["user_id"])

            # Check if user belongs to same organization
            if not hasattr(user, "organization") or user.organization != project.organization:
                return Response(
                    {"error": _("User must belong to the same organization")},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Create or update membership
            membership, created = ProjectMember.objects.get_or_create(
                project=project,
                user=user,
                defaults={"role": serializer.validated_data["role"], "is_active": True},
            )

            if not created:
                membership.role = serializer.validated_data["role"]
                membership.is_active = True
                membership.save()

            # Create activity log
            Activity.objects.create(
                user=request.user,
                action="updated",
                target_model="ProjectMember",
                target_id=membership.id,
                target_name=f"{user.get_full_name()} - {project.name}",
                description=f"Added {user.get_full_name()} to project as {membership.role}",
            )

            return Response(ProjectMemberSerializer(membership).data, status=status.HTTP_200_OK)

        except User.DoesNotExist:
            return Response({"error": _("User not found")}, status=status.HTTP_404_NOT_FOUND)
        except (ConnectionError, TimeoutError, HTTPError) as e:
            logger.error(f"Error adding project member: {e}")
            return Response(
                {"error": _("Error adding project member")},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @extend_schema(
        summary="Remove project member",
        description="Remove a member from the project team",
    )
    @action(detail=True, methods=["delete"], url_path="members/(?P<user_id>[^/.]+)/remove")
    def remove_member(self, request, pk=None, user_id=None):
        """Remove a member from the project."""
        project = self.get_object()

        # Check permissions
        if not self._check_project_permission(project, "manage"):
            return Response(
                {"error": _("You do not have permission to manage project members")},
                status=status.HTTP_403_FORBIDDEN,
            )

        try:
            user = User.objects.get(id=user_id)
            membership = ProjectMember.objects.filter(project=project, user=user).first()

            if membership:
                membership.is_active = False
                membership.save()

                # Create activity log
                Activity.objects.create(
                    user=request.user,
                    action="updated",
                    target_model="ProjectMember",
                    target_id=membership.id,
                    target_name=f"{user.get_full_name()} - {project.name}",
                    description=f"Removed {user.get_full_name()} from project",
                )

                return Response(
                    {"message": _("Member removed successfully")},
                    status=status.HTTP_200_OK,
                )
            return Response(
                {"error": _("Member not found in project")},
                status=status.HTTP_404_NOT_FOUND,
            )

        except User.DoesNotExist:
            return Response({"error": _("User not found")}, status=status.HTTP_404_NOT_FOUND)

    @extend_schema(
        request=UpdateProjectMemberRoleSerializer,
        responses={200: ProjectMemberSerializer},
        summary="Update member role",
        description="Update a project member's role",
    )
    @action(detail=True, methods=["patch"], url_path="members/(?P<user_id>[^/.]+)/role")
    def update_member_role(self, request, pk=None, user_id=None):
        """Update a project member's role."""
        project = self.get_object()

        # Check permissions
        if not self._check_project_permission(project, "manage"):
            return Response(
                {"error": _("You do not have permission to manage project members")},
                status=status.HTTP_403_FORBIDDEN,
            )

        serializer = UpdateProjectMemberRoleSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        try:
            user = User.objects.get(id=user_id)
            membership = ProjectMember.objects.filter(project=project, user=user).first()

            if membership:
                old_role = membership.role
                membership.role = serializer.validated_data["role"]
                membership.save()

                # Create activity log
                Activity.objects.create(
                    user=request.user,
                    action="updated",
                    target_model="ProjectMember",
                    target_id=membership.id,
                    target_name=f"{user.get_full_name()} - {project.name}",
                    description=f"Changed {user.get_full_name()}'s role from {old_role} to {membership.role}",
                )

                return Response(ProjectMemberSerializer(membership).data, status=status.HTTP_200_OK)
            return Response(
                {"error": _("Member not found in project")},
                status=status.HTTP_404_NOT_FOUND,
            )

        except User.DoesNotExist:
            return Response({"error": _("User not found")}, status=status.HTTP_404_NOT_FOUND)

    @extend_schema(
        summary="Get project members",
        description="Get all active members of the project",
    )
    @action(detail=True, methods=["get"])
    def members(self, request, pk=None):
        """Get all project members."""
        project = self.get_object()

        # Check permissions
        if not self._check_project_permission(project, "view"):
            return Response(
                {"error": _("You do not have permission to view project members")},
                status=status.HTTP_403_FORBIDDEN,
            )

        members = ProjectMember.objects.filter(project=project, is_active=True).select_related("user")

        serializer = ProjectMemberSerializer(members, many=True)
        return Response(serializer.data)

    @extend_schema(
        request=ProjectStatusUpdateSerializer,
        responses={200: ProjectDetailSerializer},
        summary="Update project status",
        description="Update the project's status and add status notes",
    )
    @action(detail=True, methods=["post"], url_path="status")
    def update_status(self, request, pk=None):
        """Update project status."""
        project = self.get_object()

        # Check permissions
        if not self._check_project_permission(project, "edit"):
            return Response(
                {"error": _("You do not have permission to update project status")},
                status=status.HTTP_403_FORBIDDEN,
            )

        serializer = ProjectStatusUpdateSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        # Update status
        old_status = project.status
        project.status = serializer.validated_data["status"]
        project.status_update_date = timezone.now().date()
        project.notes = serializer.validated_data.get("status_notes", project.notes)
        project.save()

        # Create activity log
        Activity.objects.create(
            user=request.user,
            action="updated",
            target_model="Project",
            target_id=project.id,
            target_name=project.name,
            description=f"Project status changed from {old_status} to {project.status}",
        )

        return Response(ProjectDetailSerializer(project).data, status=status.HTTP_200_OK)

    @extend_schema(
        summary="Get project activity feed",
        description="Get paginated activity feed for the project",
    )
    @action(detail=True, methods=["get"])
    def activity_feed(self, request, pk=None):
        """Get project activity feed."""
        project = self.get_object()

        # Check permissions
        if not self._check_project_permission(project, "view"):
            return Response(
                {"error": _("You do not have permission to view project activity")},
                status=status.HTTP_403_FORBIDDEN,
            )

        activities = (
            Activity.objects.filter(
                Q(target_model="Project", target_id=project.id)
                | Q(
                    target_model="Task",
                    target_id__in=project.tasks.values_list("id", flat=True),
                )
                | Q(target_model="ProjectMember", target_name__icontains=project.name),
            )
            .select_related("user")
            .order_by("-timestamp")
        )

        # Pagination
        page = self.paginate_queryset(activities)
        if page is not None:
            activity_data = []
            for activity in page:
                activity_data.append(
                    {
                        "id": activity.id,
                        "user": (activity.user.get_full_name() if activity.user else "System"),
                        "action": activity.action,
                        "description": activity.description,
                        "timestamp": activity.timestamp,
                        "target_model": activity.target_model,
                        "target_name": activity.target_name,
                    },
                )
            return self.get_paginated_response(activity_data)

        activity_data = []
        for activity in activities:
            activity_data.append(
                {
                    "id": activity.id,
                    "user": (activity.user.get_full_name() if activity.user else "System"),
                    "action": activity.action,
                    "description": activity.description,
                    "timestamp": activity.timestamp,
                    "target_model": activity.target_model,
                    "target_name": activity.target_name,
                },
            )

        return Response(activity_data)

    @extend_schema(summary="Get project analytics", description="Get project analytics and metrics")
    @action(detail=True, methods=["get"])
    def analytics(self, request, pk=None):
        """Get project analytics."""
        project = self.get_object()

        # Check permissions
        if not self._check_project_permission(project, "view"):
            return Response(
                {"error": _("You do not have permission to view project analytics")},
                status=status.HTTP_403_FORBIDDEN,
            )

        # Calculate analytics
        tasks = project.tasks.all()

        analytics = {
            "task_statistics": {
                "total_tasks": tasks.count(),
                "completed_tasks": tasks.filter(status="completed").count(),
                "in_progress_tasks": tasks.filter(status="in_progress").count(),
                "pending_tasks": tasks.filter(status="pending").count(),
                "overdue_tasks": tasks.filter(
                    due_date__lt=timezone.now().date(),
                    status__in=["pending", "in_progress"],
                ).count(),
            },
            "team_statistics": {
                "total_members": project.members.filter(is_active=True).count(),
                "active_members": project.members.filter(
                    is_active=True,
                    user__last_login__gte=timezone.now() - timezone.timedelta(days=30),
                ).count(),
            },
            "timeline_statistics": {
                "start_date": project.start_date,
                "end_date": project.end_date,
                "duration_days": (
                    (project.end_date - project.start_date).days if project.start_date and project.end_date else None
                ),
                "days_remaining": ((project.end_date - timezone.now().date()).days if project.end_date else None),
            },
        }

        return Response(analytics)


@extend_schema_view(
    list=extend_schema(
        summary="List tasks",
        description="Get a paginated list of tasks accessible to the current user",
    ),
    create=extend_schema(summary="Create task", description="Create a new task in a project"),
    retrieve=extend_schema(
        summary="Get task details",
        description="Get detailed information about a specific task",
    ),
    update=extend_schema(summary="Update task", description="Update task information"),
    destroy=extend_schema(summary="Delete task", description="Delete a task"),
)
class TaskViewSet(OrganizationAccessViewSetMixin, viewsets.ModelViewSet):
    """ViewSet for comprehensive task management.

    Provides CRUD operations for tasks with assignment,
    dependencies, time tracking, and organization isolation.
    """

    permission_classes = [IsAuthenticatedOr401]
    pagination_class = StandardResultsSetPagination
    filter_backends = [
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    ]
    filterset_fields = ["project", "status", "priority", "assigned_to"]
    search_fields = ["title", "description", "notes"]
    ordering_fields = [
        "created_at",
        "due_date",
        "priority",
        "status",
        "progress_percentage",
    ]
    ordering = ["-created_at"]

    def get_queryset(self):
        """Get tasks accessible to the current user with optimizations."""
        organization = self.get_user_organization()
        if not organization:
            return Task.objects.none()

        # Base queryset with optimization
        queryset = (
            Task.objects.select_related(
                "project__organization",
                "assigned_to",
                "created_by",
            )
            .prefetch_related(
                "dependencies",
                "dependents",
            )
            .filter(project__organization=organization)
        )

        # Apply project filter
        project_id = self.request.query_params.get("project")
        if project_id:
            queryset = queryset.filter(project_id=project_id)

        # Apply assignment filter
        assigned = self.request.query_params.get("assigned")
        if assigned == "me":
            queryset = queryset.filter(assigned_to=self.request.user)
        elif assigned == "unassigned":
            queryset = queryset.filter(assigned_to__isnull=True)

        # Apply due date filter
        due = self.request.query_params.get("due")
        if due == "overdue":
            queryset = queryset.filter(
                due_date__lt=timezone.now().date(),
                status__in=["pending", "in_progress"],
            )
        elif due == "today":
            today = timezone.now().date()
            queryset = queryset.filter(due_date=today)
        elif due == "week":
            week_end = timezone.now().date() + timezone.timedelta(days=7)
            queryset = queryset.filter(due_date__lte=week_end)

        return queryset

    def get_serializer_class(self):
        """Use different serializers for different actions."""
        if self.action == "create":
            return TaskCreateSerializer
        if self.action in ["update", "partial_update"]:
            return TaskUpdateSerializer
        return TaskSerializer

    def perform_create(self, serializer):
        """Create task with organization validation and activity logging."""
        project = serializer.validated_data["project"]

        # Check organization access
        if not self.check_organization_access(project):
            raise PermissionError(_("No access to project's organization"))

        task = serializer.save(created_by=self.request.user)

        # Create activity log
        Activity.objects.create(
            user=self.request.user,
            action="created",
            target_model="Task",
            target_id=task.id,
            target_name=task.title,
            description=f"Created task: {task.title}",
        )

    def perform_update(self, serializer):
        """Update task with activity logging."""
        old_values = {}
        new_values = {}

        # Track changes
        for field in serializer.validated_data:
            old_values[field] = getattr(serializer.instance, field, None)
            new_values[field] = serializer.validated_data[field]

        task = serializer.save()

        # Create activity log
        if serializer.validated_data:
            changes = ", ".join(serializer.validated_data.keys())
            Activity.objects.create(
                user=self.request.user,
                action="updated",
                target_model="Task",
                target_id=task.id,
                target_name=task.title,
                description=f"Updated task fields: {changes}",
            )

    def perform_destroy(self, instance):
        """Delete task with activity logging."""
        # Create activity log before deletion
        Activity.objects.create(
            user=self.request.user,
            action="deleted",
            target_model="Task",
            target_id=instance.id,
            target_name=instance.title,
            description=f"Deleted task: {instance.title}",
        )

        super().perform_destroy(instance)

    @extend_schema(summary="Assign task", description="Assign task to a user")
    @action(detail=True, methods=["post"])
    def assign(self, request, pk=None):
        """Assign task to a user."""
        task = self.get_object()

        # Check organization access
        if not self.check_organization_access(task):
            return Response(
                {"error": _("No access to task's organization")},
                status=status.HTTP_403_FORBIDDEN,
            )

        user_id = request.data.get("user_id")
        if not user_id:
            return Response({"error": _("user_id is required")}, status=status.HTTP_400_BAD_REQUEST)

        try:
            user = User.objects.get(id=user_id)

            # Check if user belongs to same organization
            if not hasattr(user, "organization") or user.organization != task.project.organization:
                return Response(
                    {"error": _("User must belong to the same organization")},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            task.assigned_to = user
            task.save()

            # Create activity log
            Activity.objects.create(
                user=request.user,
                action="updated",
                target_model="Task",
                target_id=task.id,
                target_name=task.title,
                description=f"Assigned task to {user.get_full_name()}",
            )

            return Response(TaskSerializer(task).data, status=status.HTTP_200_OK)

        except User.DoesNotExist:
            return Response({"error": _("User not found")}, status=status.HTTP_404_NOT_FOUND)

    @extend_schema(
        request=BulkTaskUpdateSerializer,
        summary="Bulk update tasks",
        description="Update multiple tasks at once",
    )
    @action(detail=False, methods=["post"], url_path="bulk-update")
    def bulk_update(self, request):
        """Bulk update multiple tasks."""
        serializer = BulkTaskUpdateSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        task_ids = serializer.validated_data["task_ids"]
        organization = self.get_user_organization()

        if not organization:
            return Response(
                {"error": _("No organization access")},
                status=status.HTTP_403_FORBIDDEN,
            )

        tasks = Task.objects.filter(id__in=task_ids, project__organization=organization)

        if len(tasks) != len(task_ids):
            return Response(
                {"error": _("Some tasks not found or not accessible")},
                status=status.HTTP_404_NOT_FOUND,
            )

        # Apply updates
        update_fields = []
        updates = {}

        if "status" in serializer.validated_data:
            updates["status"] = serializer.validated_data["status"]
            update_fields.append("status")

        if "assigned_to" in serializer.validated_data:
            updates["assigned_to_id"] = serializer.validated_data["assigned_to"]
            update_fields.append("assigned_to")

        if "priority" in serializer.validated_data:
            updates["priority"] = serializer.validated_data["priority"]
            update_fields.append("priority")

        if updates:
            tasks.update(**updates)

            # Create activity logs
            for task in tasks:
                Activity.objects.create(
                    user=request.user,
                    action="updated",
                    target_model="Task",
                    target_id=task.id,
                    target_name=task.title,
                    description=f"Bulk updated task fields: {', '.join(update_fields)}",
                )

        return Response(
            {"message": _("%(count)d tasks updated successfully") % {"count": len(task_ids)}},
            status=status.HTTP_200_OK,
        )

    @extend_schema(
        summary="Get task dependencies",
        description="Get task dependencies and dependents",
    )
    @action(detail=True, methods=["get"])
    def dependencies(self, request, pk=None):
        """Get task dependencies."""
        task = self.get_object()

        # Check organization access
        if not self.check_organization_access(task):
            return Response(
                {"error": _("No access to task's organization")},
                status=status.HTTP_403_FORBIDDEN,
            )

        dependencies = {
            "depends_on": TaskSerializer(task.dependencies.select_related("project", "assigned_to"), many=True).data,
            "dependents": TaskSerializer(task.dependents.select_related("project", "assigned_to"), many=True).data,
        }

        return Response(dependencies)


@extend_schema_view(
    list=extend_schema(
        summary="List persons",
        description="Get a paginated list of persons/stakeholders accessible to the current user",
    ),
    create=extend_schema(summary="Create person", description="Create a new person/stakeholder"),
    retrieve=extend_schema(
        summary="Get person details",
        description="Get detailed information about a specific person",
    ),
    update=extend_schema(summary="Update person", description="Update person information"),
    destroy=extend_schema(summary="Delete person", description="Delete a person record"),
)
class PersonViewSet(OrganizationAccessViewSetMixin, viewsets.ModelViewSet):
    """ViewSet for person/stakeholder management.

    Provides CRUD operations for persons with organization isolation
    and project association management.
    """

    permission_classes = [IsAuthenticatedOr401]
    pagination_class = StandardResultsSetPagination
    filter_backends = [
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    ]
    filterset_fields = ["importance_level", "person_type", "is_active"]
    search_fields = ["name", "email", "phone", "job_title", "expertise"]
    ordering_fields = ["name", "created_at", "updated_at", "importance_level"]
    ordering = ["name"]

    def get_queryset(self):
        """Get persons accessible to the current user."""
        organization = self.get_user_organization()
        if not organization:
            return Person.objects.none()

        return Person.objects.select_related("organization").filter(organization=organization)

    def get_serializer_class(self):
        """Use different serializers for different actions."""
        if self.action == "create":
            return PersonCreateSerializer
        if self.action in ["update", "partial_update"]:
            return PersonUpdateSerializer
        return PersonSerializer

    def perform_create(self, serializer):
        """Create person with organization assignment."""
        organization = self.get_user_organization()
        if not organization:
            raise PermissionError(_("No organization access"))

        person = serializer.save(organization=organization)

        # Create activity log
        Activity.objects.create(
            user=self.request.user,
            action="created",
            target_model="Person",
            target_id=person.id,
            target_name=person.name,
            description=f"Created person: {person.name}",
        )

    def perform_update(self, serializer):
        """Update person with activity logging."""
        old_values = {}
        new_values = {}

        # Track changes
        for field in serializer.validated_data:
            old_values[field] = getattr(serializer.instance, field, None)
            new_values[field] = serializer.validated_data[field]

        person = serializer.save()

        # Create activity log
        if serializer.validated_data:
            changes = ", ".join(serializer.validated_data.keys())
            Activity.objects.create(
                user=self.request.user,
                action="updated",
                target_model="Person",
                target_id=person.id,
                target_name=person.name,
                description=f"Updated person fields: {changes}",
            )

    def perform_destroy(self, instance):
        """Delete person with activity logging."""
        # Create activity log before deletion
        Activity.objects.create(
            user=self.request.user,
            action="deleted",
            target_model="Person",
            target_id=instance.id,
            target_name=instance.name,
            description=f"Deleted person: {instance.name}",
        )

        super().perform_destroy(instance)


@extend_schema_view(
    list=extend_schema(summary="List project templates", description="Get available project templates"),
    create=extend_schema(summary="Create project template", description="Create a new project template"),
)
class ProjectTemplateViewSet(OrganizationAccessViewSetMixin, viewsets.ModelViewSet):
    """ViewSet for project template management."""

    permission_classes = [IsAuthenticatedOr401]
    serializer_class = ProjectTemplateSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ["is_active", "is_default"]
    search_fields = ["name", "description"]

    def get_queryset(self):
        """Get templates for user's organization."""
        organization = self.get_user_organization()

        if organization:
            # Show organization templates and global templates
            queryset = ProjectTemplate.objects.filter(Q(organization=organization) | Q(organization__isnull=True))
        else:
            # Only global templates if no organization
            queryset = ProjectTemplate.objects.filter(organization__isnull=True)

        return queryset.order_by("-is_default", "name")

    def perform_create(self, serializer):
        """Create template with organization assignment."""
        organization = self.get_user_organization()
        serializer.save(created_by=self.request.user, organization=organization)


@extend_schema_view(
    list=extend_schema(
        summary="List meetings",
        description="Get meetings accessible to the current user",
    ),
    create=extend_schema(summary="Create meeting", description="Create a new meeting"),
)
class MeetingViewSet(OrganizationAccessViewSetMixin, viewsets.ModelViewSet):
    """ViewSet for meeting management."""

    permission_classes = [IsAuthenticatedOr401]
    serializer_class = MeetingSerializer
    pagination_class = StandardResultsSetPagination
    filter_backends = [
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    ]
    filterset_fields = ["project", "meeting_type", "status"]
    search_fields = ["title", "description", "location"]
    ordering_fields = ["scheduled_time", "created_at"]
    ordering = ["scheduled_time"]

    def get_queryset(self):
        """Get meetings filtered by organization with optimized queries."""
        if getattr(self, "swagger_fake_view", False):
            return Meeting.objects.none()

        queryset = Meeting.objects.filter(organization=self.get_user_organization()).select_related(
            "project",
            "created_by",
        )

        # Filter by project if specified
        project_id = self.request.query_params.get("project")
        if project_id:
            try:
                project = Project.objects.get(id=project_id, organization=self.get_user_organization())
                queryset = queryset.filter(project=project)
            except (Project.DoesNotExist, ValueError):
                # Return empty queryset if project doesn't exist or invalid ID
                return Meeting.objects.none()

        return queryset

    def perform_create(self, serializer):
        """Create meeting with organization and creator."""
        serializer.save(organization=self.get_user_organization(), created_by=self.request.user)

        # Log activity
        # Assuming ProjectActivity is defined elsewhere or needs to be imported
        # from apps.analytics.models import ProjectActivity
        # ProjectActivity.objects.create(
        #     project=serializer.instance.project,
        #     user=self.request.user,
        #     action_type="meeting_created",
        #     description=f"Created meeting: {serializer.instance.title}",
        #     target_type="meeting",
        #     target_id=str(serializer.instance.id),
        # )

    @extend_schema(summary="Join meeting", description="Join a meeting as attendee")
    @action(detail=True, methods=["post"])
    def join(self, request, pk=None):
        """Join a meeting as attendee."""
        meeting = self.get_object()
        user = request.user

        if user in meeting.attendees.all():
            return Response(
                {"detail": "Already attending this meeting"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        meeting.attendees.add(user)

        # Log activity
        # Assuming ProjectActivity is defined elsewhere or needs to be imported
        # from apps.analytics.models import ProjectActivity
        # ProjectActivity.objects.create(
        #     project=meeting.project,
        #     user=user,
        #     action_type="meeting_joined",
        #     description=f"Joined meeting: {meeting.title}",
        #     target_type="meeting",
        #     target_id=str(meeting.id),
        # )

        return Response({"detail": "Successfully joined meeting"}, status=status.HTTP_200_OK)

    @extend_schema(summary="Leave meeting", description="Leave a meeting")
    @action(detail=True, methods=["post"])
    def leave(self, request, pk=None):
        """Leave a meeting."""
        meeting = self.get_object()
        user = request.user

        if user not in meeting.attendees.all():
            return Response(
                {"detail": "Not attending this meeting"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        meeting.attendees.remove(user)

        # Log activity
        # Assuming ProjectActivity is defined elsewhere or needs to be imported
        # from apps.analytics.models import ProjectActivity
        # ProjectActivity.objects.create(
        #     project=meeting.project,
        #     user=user,
        #     action_type="meeting_left",
        #     description=f"Left meeting: {meeting.title}",
        #     target_type="meeting",
        #     target_id=str(meeting.id),
        # )

        return Response({"detail": "Successfully left meeting"}, status=status.HTTP_200_OK)


# Alias for stakeholder management - stakeholders are represented by Person model
StakeholderViewSet = PersonViewSet
