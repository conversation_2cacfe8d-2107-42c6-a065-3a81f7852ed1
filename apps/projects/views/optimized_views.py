"""
Optimized Project Views with Django 5.2 Compliance
"""

import logging
from datetime import date

from django.contrib.auth import get_user_model
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.core.exceptions import PermissionDenied
from django.db import models
from django.db.models import Count, Q, Sum
from django.http import HttpRequest, HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, render
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.views.generic import DetailView, ListView, TemplateView
from rest_framework import authentication, permissions

from apps.common.mixins import RoleRequiredMixin

User = get_user_model()
logger = logging.getLogger(__name__)

# Additional imports
import re
from rest_framework import status
from rest_framework.response import Response
from typing import Any

from django.views.decorators.http import require_http_methods

from apps.authentication.models import Organization
from apps.projects.models import Project, ProjectActivity, Task

User = get_user_model()
logger = logging.getLogger(__name__)


def _get_user_organization(user: User) -> Organization:
    """Get user's organization with validation.

    Args:
    ----
        user: The authenticated user

    Returns:
    -------
        Organization: User's organization

    Raises:
    ------
        PermissionDenied: If user has no organization access

    """
    if hasattr(user, "organization") and user.organization:
        return user.organization

    # Try to get organization from user's memberships
    membership = getattr(user, "org_memberships", None)
    if membership and membership.filter(is_active=True).exists():
        return membership.filter(is_active=True).first().organization

    raise PermissionDenied(_("Organization membership required for project access"))


def _check_project_access(user: User, project: Project, required_access: str = "view") -> None:
    """Check if user has required access to project.

    Args:
    ----
        user: The authenticated user
        project: The project to check access for
        required_access: Required access level ('view', 'edit', 'manage')

    Raises:
    ------
        PermissionDenied: If user doesn't have required access

    """
    organization = _get_user_organization(user)

    # Check organization match
    if project.organization != organization:
        raise PermissionDenied(_("Project belongs to different organization"))

    # Check project access based on role and membership
    has_access = False

    # Check if user is project team member
    if project.team_members.filter(user=user, is_active=True).exists():
        member = project.team_members.filter(user=user, is_active=True).first()
        if (
            required_access == "view"
            or (required_access == "edit" and member.role in ["manager", "coordinator", "contributor"])
            or (required_access == "manage" and member.role in ["manager"])
        ):
            has_access = True

    # Check if user is project creator or utility coordinator
    if project.created_by == user or project.utility_coordinator == user:
        has_access = True

    # Check organization role level
    if hasattr(user, "org_memberships"):
        membership = user.org_memberships.filter(organization=organization, is_active=True).first()
        if membership and hasattr(membership, "role"):
            role_level = getattr(membership.role, "level", 100)
            if role_level <= 30:  # Manager level or higher
                has_access = True

    if not has_access:
        raise PermissionDenied(_("Insufficient project access permissions"))


class OptimizedProjectListView(LoginRequiredMixin, RoleRequiredMixin, ListView):
    """Optimized project list view with organization isolation and Django 5.2 compliance"""

    required_roles = ["stakeholder"]

    model = Project
    template_name = "projects/project_list_optimized.html"
    context_object_name = "projects"
    paginate_by = 20

    def dispatch(self, request: HttpRequest, *args: Any, **kwargs: Any) -> HttpResponse:
        """Check organization access before processing request"""
        try:
            self.organization = _get_user_organization(request.user)
            return super().dispatch(request, *args, **kwargs)
        except PermissionDenied as e:
            logger.warning(f"Organization access denied in project list: {e}")
            raise
        except Exception:
            logger.exception("Error in optimized project list dispatch")
            raise

    def get_queryset(self) -> models.QuerySet:
        """Get optimized queryset for project list with organization filtering"""
        try:
            # Use organization-aware custom manager
            queryset = Project.objects.for_user(self.request.user).for_organization(self.organization)

            # Apply optimizations
            queryset = queryset.with_statistics().with_basic_data()

            # Apply filters
            search_query = self.request.GET.get("search", "").strip()
            if search_query:
                queryset = queryset.search(search_query)

            status_filter = self.request.GET.get("status")
            if status_filter and status_filter != "all":
                queryset = queryset.by_status(status_filter)

            priority_filter = self.request.GET.get("priority")
            if priority_filter and priority_filter != "all":
                queryset = queryset.by_priority(priority_filter)

            # Apply sorting
            sort_by = self.request.GET.get("sort", "-updated_at")
            valid_sorts = [
                "-updated_at",
                "name",
                "-created_at",
                "status",
                "project_priority",
            ]
            if sort_by in valid_sorts:
                queryset = queryset.order_by(sort_by)

            return queryset

        except Exception:
            logger.exception("Error building optimized project queryset")
            return Project.objects.none()

    def get_context_data(self, **kwargs: Any) -> dict[str, Any]:
        """Add optimized project statistics and filter options"""
        context = super().get_context_data(**kwargs)

        try:
            # Add aggregated statistics with organization filtering
            user_projects = Project.objects.for_user(self.request.user).for_organization(self.organization)

            context.update(
                {
                    "organization": self.organization,
                    "project_stats": {
                        "total_projects": user_projects.count(),
                        "active_projects": user_projects.active_projects().count(),
                        "completed_projects": user_projects.completed_projects().count(),
                        "overdue_projects": user_projects.with_overdue_tasks().count(),
                    },
                    "filter_options": {
                        "status_choices": [
                            ("all", _("All Statuses")),
                            ("planning", _("Planning")),
                            ("in_progress", _("In Progress")),
                            ("on_hold", _("On Hold")),
                            ("completed", _("Completed")),
                            ("cancelled", _("Cancelled")),
                        ],
                        "priority_choices": [
                            ("all", _("All Priorities")),
                            ("low", _("Low")),
                            ("medium", _("Medium")),
                            ("high", _("High")),
                            ("urgent", _("Urgent")),
                        ],
                    },
                    "current_filters": {
                        "search": self.request.GET.get("search", ""),
                        "status": self.request.GET.get("status", "all"),
                        "priority": self.request.GET.get("priority", "all"),
                        "sort": self.request.GET.get("sort", "-updated_at"),
                    },
                },
            )

        except Exception:
            logger.exception("Error building optimized project list context")
            context["project_stats"] = {}

        return context


class OptimizedProjectDetailView(LoginRequiredMixin, RoleRequiredMixin, DetailView):
    """Optimized project detail view with comprehensive prefetching and organization validation"""

    required_roles = ["stakeholder"]

    model = Project
    template_name = "projects/project_detail_optimized.html"
    context_object_name = "project"

    def get_object(self, queryset: models.QuerySet | None = None) -> Project:
        """Get optimized project object with comprehensive prefetching"""
        try:
            project = get_object_or_404(
                Project.objects.select_related("organization", "created_by", "utility_coordinator").prefetch_related(
                    "team_members__user",
                    "team_members__role",
                    "tasks__assigned_to",
                    "person_projects__person",
                ),
                pk=self.kwargs["pk"],
            )

            # Check access permissions
            _check_project_access(self.request.user, project, "view")

            return project

        except PermissionDenied as e:
            logger.warning(f"Access denied to project {self.kwargs.get('pk')}: {e}")
            raise
        except Exception:
            logger.exception("Error getting optimized project detail")
            raise

    def get_context_data(self, **kwargs: Any) -> dict[str, Any]:
        """Add comprehensive project analytics and related data"""
        context = super().get_context_data(**kwargs)
        project = self.object

        try:
            # Get optimized task data
            tasks_qs = Task.objects.filter(project=project).select_related("assigned_to", "created_by")

            context.update(
                {
                    "organization": project.organization,
                    "recent_tasks": tasks_qs.active_tasks().order_by("-updated_at")[:10],
                    "overdue_tasks": tasks_qs.overdue_tasks(),
                    "high_priority_tasks": tasks_qs.high_priority_tasks(),
                    # Get activity feed with optimization
                    "recent_activities": ProjectActivity.objects.filter(project=project)
                    .select_related("user")
                    .order_by("-timestamp")[:15],
                    # Project statistics with proper handling
                    "project_stats": {
                        "total_tasks": tasks_qs.count(),
                        "completed_tasks": tasks_qs.completed_tasks().count(),
                        "active_tasks": tasks_qs.active_tasks().count(),
                        "overdue_tasks": tasks_qs.overdue_tasks().count(),
                        "team_members": project.team_members.filter(is_active=True).count(),
                        "total_hours": tasks_qs.aggregate(total=Sum("time_entries__hours"))["total"] or 0,
                    },
                    # User permissions
                    "user_can_edit": True,  # Will be determined by access check
                    "user_can_manage": True,  # Will be determined by access check
                },
            )

            # Calculate completion rate
            total_tasks = context["project_stats"]["total_tasks"]
            completed_tasks = context["project_stats"]["completed_tasks"]
            context["project_stats"]["completion_rate"] = (
                (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0
            )

        except Exception:
            logger.exception("Error building optimized project detail context")
            context["project_stats"] = {}

        return context


class OptimizedTaskListView(LoginRequiredMixin, RoleRequiredMixin, ListView):
    """Optimized task list view with organization isolation and advanced filtering"""

    required_roles = ["stakeholder"]

    model = Task
    template_name = "projects/task_list_optimized.html"
    context_object_name = "tasks"
    paginate_by = 25

    def dispatch(self, request: HttpRequest, *args: Any, **kwargs: Any) -> HttpResponse:
        """Check organization access before processing request"""
        try:
            self.organization = _get_user_organization(request.user)
            return super().dispatch(request, *args, **kwargs)
        except PermissionDenied as e:
            logger.warning(f"Organization access denied in task list: {e}")
            raise

    def get_queryset(self) -> models.QuerySet:
        """Get optimized queryset for tasks with organization filtering"""
        try:
            # Start with organization-aware base query
            queryset = (
                Task.objects.for_user(self.request.user)
                .filter(project__organization=self.organization)
                .with_time_tracking()
                .with_basic_data()
            )

            # Filter by project if specified
            project_id = self.request.GET.get("project")
            if project_id:
                try:
                    project = Project.objects.get(id=project_id, organization=self.organization)
                    _check_project_access(self.request.user, project, "view")
                    queryset = queryset.filter(project=project)
                except (Project.DoesNotExist, PermissionDenied):
                    queryset = queryset.none()

            # Apply filters
            status_filter = self.request.GET.get("status")
            if status_filter and status_filter != "all":
                if status_filter == "active":
                    queryset = queryset.active_tasks()
                elif status_filter == "overdue":
                    queryset = queryset.overdue_tasks()
                else:
                    queryset = queryset.by_status(status_filter)

            priority_filter = self.request.GET.get("priority")
            if priority_filter and priority_filter != "all":
                queryset = queryset.by_priority(priority_filter)

            assigned_to_filter = self.request.GET.get("assigned_to")
            if assigned_to_filter and assigned_to_filter != "all":
                queryset = queryset.filter(assigned_to_id=assigned_to_filter)

            # Search
            search_query = self.request.GET.get("search", "").strip()
            if search_query:
                queryset = queryset.search(search_query)

            # Apply sorting
            sort_by = self.request.GET.get("sort", "-updated_at")
            valid_sorts = [
                "-updated_at",
                "title",
                "-created_at",
                "status",
                "priority",
                "due_date",
            ]
            if sort_by in valid_sorts:
                queryset = queryset.order_by(sort_by)

            return queryset

        except Exception:
            logger.exception("Error building optimized task queryset")
            return Task.objects.none()

    def get_context_data(self, **kwargs: Any) -> dict[str, Any]:
        """Add task statistics and filter options"""
        context = super().get_context_data(**kwargs)

        try:
            # Add task statistics
            user_tasks = Task.objects.for_user(self.request.user).filter(project__organization=self.organization)

            context.update(
                {
                    "organization": self.organization,
                    "task_stats": {
                        "total_tasks": user_tasks.count(),
                        "active_tasks": user_tasks.active_tasks().count(),
                        "overdue_tasks": user_tasks.overdue_tasks().count(),
                        "high_priority_tasks": user_tasks.high_priority_tasks().count(),
                        "assigned_to_user": user_tasks.assigned_to_user(self.request.user).count(),
                    },
                    # Filter options
                    "filter_projects": Project.objects.for_user(self.request.user)
                    .for_organization(self.organization)
                    .order_by("name"),
                    "filter_users": User.objects.filter(
                        assigned_tasks__project__organization=self.organization,
                        assigned_tasks__project__team_members__user=self.request.user,
                    )
                    .distinct()
                    .order_by("first_name", "last_name"),
                    "current_filters": {
                        "search": self.request.GET.get("search", ""),
                        "status": self.request.GET.get("status", "all"),
                        "priority": self.request.GET.get("priority", "all"),
                        "project": self.request.GET.get("project", "all"),
                        "assigned_to": self.request.GET.get("assigned_to", "all"),
                        "sort": self.request.GET.get("sort", "-updated_at"),
                    },
                },
            )

        except Exception:
            logger.exception("Error building optimized task list context")
            context["task_stats"] = {}

        return context


class OptimizedDashboardView(LoginRequiredMixin, RoleRequiredMixin, TemplateView):
    """Optimized dashboard view with organization isolation and minimal queries"""

    required_roles = ["department-manager"]

    template_name = "projects/dashboard_optimized.html"

    def dispatch(self, request: HttpRequest, *args: Any, **kwargs: Any) -> HttpResponse:
        """Check organization access before processing request"""
        try:
            self.organization = _get_user_organization(request.user)
            return super().dispatch(request, *args, **kwargs)
        except PermissionDenied as e:
            logger.warning(f"Organization access denied in dashboard: {e}")
            raise

    def get_context_data(self, **kwargs: Any) -> dict[str, Any]:
        """Build optimized dashboard context with organization filtering"""
        context = super().get_context_data(**kwargs)
        user = self.request.user

        try:
            # Get dashboard data with organization filtering
            user_projects = Project.objects.for_user(user).for_organization(self.organization)
            user_tasks = Task.objects.for_user(user).filter(project__organization=self.organization)

            context.update(
                {
                    "organization": self.organization,
                    # Recent data with optimization
                    "recent_projects": user_projects.with_basic_data().order_by("-updated_at")[:5],
                    "recent_tasks": user_tasks.with_basic_data().active_tasks().order_by("-updated_at")[:8],
                    "recent_activities": ProjectActivity.objects.filter(
                        project__organization=self.organization,
                        project__team_members__user=user,
                    )
                    .select_related("user", "project")
                    .order_by("-timestamp")[:10],
                    # Statistics with proper aggregation
                    "dashboard_stats": {
                        "total_projects": user_projects.count(),
                        "active_projects": user_projects.active_projects().count(),
                        "total_tasks": user_tasks.count(),
                        "active_tasks": user_tasks.active_tasks().count(),
                        "overdue_tasks": user_tasks.overdue_tasks().count(),
                        "completed_today": user_tasks.filter(
                            status="completed",
                            updated_at__date=timezone.now().date(),
                        ).count(),
                        "high_priority_tasks": user_tasks.high_priority_tasks().count(),
                    },
                    # Additional metrics
                    "user_role_level": self._get_user_role_level(user),
                },
            )

        except Exception:
            logger.exception("Error building optimized dashboard context")
            context["dashboard_stats"] = {}

        return context

    def _get_user_role_level(self, user: User) -> int:
        """Get user's role level in organization"""
        try:
            if hasattr(user, "org_memberships"):
                membership = user.org_memberships.filter(organization=self.organization, is_active=True).first()
                if membership and hasattr(membership, "role"):
                    return getattr(membership.role, "level", 100)
            return 100  # Default stakeholder level
        except (AttributeError, KeyError, ValueError, TypeError):
            return 100


# HTMX optimized views with Django 5.2 compliance and organization isolation


@login_required
@require_http_methods(["GET"])
def optimized_project_search(request: HttpRequest) -> JsonResponse:
    """Optimized HTMX project search endpoint with organization filtering"""
    try:
        organization = _get_user_organization(request.user)
        query = request.GET.get("q", "").strip()

        if len(query) < 2:
            return JsonResponse({"projects": []})

        # Optimized search query with organization filtering
        projects = (
            Project.objects.for_user(request.user).for_organization(organization).search(query).with_basic_data()[:10]
        )

        project_data = []
        for project in projects:
            project_data.append(
                {
                    "id": str(project.id),
                    "name": project.name,
                    "client": getattr(project, "client", ""),
                    "status": project.status,
                    "created_by": (project.created_by.get_full_name() if project.created_by else ""),
                    "team_size": project.team_members.filter(is_active=True).count(),
                    "task_count": project.tasks.count(),
                },
            )

        return JsonResponse({"projects": project_data, "query": query})

    except PermissionDenied as e:
        logger.warning(f"Permission denied in project search: {e}")
        return JsonResponse({"error": str(e)}, status=403)
    except Exception:
        logger.exception("Error in optimized project search")
        return JsonResponse({"error": _("Search error")}, status=500)


@login_required
@require_http_methods(["GET"])
def optimized_task_search(request: HttpRequest) -> JsonResponse:
    """Optimized HTMX task search endpoint with organization filtering"""
    try:
        organization = _get_user_organization(request.user)
        query = request.GET.get("q", "").strip()
        project_id = request.GET.get("project_id")

        if len(query) < 2:
            return JsonResponse({"tasks": []})

        # Build optimized search query
        tasks_qs = (
            Task.objects.for_user(request.user)
            .filter(project__organization=organization)
            .search(query)
            .with_basic_data()
        )

        if project_id:
            try:
                project = Project.objects.get(id=project_id, organization=organization)
                _check_project_access(request.user, project, "view")
                tasks_qs = tasks_qs.filter(project=project)
            except (Project.DoesNotExist, PermissionDenied):
                tasks_qs = tasks_qs.none()

        tasks = tasks_qs[:12]

        task_data = []
        for task in tasks:
            task_data.append(
                {
                    "id": str(task.id),
                    "title": task.title,
                    "status": task.status,
                    "priority": task.priority,
                    "assigned_to": (task.assigned_to.get_full_name() if task.assigned_to else ""),
                    "project": task.project.name,
                    "due_date": task.due_date.isoformat() if task.due_date else None,
                },
            )

        return JsonResponse({"tasks": task_data, "query": query})

    except PermissionDenied as e:
        logger.warning(f"Permission denied in task search: {e}")
        return JsonResponse({"error": str(e)}, status=403)
    except Exception:
        logger.exception("Error in optimized task search")
        return JsonResponse({"error": _("Search error")}, status=500)


@login_required
@require_http_methods(["GET"])
def optimized_activity_feed(request: HttpRequest) -> HttpResponse:
    """Optimized HTMX activity feed endpoint with organization filtering"""
    try:
        organization = _get_user_organization(request.user)
        limit = min(int(request.GET.get("limit", 20)), 50)  # Cap at 50
        offset = max(int(request.GET.get("offset", 0)), 0)

        # Get optimized activity feed with organization filtering
        activities = (
            ProjectActivity.objects.filter(
                project__organization=organization,
                project__team_members__user=request.user,
            )
            .select_related("user", "project")
            .order_by("-timestamp")[offset : offset + limit]
        )

        return render(
            request,
            "projects/partials/activity_feed.html",
            {
                "activities": activities,
                "organization": organization,
            },
        )

    except PermissionDenied as e:
        logger.warning(f"Permission denied in activity feed: {e}")
        return HttpResponse(f'<div class="alert alert-danger">{e}</div>', status=403)
    except Exception:
        logger.exception("Error in optimized activity feed")
        return HttpResponse(
            f'<div class="alert alert-danger">{_("Error loading activity feed")}</div>',
            status=500,
        )


@login_required
@require_http_methods(["GET"])
def optimized_project_stats(request: HttpRequest, project_id: str) -> JsonResponse:
    """Optimized HTMX project statistics endpoint with organization validation"""
    try:
        organization = _get_user_organization(request.user)

        # Get project with organization validation
        project = get_object_or_404(
            Project.objects.select_related("organization"),
            id=project_id,
            organization=organization,
        )

        # Check access permissions
        _check_project_access(request.user, project, "view")

        # Calculate statistics efficiently
        tasks_qs = Task.objects.filter(project=project)
        task_stats = tasks_qs.aggregate(
            total_tasks=Count("id"),
            completed_tasks=Count("id", filter=Q(status="completed")),
            total_hours=Sum("time_entries__hours") or 0,
        )

        stats = {
            "total_tasks": task_stats["total_tasks"],
            "completed_tasks": task_stats["completed_tasks"],
            "completion_rate": (
                (task_stats["completed_tasks"] / task_stats["total_tasks"] * 100)
                if task_stats["total_tasks"] > 0
                else 0
            ),
            "team_members": project.team_members.filter(is_active=True).count(),
            "total_hours": float(task_stats["total_hours"]),
            "active_tasks": tasks_qs.active_tasks().count(),
            "overdue_tasks": tasks_qs.overdue_tasks().count(),
        }

        return JsonResponse(stats)

    except PermissionDenied as e:
        logger.warning(f"Permission denied for project stats {project_id}: {e}")
        return JsonResponse({"error": str(e)}, status=403)
    except Exception:
        logger.exception("Error getting optimized project stats")
        return JsonResponse({"error": _("Statistics error")}, status=500)


@login_required
@require_http_methods(["GET"])
def optimized_kanban_board(request: HttpRequest, project_id: str) -> HttpResponse:
    """Optimized HTMX kanban board endpoint with organization validation"""
    try:
        organization = _get_user_organization(request.user)

        project = get_object_or_404(
            Project.objects.select_related("organization"),
            id=project_id,
            organization=organization,
        )

        # Check access permissions
        _check_project_access(request.user, project, "view")

        # Get optimized task data for kanban
        tasks = (
            Task.objects.filter(project=project)
            .select_related("assigned_to")
            .order_by("status", "priority", "due_date")
        )

        # Group tasks by status efficiently
        task_groups = {}
        status_choices = [
            ("pending", _("Pending")),
            ("in_progress", _("In Progress")),
            ("review", _("Review")),
            ("completed", _("Completed")),
        ]

        for status_code, status_label in status_choices:
            task_groups[status_code] = {
                "label": status_label,
                "tasks": [task for task in tasks if task.status == status_code],
            }

        return render(
            request,
            "projects/partials/kanban_board.html",
            {
                "project": project,
                "task_groups": task_groups,
                "organization": organization,
            },
        )

    except PermissionDenied as e:
        logger.warning(f"Permission denied for kanban board {project_id}: {e}")
        return HttpResponse(f'<div class="alert alert-danger">{e}</div>', status=403)
    except Exception:
        logger.exception("Error loading optimized kanban board")
        return HttpResponse(
            f'<div class="alert alert-danger">{_("Error loading kanban board")}</div>',
            status=500,
        )


@login_required
@require_http_methods(["GET"])
def optimized_team_workload(request: HttpRequest) -> HttpResponse:
    """Optimized HTMX team workload endpoint with organization filtering"""
    try:
        organization = _get_user_organization(request.user)

        # Get team members with their task counts (organization-aware)
        team_members = (
            User.objects.filter(
                assigned_tasks__project__organization=organization,
                assigned_tasks__project__team_members__user=request.user,
                assigned_tasks__project__team_members__is_active=True,
            )
            .annotate(
                active_task_count=Count(
                    "assigned_tasks",
                    filter=Q(
                        assigned_tasks__status__in=["pending", "in_progress"],
                        assigned_tasks__project__organization=organization,
                    ),
                ),
                overdue_task_count=Count(
                    "assigned_tasks",
                    filter=Q(
                        assigned_tasks__due_date__lt=timezone.now(),
                        assigned_tasks__status__in=["pending", "in_progress"],
                        assigned_tasks__project__organization=organization,
                    ),
                ),
                total_hours_logged=Sum(
                    "assigned_tasks__time_entries__hours",
                    filter=Q(assigned_tasks__project__organization=organization),
                )
                or 0,
            )
            .distinct()
            .order_by("first_name", "last_name")
        )

        return render(
            request,
            "projects/partials/team_workload.html",
            {
                "team_members": team_members,
                "organization": organization,
            },
        )

    except PermissionDenied as e:
        logger.warning(f"Permission denied for team workload: {e}")
        return HttpResponse(f'<div class="alert alert-danger">{e}</div>', status=403)
    except Exception:
        logger.exception("Error loading optimized team workload")
        return HttpResponse(
            f'<div class="alert alert-danger">{_("Error loading team workload")}</div>',
            status=500,
        )
