"""Stakeholder Management Views for Projects App

from requests.exceptions import HTT<PERSON><PERSON><PERSON>r, ConnectionError, TimeoutError
from datetime import date
from datetime import datetime
from datetime import timedelta
from django import forms
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.core.exceptions import PermissionDenied
from django.db import models
from django.db.models import Count
from django.db.models import Prefetch
from django.db.models import Q
from django.http import HttpResponse
from django.http import JsonResponse
from django.shortcuts import get_object_or_404
from django.shortcuts import redirect
from django.shortcuts import render
from django.urls import reverse
from django.urls import reverse_lazy
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from rest_framework import status
from rest_framework.response import Response
from typing import Any
import json
import logging
import logging
logger = logging.getLogger(__name__)
import re
import time

This module provides stakeholder/person management functionality using the modern Person model.
All views include organization-based data isolation and role-based access control.

Views:
    - StakeholderListView: Stakeholder directory with search and filtering
    - StakeholderDetailView: Detailed stakeholder profile with project associations
    - HTMX endpoints for dynamic interactions
    - Export functionality for stakeholder data
    - Analytics and reporting views
"""

import csv
import logging
from datetime import timedelta
from typing import Any

from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.core.exceptions import PermissionDenied
from django.core.paginator import Paginator
from django.db.models import Prefetch, Q
from django.http import HttpRequest, HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.urls import reverse, reverse_lazy
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.views.generic import CreateView, DetailView, ListView, UpdateView

from apps.analytics.models import Activity
from apps.common.decorators import requires_organization_role
from apps.common.mixins import OrganizationAccessMixin, RoleRequiredMixin
from apps.projects.forms import PersonForm
from apps.projects.models import Person, Project, ProjectMember

logger = logging.getLogger(__name__)


class StakeholderListView(LoginRequiredMixin, RoleRequiredMixin, OrganizationAccessMixin, ListView):
    """Stakeholder list view using Person model with comprehensive filtering and search.

    Features:
    - Organization-based data isolation
    - Advanced search and filtering
    - Pagination with configurable page size
    - Export functionality
    - Role-based access control
    """

    required_roles = ["stakeholder"]

    model = Person
    template_name = "projects/stakeholders/stakeholder_list.html"
    context_object_name = "stakeholders"
    paginate_by = 25

    def get_queryset(self):
        """Get optimized Person queryset with organization filtering and search."""
        organization = self.get_organization()
        if not organization:
            return Person.objects.none()

        # Base queryset with optimization
        queryset = (
            Person.objects.select_related("organization", "primary_project")
            .prefetch_related(
                Prefetch(
                    "projects",
                    queryset=Project.objects.select_related("organization").filter(organization=organization),
                ),
                "companies",
            )
            .filter(organization=organization)
        )

        # Apply search filter
        search_query = self.request.GET.get("search", "").strip()
        if search_query:
            queryset = queryset.filter(
                Q(name__icontains=search_query)
                | Q(email__icontains=search_query)
                | Q(phone__icontains=search_query)
                | Q(phone_secondary__icontains=search_query)
                | Q(job_title__icontains=search_query)
                | Q(expertise__icontains=search_query)
                | Q(notes__icontains=search_query),
            )

        # Apply importance filter
        importance_filter = self.request.GET.get("importance")
        if importance_filter and importance_filter in dict(Person.IMPORTANCE_CHOICES):
            queryset = queryset.filter(importance_level=importance_filter)

        # Apply job title filter
        job_title_filter = self.request.GET.get("job_title", "").strip()
        if job_title_filter:
            queryset = queryset.filter(job_title__icontains=job_title_filter)

        # Apply communication preference filter
        comm_pref_filter = self.request.GET.get("communication_preference")
        if comm_pref_filter and comm_pref_filter in dict(Person.COMMUNICATION_PREFERENCES):
            queryset = queryset.filter(communication_preference=comm_pref_filter)

        # Apply availability filter
        availability_filter = self.request.GET.get("availability")
        if availability_filter and availability_filter in dict(Person.AVAILABILITY_CHOICES):
            queryset = queryset.filter(availability=availability_filter)

        # Apply sorting
        sort_by = self.request.GET.get("sort", "name")
        if sort_by in [
            "name",
            "-name",
            "created_at",
            "-created_at",
            "importance_level",
            "-importance_level",
        ]:
            queryset = queryset.order_by(sort_by)
        else:
            queryset = queryset.order_by("name")

        return queryset

    def get_context_data(self, **kwargs) -> dict[str, Any]:
        """Add additional context for filtering and statistics."""
        context = super().get_context_data(**kwargs)
        organization = self.get_organization()

        if not organization:
            return context

        # Filter options
        context.update(
            {
                "importance_levels": Person.IMPORTANCE_CHOICES,
                "communication_preferences": Person.COMMUNICATION_PREFERENCES,
                "availability_choices": Person.AVAILABILITY_CHOICES,
                "job_titles": Person.objects.filter(organization=organization)
                .exclude(job_title__isnull=True)
                .exclude(job_title="")
                .values_list("job_title", flat=True)
                .distinct()[:20],
            },
        )

        # Current filter values
        context["current_filters"] = {
            "search": self.request.GET.get("search", ""),
            "importance": self.request.GET.get("importance", ""),
            "job_title": self.request.GET.get("job_title", ""),
            "communication_preference": self.request.GET.get("communication_preference", ""),
            "availability": self.request.GET.get("availability", ""),
            "sort": self.request.GET.get("sort", "name"),
        }

        # Statistics
        total_queryset = Person.objects.filter(organization=organization)
        context.update(
            {
                "total_stakeholders": total_queryset.count(),
                "vip_stakeholders": total_queryset.filter(importance_level="vip").count(),
                "high_importance": total_queryset.filter(importance_level="high").count(),
                "recent_stakeholders": total_queryset.filter(
                    created_at__gte=timezone.now() - timedelta(days=30),
                ).count(),
            },
        )

        # Page size options
        context["page_sizes"] = [10, 25, 50, 100]
        context["current_page_size"] = int(self.request.GET.get("page_size", self.paginate_by))

        return context

    def get_paginate_by(self, queryset):
        """Allow dynamic pagination size."""
        try:
            page_size = int(self.request.GET.get("page_size", self.paginate_by))
            if page_size in [10, 25, 50, 100]:
                return page_size
        except (ValueError, TypeError):
            pass
        return self.paginate_by


class StakeholderDetailView(LoginRequiredMixin, RoleRequiredMixin, OrganizationAccessMixin, DetailView):
    """Detailed stakeholder view with project associations and activity history.

    Features:
    - Comprehensive stakeholder profile
    - Project associations and roles
    - Communication history
    - Activity tracking
    - Performance metrics
    """

    required_roles = ["stakeholder"]

    model = Person
    template_name = "projects/stakeholders/stakeholder_detail.html"
    context_object_name = "stakeholder"

    def get_queryset(self):
        """Get optimized Person queryset with related data."""
        organization = self.get_organization()
        if not organization:
            return Person.objects.none()

        return (
            Person.objects.select_related(
                "organization",
                "primary_project",
            )
            .prefetch_related(
                Prefetch(
                    "projects",
                    queryset=Project.objects.select_related("organization").filter(organization=organization),
                ),
                "companies",
                Prefetch(
                    "project_memberships",
                    queryset=ProjectMember.objects.select_related("project", "user"),
                ),
            )
            .filter(organization=organization)
        )

    def get_context_data(self, **kwargs) -> dict[str, Any]:
        """Add comprehensive context for stakeholder detail."""
        context = super().get_context_data(**kwargs)
        stakeholder = self.object
        organization = self.get_organization()

        # Project statistics
        stakeholder_projects = stakeholder.projects.filter(organization=organization)
        context.update(
            {
                "project_count": stakeholder_projects.count(),
                "active_projects": stakeholder_projects.filter(status__in=["active", "planning", "in_progress"]),
                "completed_projects": stakeholder_projects.filter(status="completed"),
                "recent_projects": stakeholder_projects.filter(
                    created_at__gte=timezone.now() - timedelta(days=90),
                ).order_by("-created_at")[:5],
            },
        )

        # Communication and activity history
        context.update(
            {
                "recent_communications": Activity.objects.filter(
                    target_model="Communication",
                    description__icontains=stakeholder.name,
                ).order_by("-timestamp")[:10],
                "recent_activities": Activity.objects.filter(
                    Q(target_model="Person", target_id=stakeholder.id) | Q(description__icontains=stakeholder.name),
                ).order_by("-timestamp")[:20],
            },
        )

        # Project roles and memberships
        memberships = (
            ProjectMember.objects.filter(user__person=stakeholder, project__organization=organization)
            .select_related("project", "user")
            .order_by("-created_at")
        )

        context["project_memberships"] = memberships
        context["membership_roles"] = [
            {
                "project": membership.project,
                "role": membership.role,
                "date_joined": membership.created_at,
                "is_active": membership.is_active,
            }
            for membership in memberships
        ]

        # Performance metrics
        if stakeholder_projects.exists():
            context["performance_metrics"] = {
                "average_project_duration": self._calculate_average_project_duration(stakeholder_projects),
                "project_success_rate": self._calculate_project_success_rate(stakeholder_projects),
                "communication_frequency": self._calculate_communication_frequency(stakeholder),
            }

        return context

    def _calculate_average_project_duration(self, projects) -> int | None:
        """Calculate average project duration in days."""
        completed_projects = projects.filter(status="completed", end_date__isnull=False, start_date__isnull=False)

        if not completed_projects.exists():
            return None

        total_duration = sum([(project.end_date - project.start_date).days for project in completed_projects])

        return total_duration // completed_projects.count()

    def _calculate_project_success_rate(self, projects) -> float:
        """Calculate project success rate as percentage."""
        total_projects = projects.filter(status__in=["completed", "cancelled"]).count()
        if total_projects == 0:
            return 0.0

        successful_projects = projects.filter(status="completed").count()
        return round((successful_projects / total_projects) * 100, 1)

    def _calculate_communication_frequency(self, stakeholder) -> int:
        """Calculate average communications per month."""
        start_date = timezone.now() - timedelta(days=180)  # Last 6 months
        communication_count = Activity.objects.filter(
            target_model="Communication",
            timestamp__gte=start_date,
            description__icontains=stakeholder.name,
        ).count()

        return communication_count // 6  # Average per month


class StakeholderCreateView(LoginRequiredMixin, RoleRequiredMixin, OrganizationAccessMixin, CreateView):
    """Create new stakeholder with organization isolation."""

    required_roles = ["utility-coordinator"]

    model = Person
    form_class = PersonForm
    template_name = "projects/stakeholders/stakeholder_create.html"
    success_url = reverse_lazy("projects:stakeholder_list")

    def form_valid(self, form):
        """Set organization and create activity log."""
        form.instance.organization = self.get_organization()
        response = super().form_valid(form)

        # Create activity log
        Activity.objects.create(
            user=self.request.user,
            action="created",
            target_model="Person",
            target_id=self.object.id,
            target_name=self.object.name,
            description=f"Created new stakeholder: {self.object.name}",
        )

        messages.success(self.request, f'Stakeholder "{self.object.name}" created successfully.')
        return response


class StakeholderUpdateView(LoginRequiredMixin, RoleRequiredMixin, OrganizationAccessMixin, UpdateView):
    """Update stakeholder with organization validation."""

    required_roles = ["utility-coordinator"]

    model = Person
    form_class = PersonForm
    template_name = "projects/stakeholders/stakeholder_update.html"

    def get_queryset(self):
        """Ensure stakeholder belongs to user's organization."""
        organization = self.get_organization()
        if not organization:
            return Person.objects.none()
        return Person.objects.filter(organization=organization)

    def form_valid(self, form):
        """Create activity log for update."""
        response = super().form_valid(form)

        # Create activity log
        Activity.objects.create(
            user=self.request.user,
            action="updated",
            target_model="Person",
            target_id=self.object.id,
            target_name=self.object.name,
            description=f"Updated stakeholder information: {self.object.name}",
        )

        messages.success(self.request, f'Stakeholder "{self.object.name}" updated successfully.')
        return response

    def get_success_url(self):
        """Return to stakeholder detail page."""
        return reverse("projects:stakeholder_detail", kwargs={"pk": self.object.pk})


# HTMX Views for Dynamic Interactions


@login_required
@requires_organization_role(["executive", "department-manager", "utility-coordinator"])
def stakeholder_search_htmx(request: HttpRequest) -> HttpResponse:
    """HTMX endpoint for searching stakeholders with real-time results."""
    search_query = request.GET.get("query", "").strip()

    if not search_query or len(search_query) < 2:
        return render(
            request,
            "projects/stakeholders/partials/stakeholder_search_results.html",
            {"stakeholders": [], "query": search_query},
        )

    # Get user's organization
    organization = getattr(request.user, "organization", None)
    if not organization:
        return render(
            request,
            "projects/stakeholders/partials/stakeholder_search_results.html",
            {
                "stakeholders": [],
                "query": search_query,
                "error": _("No organization access"),
            },
        )

    # Perform search with optimization
    stakeholders = (
        Person.objects.filter(organization=organization)
        .filter(
            Q(name__icontains=search_query)
            | Q(email__icontains=search_query)
            | Q(phone__icontains=search_query)
            | Q(phone_secondary__icontains=search_query)
            | Q(job_title__icontains=search_query)
            | Q(expertise__icontains=search_query),
        )
        .select_related("organization")
        .order_by("name")[:20]
    )

    return render(
        request,
        "projects/stakeholders/partials/stakeholder_search_results.html",
        {"stakeholders": stakeholders, "query": search_query},
    )


@login_required
@requires_organization_role(["executive", "department-manager", "utility-coordinator"])
def stakeholder_filter_htmx(request: HttpRequest) -> HttpResponse:
    """HTMX endpoint for dynamic filtering of stakeholders."""
    organization = getattr(request.user, "organization", None)
    if not organization:
        return HttpResponse(_("No organization access"), status=403)

    # Base queryset with optimization
    queryset = Person.objects.filter(organization=organization).select_related("organization")

    # Apply filters
    filters = {}

    importance_filter = request.GET.get("importance")
    if importance_filter and importance_filter in dict(Person.IMPORTANCE_CHOICES):
        filters["importance_level"] = importance_filter

    job_title_filter = request.GET.get("job_title", "").strip()
    if job_title_filter:
        filters["job_title__icontains"] = job_title_filter

    comm_pref_filter = request.GET.get("communication_preference")
    if comm_pref_filter and comm_pref_filter in dict(Person.COMMUNICATION_PREFERENCES):
        filters["communication_preference"] = comm_pref_filter

    availability_filter = request.GET.get("availability")
    if availability_filter and availability_filter in dict(Person.AVAILABILITY_CHOICES):
        filters["availability"] = availability_filter

    # Apply all filters
    if filters:
        queryset = queryset.filter(**filters)

    # Apply search if provided
    search_query = request.GET.get("search", "").strip()
    if search_query:
        queryset = queryset.filter(
            Q(name__icontains=search_query)
            | Q(email__icontains=search_query)
            | Q(phone__icontains=search_query)
            | Q(job_title__icontains=search_query),
        )

    # Apply sorting
    sort_by = request.GET.get("sort", "name")
    if sort_by in ["name", "-name", "created_at", "-created_at"]:
        queryset = queryset.order_by(sort_by)
    else:
        queryset = queryset.order_by("name")

    # Pagination for HTMX
    paginator = Paginator(queryset, 25)
    page_number = request.GET.get("page", 1)
    page_obj = paginator.get_page(page_number)

    context = {
        "stakeholders": page_obj,
        "page_obj": page_obj,
        "current_filters": {
            "importance": importance_filter,
            "job_title": job_title_filter,
            "communication_preference": comm_pref_filter,
            "availability": availability_filter,
            "search": search_query,
            "sort": sort_by,
        },
    }

    return render(
        request,
        "projects/stakeholders/partials/stakeholder_filtered_list.html",
        context,
    )


@login_required
@requires_organization_role(["executive", "department-manager", "utility-coordinator"])
def stakeholder_quick_update_htmx(request: HttpRequest, stakeholder_id: int) -> HttpResponse:
    """HTMX endpoint for quick stakeholder updates."""
    organization = getattr(request.user, "organization", None)
    if not organization:
        return HttpResponse(_("No organization access"), status=403)

    user_orgs = request.user.user_roles.values_list("organization_id", flat=True)
    stakeholder = get_object_or_404(
        Person.objects.select_related("organization"),
        pk=stakeholder_id,
        organization__in=user_orgs,
    )

    if request.method == "POST":
        # Update specific fields
        updates = {}

        # Validate and update fields
        if "name" in request.POST:
            name = request.POST.get("name", "").strip()
            if name:
                updates["name"] = name

        if "email" in request.POST:
            email = request.POST.get("email", "").strip()
            if email:
                updates["email"] = email

        if "phone" in request.POST:
            phone = request.POST.get("phone", "").strip()
            if phone:
                updates["phone"] = phone

        if "job_title" in request.POST:
            updates["job_title"] = request.POST.get("job_title", "").strip()

        if "importance_level" in request.POST:
            importance = request.POST.get("importance_level")
            if importance in dict(Person.IMPORTANCE_CHOICES):
                updates["importance_level"] = importance

        if "notes" in request.POST:
            updates["notes"] = request.POST.get("notes", "").strip()

        # Apply updates
        if updates:
            for field, value in updates.items():
                setattr(stakeholder, field, value)

            try:
                stakeholder.full_clean()
                stakeholder.save(update_fields=[*list(updates.keys()), "updated_at"])

                # Create activity record
                Activity.objects.create(
                    user=request.user,
                    action="updated",
                    target_model="Person",
                    target_id=stakeholder.id,
                    target_name=stakeholder.name,
                    description=f"Quick update for {stakeholder.name}: {', '.join(updates.keys())}",
                )

                return render(
                    request,
                    "projects/stakeholders/partials/stakeholder_detail_content.html",
                    {"stakeholder": stakeholder, "success": True},
                )

            except (ConnectionError, TimeoutError, HTTPError) as e:
                logger.error(f"Error updating stakeholder {stakeholder.id}: {e}")
                return render(
                    request,
                    "projects/stakeholders/partials/stakeholder_edit_form.html",
                    {
                        "stakeholder": stakeholder,
                        "importance_levels": Person.IMPORTANCE_CHOICES,
                        "error": str(e),
                    },
                )

    # GET request - show form
    context = {
        "stakeholder": stakeholder,
        "importance_levels": Person.IMPORTANCE_CHOICES,
        "communication_preferences": Person.COMMUNICATION_PREFERENCES,
        "availability_choices": Person.AVAILABILITY_CHOICES,
    }

    return render(request, "projects/stakeholders/partials/stakeholder_edit_form.html", context)


@login_required
@requires_organization_role(["executive", "department-manager", "utility-coordinator"])
def stakeholder_projects_htmx(request: HttpRequest, stakeholder_id: int) -> HttpResponse:
    """HTMX endpoint for displaying stakeholder's project associations."""
    organization = getattr(request.user, "organization", None)
    if not organization:
        return HttpResponse(_("No organization access"), status=403)

    user_orgs = request.user.user_roles.values_list("organization_id", flat=True)
    stakeholder = get_object_or_404(
        Person.objects.select_related("organization"),
        pk=stakeholder_id,
        organization__in=user_orgs,
    )

    # Get stakeholder's projects with role information
    projects = (
        Project.objects.filter(organization=organization)
        .filter(Q(members__person=stakeholder) | Q(stakeholders=stakeholder) | Q(created_by__person=stakeholder))
        .distinct()
        .select_related("organization")
        .order_by("-created_at")
    )

    # Get project statistics and roles
    project_data = []

    for project in projects:
        # Determine stakeholder's role
        role = "Stakeholder"  # Default

        # Check project membership
        membership = ProjectMember.objects.filter(project=project, user__person=stakeholder).first()

        if membership:
            role = membership.role
        elif project.created_by and hasattr(project.created_by, "person") and project.created_by.person == stakeholder:
            role = "Project Owner"

        # Get communication count
        communication_count = (
            Activity.objects.filter(
                target_model="Communication",
                description__icontains=stakeholder.name,
            )
            .filter(Q(project=project) if hasattr(Activity, "project") else Q())
            .count()
        )

        # Get last communication
        last_communication = (
            Activity.objects.filter(
                target_model="Communication",
                description__icontains=stakeholder.name,
            )
            .filter(Q(project=project) if hasattr(Activity, "project") else Q())
            .order_by("-timestamp")
            .first()
        )

        project_data.append(
            {
                "project": project,
                "role": role,
                "communication_count": communication_count,
                "last_communication": last_communication,
                "is_active_member": membership.is_active if membership else False,
            },
        )

    context = {
        "stakeholder": stakeholder,
        "project_data": project_data,
        "total_projects": len(project_data),
        "active_projects": len([p for p in project_data if p["project"].status in ["active", "planning"]]),
    }

    return render(request, "projects/stakeholders/partials/stakeholder_projects.html", context)


@login_required
@requires_organization_role(["executive", "department-manager"])
def stakeholder_analytics_htmx(request: HttpRequest) -> HttpResponse:
    """HTMX endpoint for stakeholder analytics dashboard."""
    organization = getattr(request.user, "organization", None)
    if not organization:
        return HttpResponse(_("No organization access"), status=403)

    # Get time period
    period = request.GET.get("period", "30d")

    # Calculate date ranges
    if period == "7d":
        start_date = timezone.now() - timedelta(days=7)
        period_name = _("Last 7 Days")
        start_date - timedelta(days=7)
    elif period == "30d":
        start_date = timezone.now() - timedelta(days=30)
        period_name = _("Last 30 Days")
        start_date - timedelta(days=30)
    elif period == "90d":
        start_date = timezone.now() - timedelta(days=90)
        period_name = _("Last 90 Days")
        start_date - timedelta(days=90)
    else:
        start_date = timezone.now() - timedelta(days=30)
        period_name = _("Last 30 Days")
        start_date - timedelta(days=30)

    # Analytics calculations
    total_stakeholders = Person.objects.filter(organization=organization).count()
    new_stakeholders = Person.objects.filter(organization=organization, created_at__gte=start_date).count()

    # Importance distribution
    importance_distribution = {}
    for level, name in Person.IMPORTANCE_CHOICES:
        count = Person.objects.filter(organization=organization, importance_level=level).count()
        importance_distribution[name] = count

    # Communication preferences distribution
    comm_pref_distribution = {}
    for pref, name in Person.COMMUNICATION_PREFERENCES:
        count = Person.objects.filter(organization=organization, communication_preference=pref).count()
        comm_pref_distribution[name] = count

    # Most active stakeholders (by recent communications)
    stakeholder_activity = []
    recent_stakeholders = Person.objects.filter(organization=organization)[:50]

    for stakeholder in recent_stakeholders:
        comm_count = Activity.objects.filter(
            target_model="Communication",
            timestamp__gte=start_date,
            description__icontains=stakeholder.name,
        ).count()

        if comm_count > 0:
            stakeholder_activity.append({"stakeholder": stakeholder, "count": comm_count})

    # Sort by activity count and take top 10
    stakeholder_activity.sort(key=lambda x: x["count"], reverse=True)
    most_active = stakeholder_activity[:10]

    context = {
        "period_name": period_name,
        "current_period": period,
        "organization": organization,
        "metrics": {
            "total_stakeholders": total_stakeholders,
            "new_stakeholders": new_stakeholders,
            "vip_stakeholders": Person.objects.filter(organization=organization, importance_level="vip").count(),
            "high_importance": Person.objects.filter(organization=organization, importance_level="high").count(),
        },
        "importance_distribution": importance_distribution,
        "comm_pref_distribution": comm_pref_distribution,
        "most_active": most_active,
    }

    return render(request, "projects/stakeholders/partials/stakeholder_analytics.html", context)


# Export Functions


@login_required
@requires_organization_role(["executive", "department-manager"])
def stakeholder_export_csv(request: HttpRequest) -> HttpResponse:
    """Export stakeholders to CSV format with filtering support."""
    organization = getattr(request.user, "organization", None)
    if not organization:
        raise PermissionDenied(_("No organization access"))

    # Create CSV response
    response = HttpResponse(content_type="text/csv")
    response["Content-Disposition"] = (
        f'attachment; filename="stakeholders_{organization.slug}_{timezone.now().strftime("%Y%m%d")}.csv"'
    )

    writer = csv.writer(response)

    # Write header
    writer.writerow(
        [
            "ID",
            "Name",
            "Email",
            "Phone",
            "Phone Secondary",
            "Job Title",
            "Organization",
            "Importance Level",
            "Communication Preference",
            "Availability",
            "Address",
            "Address Secondary",
            "Expertise",
            "Certifications",
            "Notes",
            "Created At",
            "Updated At",
            "Project Count",
            "Active Project Count",
        ],
    )

    # Get stakeholders with filters
    stakeholders = (
        Person.objects.filter(organization=organization).select_related("organization").prefetch_related("projects")
    )

    # Apply filters if provided
    filters = {}

    importance_filter = request.GET.get("importance")
    if importance_filter and importance_filter in dict(Person.IMPORTANCE_CHOICES):
        filters["importance_level"] = importance_filter

    job_title_filter = request.GET.get("job_title", "").strip()
    if job_title_filter:
        filters["job_title__icontains"] = job_title_filter

    if filters:
        stakeholders = stakeholders.filter(**filters)

    # Write data
    for stakeholder in stakeholders:
        project_count = stakeholder.projects.filter(organization=organization).count()
        active_project_count = stakeholder.projects.filter(
            organization=organization,
            status__in=["active", "planning", "in_progress"],
        ).count()

        writer.writerow(
            [
                stakeholder.id,
                stakeholder.name,
                stakeholder.email or "",
                stakeholder.phone or "",
                stakeholder.phone_secondary or "",
                stakeholder.job_title or "",
                stakeholder.organization.name if stakeholder.organization else "",
                dict(Person.IMPORTANCE_CHOICES).get(stakeholder.importance_level, ""),
                dict(Person.COMMUNICATION_PREFERENCES).get(stakeholder.communication_preference, ""),
                dict(Person.AVAILABILITY_CHOICES).get(stakeholder.availability, ""),
                stakeholder.address or "",
                stakeholder.address_secondary or "",
                stakeholder.expertise or "",
                stakeholder.certifications or "",
                (stakeholder.notes.replace("\n", " ").replace("\r", "") if stakeholder.notes else ""),
                stakeholder.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                stakeholder.updated_at.strftime("%Y-%m-%d %H:%M:%S"),
                project_count,
                active_project_count,
            ],
        )

    return response


@login_required
@requires_organization_role(["executive", "department-manager"])
def stakeholder_export_json(request: HttpRequest) -> JsonResponse:
    """Export stakeholders to JSON format for API integration."""
    organization = getattr(request.user, "organization", None)
    if not organization:
        raise PermissionDenied(_("No organization access"))

    # Get stakeholders
    stakeholders = (
        Person.objects.filter(organization=organization).select_related("organization").prefetch_related("projects")
    )

    # Apply filters if provided
    filters = {}

    importance_filter = request.GET.get("importance")
    if importance_filter and importance_filter in dict(Person.IMPORTANCE_CHOICES):
        filters["importance_level"] = importance_filter

    if filters:
        stakeholders = stakeholders.filter(**filters)

    # Serialize data
    data = []
    for stakeholder in stakeholders:
        data.append(
            {
                "id": stakeholder.id,
                "name": stakeholder.name,
                "email": stakeholder.email,
                "phone": stakeholder.phone,
                "phone_secondary": stakeholder.phone_secondary,
                "job_title": stakeholder.job_title,
                "organization": (stakeholder.organization.name if stakeholder.organization else None),
                "importance_level": stakeholder.importance_level,
                "communication_preference": stakeholder.communication_preference,
                "availability": stakeholder.availability,
                "address": stakeholder.address,
                "address_secondary": stakeholder.address_secondary,
                "expertise": stakeholder.expertise,
                "certifications": stakeholder.certifications,
                "notes": stakeholder.notes,
                "created_at": stakeholder.created_at.isoformat(),
                "updated_at": stakeholder.updated_at.isoformat(),
                "project_count": stakeholder.projects.filter(organization=organization).count(),
            },
        )

    return JsonResponse(
        {
            "stakeholders": data,
            "total_count": len(data),
            "organization": organization.name,
            "exported_at": timezone.now().isoformat(),
        },
    )


# Legacy Compatibility Redirects


@login_required
def legacy_stakeholder_redirect(request: HttpRequest, stakeholder_id: int) -> HttpResponse:
    """Redirect legacy stakeholder URLs to person URLs."""
    try:
        # Add organization context to prevent IDOR vulnerability
        user_orgs = request.user.user_roles.values_list("organization_id", flat=True)
        person = Person.objects.get(id=stakeholder_id, organization_id__in=user_orgs)
        messages.info(request, _("Redirected from legacy stakeholder URL."))
        return redirect("projects:stakeholder_detail", pk=person.id)
    except Person.DoesNotExist:
        messages.error(request, _("Stakeholder not found."))
        return redirect("projects:stakeholder_list")


@login_required
def legacy_stakeholder_list_redirect(request: HttpRequest) -> HttpResponse:
    """Redirect legacy stakeholder list to modern stakeholder list."""
    messages.info(request, _("Redirected from legacy stakeholder list."))
    return redirect("projects:stakeholder_list")
