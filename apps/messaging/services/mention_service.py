"""
Advanced @mention detection and processing service for CLEAR HTMX platform.

This service provides comprehensive mention functionality including:
- Regex-based mention detection in text content
- User search and autocomplete for mentions
- Permission checking for private content mentions
- Notification creation for mentioned users
- Mention highlighting and rendering
- Analytics and tracking of mention usage
"""

import logging
import re
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Tuple

from django.contrib.auth import get_user_model
from django.contrib.contenttypes.models import ContentType
from django.core.cache import cache
from django.db import transaction
from django.db.models import Count, Q
from django.utils import timezone

from ..models import MessageMention, Notification

User = get_user_model()
logger = logging.getLogger(__name__)


@dataclass
class MentionMatch:
    """Data class for mention match information."""

    username: str
    start_pos: int
    end_pos: int
    mention_text: str
    user: Optional[User] = None
    is_valid: bool = False


@dataclass
class MentionProcessingResult:
    """Data class for mention processing results."""

    mentioned_users: List[User]
    created_notifications: List[Notification]
    created_mentions: List[MessageMention]
    invalid_mentions: List[str]
    blocked_mentions: List[Dict[str, Any]]
    permission_errors: List[Dict[str, Any]]
    total_mentions_found: int
    processing_time_ms: float


class MentionService:
    """Advanced service for handling @mention detection and processing."""

    # Enhanced regex pattern for mentions with various formats
    MENTION_PATTERNS = {
        "standard": r"@([a-zA-Z0-9_.-]+)",  # @username
        "quoted": r'@"([^"]+)"',  # @"First Last"
        "display_name": r"@\[([^\]]+)\]",  # @[Display Name]
        "email": r"@\(([^)]+@[^)]+)\)",  # @(<EMAIL>)
    }

    # Cache settings
    CACHE_TIMEOUT = 300  # 5 minutes
    CACHE_PREFIX = "mention_service"

    # Permission levels
    PERMISSION_LEVELS = {
        "public": 0,
        "organization": 1,
        "project": 2,
        "team": 3,
        "private": 4,
    }

    @classmethod
    def detect_mentions(cls, text: str, include_patterns: List[str] = None) -> List[MentionMatch]:
        """
        Detect all @mentions in text using multiple patterns.

        Args:
            text: Text to search for mentions
            include_patterns: List of pattern names to use (default: all)

        Returns:
            List of MentionMatch objects
        """
        if not text:
            return []

        patterns = include_patterns or list(cls.MENTION_PATTERNS.keys())
        mentions = []

        for pattern_name in patterns:
            if pattern_name not in cls.MENTION_PATTERNS:
                continue

            pattern = cls.MENTION_PATTERNS[pattern_name]
            matches = re.finditer(pattern, text, re.IGNORECASE)

            for match in matches:
                mention = MentionMatch(
                    username=match.group(1).strip(),
                    start_pos=match.start(),
                    end_pos=match.end(),
                    mention_text=match.group(0),
                )

                # Avoid duplicate mentions at same position
                if not any(m.start_pos == mention.start_pos for m in mentions):
                    mentions.append(mention)

        # Sort by position in text
        mentions.sort(key=lambda m: m.start_pos)
        return mentions

    @classmethod
    def resolve_mentioned_users(
        cls, mentions: List[MentionMatch], organization=None, context_object=None
    ) -> List[MentionMatch]:
        """
        Resolve mention matches to actual User objects.

        Args:
            mentions: List of MentionMatch objects
            organization: Organization to scope user search
            context_object: Context object for permission checking

        Returns:
            Updated list of MentionMatch objects with user data
        """
        if not mentions:
            return []

        # Extract usernames for batch lookup
        usernames = [m.username.lower() for m in mentions]

        # Build cache keys
        cache_key = f"{cls.CACHE_PREFIX}:users:{hash(tuple(sorted(usernames)))}"
        if organization:
            cache_key += f":org:{organization.id}"

        # Try cache first
        cached_users = cache.get(cache_key)
        if cached_users is not None:
            user_map = cached_users
        else:
            # Query users with optimized select
            user_query = (
                User.objects.filter(
                    Q(username__iregex=r"^(" + "|".join(re.escape(u) for u in usernames) + ")$")
                    | Q(email__iregex=r"^(" + "|".join(re.escape(u) for u in usernames) + ")$")
                    | Q(first_name__iregex=r"^(" + "|".join(re.escape(u) for u in usernames) + ")$")
                    | Q(last_name__iregex=r"^(" + "|".join(re.escape(u) for u in usernames) + ")$")
                )
                .select_related("profile")
                .prefetch_related("organizations")
            )

            # Filter by organization if provided
            if organization:
                user_query = user_query.filter(organizations=organization)

            # Filter by context permissions
            if context_object and hasattr(context_object, "organization"):
                user_query = user_query.filter(organizations=context_object.organization)

            # Create lookup map
            users = list(user_query)
            user_map = {}
            for user in users:
                user_map[user.username.lower()] = user
                user_map[user.email.lower()] = user
                if user.first_name:
                    user_map[user.first_name.lower()] = user
                if user.last_name:
                    user_map[user.last_name.lower()] = user
                if hasattr(user, "get_full_name"):
                    full_name = user.get_full_name()
                    if full_name:
                        user_map[full_name.lower()] = user

            # Cache the results
            cache.set(cache_key, user_map, cls.CACHE_TIMEOUT)

        # Match users to mentions
        for mention in mentions:
            username_key = mention.username.lower()
            if username_key in user_map:
                mention.user = user_map[username_key]
                mention.is_valid = True

        return mentions

    @classmethod
    def check_mention_permissions(
        cls,
        mentioned_user: User,
        content_object: Any,
        mentioning_user: User,
        permission_level: str = "organization",
    ) -> Tuple[bool, str]:
        """
        Check if a user can be mentioned in the given context.

        Args:
            mentioned_user: User being mentioned
            content_object: Object where mention occurs
            mentioning_user: User creating the mention
            permission_level: Required permission level

        Returns:
            Tuple of (allowed: bool, reason: str)
        """
        try:
            # Basic checks
            if not mentioned_user.is_active:
                return False, "User account is inactive"

            if mentioned_user == mentioning_user:
                return True, "Self-mention allowed"

            # Organization level check
            if permission_level in ["organization", "project", "team", "private"]:
                if hasattr(content_object, "organization") and hasattr(mentioned_user, "organizations"):
                    if not mentioned_user.organizations.filter(id=content_object.organization.id).exists():
                        return False, "User not in organization"
                elif hasattr(mentioning_user, "organizations") and hasattr(mentioned_user, "organizations"):
                    # Check if users share any organization
                    shared_orgs = mentioning_user.organizations.values_list(
                        "id", flat=True
                    ) & mentioned_user.organizations.values_list("id", flat=True)
                    if not shared_orgs:
                        return False, "Users not in same organization"

            # Project level check
            if permission_level in ["project", "team", "private"]:
                if hasattr(content_object, "project"):
                    project = content_object.project
                    if hasattr(project, "team_members"):
                        if not project.team_members.filter(id=mentioned_user.id).exists():
                            return False, "User not in project team"

            # Team level check
            if permission_level in ["team", "private"]:
                if hasattr(content_object, "team") or hasattr(content_object, "assigned_users"):
                    # Check if user is directly assigned or part of team
                    if hasattr(content_object, "assigned_users"):
                        if not content_object.assigned_users.filter(id=mentioned_user.id).exists():
                            return False, "User not assigned to this item"

            # Private level check (most restrictive)
            if permission_level == "private":
                # Only allow if user is directly involved
                if hasattr(content_object, "participants"):
                    if not content_object.participants.filter(id=mentioned_user.id).exists():
                        return False, "User not a participant"

            # Check user's mention preferences
            if hasattr(mentioned_user, "notification_settings"):
                settings = mentioned_user.notification_settings
                if not settings.notify_mentions:
                    return False, "User has disabled mention notifications"

                # Check if user is in quiet hours
                if settings.is_in_quiet_hours() and permission_level != "urgent":
                    return False, "User is in quiet hours"

            return True, "Mention allowed"

        except Exception as e:
            logger.exception(f"Error checking mention permissions: {e}")
            return False, f"Permission check failed: {str(e)}"

    @classmethod
    def create_mention_notifications(
        cls,
        mentions: List[MentionMatch],
        content_object: Any,
        mentioning_user: User,
        notification_title: str = None,
        notification_message: str = None,
        action_url: str = None,
        permission_level: str = "organization",
    ) -> MentionProcessingResult:
        """
        Create notifications and mention records for valid mentions.

        Args:
            mentions: List of resolved MentionMatch objects
            content_object: Object containing the mentions
            mentioning_user: User who created the mentions
            notification_title: Custom notification title
            notification_message: Custom notification message
            action_url: URL for notification action
            permission_level: Required permission level

        Returns:
            MentionProcessingResult with processing details
        """
        start_time = timezone.now()

        result = MentionProcessingResult(
            mentioned_users=[],
            created_notifications=[],
            created_mentions=[],
            invalid_mentions=[],
            blocked_mentions=[],
            permission_errors=[],
            total_mentions_found=len(mentions),
            processing_time_ms=0.0,
        )

        valid_mentions = [m for m in mentions if m.is_valid and m.user]

        # Get content type for generic relations
        content_type = ContentType.objects.get_for_model(content_object)

        with transaction.atomic():
            for mention in valid_mentions:
                mentioned_user = mention.user

                # Skip if mentioning self (unless explicitly allowed)
                if mentioned_user == mentioning_user:
                    continue

                # Check permissions
                allowed, reason = cls.check_mention_permissions(
                    mentioned_user, content_object, mentioning_user, permission_level
                )

                if not allowed:
                    result.blocked_mentions.append(
                        {
                            "username": mention.username,
                            "reason": reason,
                            "user_id": mentioned_user.id if mentioned_user else None,
                        }
                    )
                    continue

                try:
                    # Create mention record if applicable (for ChatMessage)
                    mention_record = None
                    if hasattr(content_object, "mentions"):  # ChatMessage model
                        mention_record, created = MessageMention.objects.get_or_create(
                            message=content_object,
                            mentioned_user=mentioned_user,
                            defaults={
                                "mention_text": mention.mention_text,
                                "start_position": mention.start_pos,
                                "end_position": mention.end_pos,
                            },
                        )
                        if created:
                            result.created_mentions.append(mention_record)

                    # Create notification
                    notification_title = notification_title or "You were mentioned"
                    notification_message = notification_message or (
                        f"{mentioning_user.get_full_name() or mentioning_user.username} "
                        f"mentioned you in {content_type.name}"
                    )

                    notification = Notification.create_notification(
                        recipient=mentioned_user,
                        sender=mentioning_user,
                        notification_type="mention",
                        title=notification_title,
                        message=notification_message,
                        action_url=action_url or "",
                        action_label="View",
                        content_object=content_object,
                        category="mention",
                        priority="normal",
                        respect_preferences=True,
                    )

                    if notification:
                        result.created_notifications.append(notification)
                        result.mentioned_users.append(mentioned_user)

                        logger.info(
                            f"Created mention notification: {mentioning_user.username} -> "
                            f"{mentioned_user.username} in {content_type.name}:{content_object.id}"
                        )

                except Exception as e:
                    logger.exception(f"Failed to create mention for {mentioned_user.username}: {e}")
                    result.permission_errors.append(
                        {
                            "username": mention.username,
                            "error": str(e),
                            "user_id": mentioned_user.id,
                        }
                    )

        # Track invalid mentions
        for mention in mentions:
            if not mention.is_valid:
                result.invalid_mentions.append(mention.username)

        # Calculate processing time
        end_time = timezone.now()
        result.processing_time_ms = (end_time - start_time).total_seconds() * 1000

        return result

    @classmethod
    def highlight_mentions_in_html(
        cls,
        text: str,
        mentions: List[MentionMatch] = None,
        highlight_class: str = "mention",
        invalid_class: str = "mention-invalid",
    ) -> str:
        """
        Replace mentions in text with HTML highlighting.

        Args:
            text: Original text containing mentions
            mentions: Pre-resolved mentions (if None, will detect automatically)
            highlight_class: CSS class for valid mentions
            invalid_class: CSS class for invalid mentions

        Returns:
            Text with mentions wrapped in HTML spans
        """
        if not text:
            return text

        if mentions is None:
            mentions = cls.detect_mentions(text)
            mentions = cls.resolve_mentioned_users(mentions)

        # Sort by position (reverse order to avoid position shifts)
        sorted_mentions = sorted(mentions, key=lambda m: m.start_pos, reverse=True)

        result_text = text
        for mention in sorted_mentions:
            if mention.is_valid and mention.user:
                # Valid mention with user link
                replacement = (
                    f'<span class="{highlight_class}" '
                    f'data-user-id="{mention.user.id}" '
                    f'data-username="{mention.user.username}" '
                    f'title="{mention.user.get_full_name() or mention.user.username}">'
                    f"{mention.mention_text}</span>"
                )
            else:
                # Invalid mention
                replacement = f'<span class="{invalid_class}" title="User not found">{mention.mention_text}</span>'

            result_text = result_text[: mention.start_pos] + replacement + result_text[mention.end_pos :]

        return result_text

    @classmethod
    def search_mentionable_users(
        cls,
        query: str,
        requesting_user: User,
        context_object: Any = None,
        limit: int = 10,
        include_offline: bool = True,
    ) -> List[Dict[str, Any]]:
        """
        Search for users that can be mentioned in autocomplete.

        Args:
            query: Search query string
            requesting_user: User performing the search
            context_object: Context for permission filtering
            limit: Maximum number of results
            include_offline: Whether to include offline users

        Returns:
            List of user data dictionaries for autocomplete
        """
        if not query or len(query) < 2:
            return []

        # Build search cache key
        cache_key = (
            f"{cls.CACHE_PREFIX}:search:{hash(query.lower())}:"
            f"user:{requesting_user.id}:limit:{limit}:offline:{include_offline}"
        )
        if context_object:
            content_type = ContentType.objects.get_for_model(context_object)
            cache_key += f":ctx:{content_type.id}:{context_object.id}"

        cached_results = cache.get(cache_key)
        if cached_results is not None:
            return cached_results

        # Build user query with search filters
        user_query = (
            User.objects.filter(
                Q(username__icontains=query)
                | Q(first_name__icontains=query)
                | Q(last_name__icontains=query)
                | Q(email__icontains=query)
            )
            .exclude(id=requesting_user.id)
            .select_related("profile")
        )

        # Filter by organization if available
        if hasattr(requesting_user, "organizations"):
            user_orgs = requesting_user.organizations.values_list("id", flat=True)
            user_query = user_query.filter(organizations__in=user_orgs)

        # Filter by context permissions
        if context_object:
            if hasattr(context_object, "organization"):
                user_query = user_query.filter(organizations=context_object.organization)
            elif hasattr(context_object, "project") and hasattr(context_object.project, "team_members"):
                user_query = user_query.filter(id__in=context_object.project.team_members.values_list("id", flat=True))

        # Filter by online status if needed
        if not include_offline:
            user_query = user_query.filter(last_seen__gte=timezone.now() - timezone.timedelta(minutes=15))

        # Order by relevance and limit
        user_query = user_query.annotate(mention_count=Count("mentions", distinct=True)).order_by(
            "-mention_count", "username"
        )[:limit]

        # Build result data
        results = []
        for user in user_query:
            profile = getattr(user, "profile", None)
            results.append(
                {
                    "id": user.id,
                    "username": user.username,
                    "email": user.email,
                    "first_name": user.first_name,
                    "last_name": user.last_name,
                    "full_name": (user.get_full_name() if hasattr(user, "get_full_name") else ""),
                    "avatar_url": (profile.avatar.url if profile and profile.avatar else None),
                    "avatar_color": (getattr(profile, "avatar_color", "#007cba") if profile else "#007cba"),
                    "job_title": getattr(profile, "job_title", "") if profile else "",
                    "department": getattr(profile, "department", "") if profile else "",
                    "is_online": getattr(user, "is_online", False),
                    "last_seen": (
                        user.last_seen.isoformat() if hasattr(user, "last_seen") and user.last_seen else None
                    ),
                    "mention_count": getattr(user, "mention_count", 0),
                    "is_verified": getattr(user, "is_verified", False),
                    "is_staff": user.is_staff,
                }
            )

        # Cache results
        cache.set(cache_key, results, cls.CACHE_TIMEOUT)
        return results

    @classmethod
    def extract_mentions_from_content(
        cls, content_object: Any, mentioning_user: User, content_field: str = "content"
    ) -> MentionProcessingResult:
        """
        Extract and process mentions from any content object.

        Args:
            content_object: Object containing mentionable content
            mentioning_user: User who created the content
            content_field: Field name containing the text content

        Returns:
            MentionProcessingResult with processing details
        """
        content_text = getattr(content_object, content_field, "")
        if not content_text:
            return MentionProcessingResult(
                mentioned_users=[],
                created_notifications=[],
                created_mentions=[],
                invalid_mentions=[],
                blocked_mentions=[],
                permission_errors=[],
                total_mentions_found=0,
                processing_time_ms=0.0,
            )

        # Detect mentions
        mentions = cls.detect_mentions(content_text)

        # Resolve users
        organization = getattr(content_object, "organization", None)
        mentions = cls.resolve_mentioned_users(mentions, organization, content_object)

        # Determine permission level based on content type
        permission_level = "organization"  # default
        if hasattr(content_object, "is_private") and content_object.is_private:
            permission_level = "private"
        elif hasattr(content_object, "project"):
            permission_level = "project"

        # Build action URL
        action_url = ""
        if hasattr(content_object, "get_absolute_url"):
            try:
                action_url = content_object.get_absolute_url()
            except (AttributeError, ValueError, TypeError) as e:
                logger.debug(f"Failed to get absolute URL for content object: {e}")
                pass

        # Process mentions
        return cls.create_mention_notifications(
            mentions=mentions,
            content_object=content_object,
            mentioning_user=mentioning_user,
            permission_level=permission_level,
            action_url=action_url,
        )

    @classmethod
    def get_mention_analytics(cls, user: User = None, organization: Any = None, days: int = 30) -> Dict[str, Any]:
        """
        Get mention analytics and statistics.

        Args:
            user: User to get analytics for (None for organization-wide)
            organization: Organization to scope analytics
            days: Number of days to include

        Returns:
            Dictionary of analytics data
        """
        from datetime import timedelta

        cutoff_date = timezone.now() - timedelta(days=days)

        # Base queryset for notifications
        notification_query = Notification.objects.filter(notification_type="mention", created_at__gte=cutoff_date)

        if user:
            notification_query = notification_query.filter(recipient=user)
        elif organization:
            notification_query = notification_query.filter(recipient__organizations=organization)

        # Base queryset for mention records
        mention_query = MessageMention.objects.filter(created_at__gte=cutoff_date)
        if user:
            mention_query = mention_query.filter(mentioned_user=user)
        elif organization:
            mention_query = mention_query.filter(mentioned_user__organizations=organization)

        analytics = {
            "period_days": days,
            "total_mentions": notification_query.count(),
            "unread_mentions": notification_query.filter(is_read=False).count(),
            "unique_mentioners": notification_query.values("sender").distinct().count(),
            "unique_mentioned": notification_query.values("recipient").distinct().count(),
            "mentions_by_day": [],
            "top_mentioners": [],
            "top_mentioned": [],
            "mention_types": {},
            "response_times": {},
        }

        # Mentions by day
        from django.db.models import Count

        daily_mentions = (
            notification_query.extra({"date": "date(created_at)"})
            .values("date")
            .annotate(count=Count("id"))
            .order_by("date")
        )

        analytics["mentions_by_day"] = [{"date": item["date"], "count": item["count"]} for item in daily_mentions]

        # Top mentioners
        top_mentioners = (
            notification_query.values("sender__username", "sender__first_name", "sender__last_name")
            .annotate(mention_count=Count("id"))
            .order_by("-mention_count")[:10]
        )

        analytics["top_mentioners"] = [
            {
                "username": item["sender__username"],
                "name": f"{item['sender__first_name']} {item['sender__last_name']}".strip(),
                "count": item["mention_count"],
            }
            for item in top_mentioners
        ]

        # Top mentioned users
        top_mentioned = (
            notification_query.values("recipient__username", "recipient__first_name", "recipient__last_name")
            .annotate(mention_count=Count("id"))
            .order_by("-mention_count")[:10]
        )

        analytics["top_mentioned"] = [
            {
                "username": item["recipient__username"],
                "name": f"{item['recipient__first_name']} {item['recipient__last_name']}".strip(),
                "count": item["mention_count"],
            }
            for item in top_mentioned
        ]

        return analytics

    @classmethod
    def cleanup_old_mentions(cls, days: int = 90) -> Dict[str, int]:
        """
        Clean up old mention records and notifications.

        Args:
            days: Age threshold in days

        Returns:
            Dictionary with cleanup statistics
        """
        from datetime import timedelta

        cutoff_date = timezone.now() - timedelta(days=days)

        # Clean up old mention notifications (keep read ones longer)
        old_notifications = Notification.objects.filter(
            notification_type="mention", created_at__lt=cutoff_date, is_read=True
        )
        notification_count = old_notifications.count()
        old_notifications.delete()

        # Clean up orphaned mention records
        orphaned_mentions = MessageMention.objects.filter(created_at__lt=cutoff_date, message__isnull=True)
        mention_count = orphaned_mentions.count()
        orphaned_mentions.delete()

        # Clear related caches
        cache_pattern = f"{cls.CACHE_PREFIX}:*"
        try:
            # This depends on cache backend supporting pattern deletion
            if hasattr(cache, "delete_pattern"):
                cache.delete_pattern(cache_pattern)
        except (AttributeError, NotImplementedError, Exception) as e:
            logger.debug(f"Failed to clear mention cache pattern {cache_pattern}: {e}")
            pass

        logger.info(f"Cleaned up {notification_count} old mention notifications and {mention_count} orphaned mentions")

        return {
            "deleted_notifications": notification_count,
            "deleted_mentions": mention_count,
            "cutoff_date": cutoff_date.isoformat(),
        }
