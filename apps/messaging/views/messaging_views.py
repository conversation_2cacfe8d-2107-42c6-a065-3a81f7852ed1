import json
import logging

try:
    from requests.exceptions import HTT<PERSON><PERSON>rror, ConnectionError, RequestException
except ImportError:
    # Fallback for development environments
    class HTTPError(Exception):
        pass
    class ConnectionError(Exception):
        pass
    class RequestException(Exception):
        pass

try:
    from urllib3.exceptions import TimeoutError
except ImportError:
    # Fallback for development environments
    class TimeoutError(Exception):
        pass

from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.core.exceptions import PermissionDenied
from django.core.paginator import Paginator
from django.db import transaction
from django.db.models import Count, Q
from django.http import Http404, HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.utils import timezone
from django.utils.html import escape
from django.views.decorators.http import require_http_methods
from django.views.decorators.vary import vary_on_headers
from django.views.generic import DetailView, ListView, TemplateView

import markdown

from apps.analytics.models import Activity
from apps.authentication.models import User
from apps.common.mixins import RoleRequiredMixin
from apps.documents.models import Document
from apps.messaging.forms.message_forms import ChatMessageForm
from apps.messaging.models import (
    ChatMessage,
    Comment,
    Conversation,
    ConversationMember,
    MessageMention,
    MessageReaction,
    MessageRead,
    MessageThread,
    Notification,
    WhisperMessage,
)
from apps.projects.models import Project

logger = logging.getLogger(__name__)

"""
Messaging Views - Updated with additional whisper and conversation views

This module contains views related to messaging, chat, and communication features.
Includes whisper messaging, conversation management, and chat interfaces.

"""

import logging
import re

logger = logging.getLogger(__name__)


@login_required
def messaging_interface(request):
    """Main messaging interface"""
    # Get user's conversations
    conversations = (
        Conversation.objects.filter(participants=request.user, is_active=True)
        .select_related("project", "created_by")
        .prefetch_related("participants")[:20]
    )

    # Get recent messages from active conversation or first conversation
    active_conversation_id = request.GET.get("conversation")
    active_conversation = None
    messages = []

    if active_conversation_id:
        try:
            active_conversation = conversations.get(id=active_conversation_id)
            messages = active_conversation.messages.select_related("user").order_by("created_at")[:50]
        except Conversation.DoesNotExist:
            pass
    elif conversations.exists():
        active_conversation = conversations.first()
        messages = active_conversation.messages.select_related("user").order_by("created_at")[:50]

    context = {
        "conversations": conversations,
        "active_conversation": active_conversation,
        "messages": messages,
        "user": request.user,
    }

    return render(request, "messaging/chat_interface.html", context)


@login_required
@require_http_methods(["GET"])
def conversation_search_htmx(request):
    """HTMX endpoint for searching conversations."""
    search_query = request.GET.get("q", "").strip()

    if not search_query:
        conversations = []
    else:
        conversations = Conversation.objects.filter(
            Q(name__icontains=search_query) | Q(description__icontains=search_query),
            members=request.user,
        ).distinct()[:10]

    return render(
        request,
        "messaging/partials/conversation_search_results.html",
        {
            "conversations": conversations,
            "search_query": search_query,
        },
    )


@login_required
@require_http_methods(["GET"])
def conversation_info_htmx(request, conversation_id):
    """HTMX endpoint for getting conversation information."""
    try:
        conversation = Conversation.objects.get(id=conversation_id, members=request.user)

        return render(
            request,
            "messaging/partials/conversation_info.html",
            {
                "conversation": conversation,
            },
        )
    except Conversation.DoesNotExist:
        raise Http404("Conversation not found")


@login_required
@require_http_methods(["GET"])
def conversation_settings_htmx(request, conversation_id):
    """HTMX endpoint for conversation settings."""
    try:
        conversation = Conversation.objects.get(id=conversation_id, participants=request.user)

        return render(
            request,
            "messaging/partials/conversation_settings.html",
            {
                "conversation": conversation,
            },
        )
    except Conversation.DoesNotExist:
        raise Http404("Conversation not found")


@login_required
@require_http_methods(["POST"])
def conversation_join_htmx(request, conversation_id):
    """HTMX endpoint for joining a conversation."""
    try:
        conversation = Conversation.objects.get(id=conversation_id)

        # Add user to conversation if not already a member
        if not conversation.members.filter(id=request.user.id).exists():
            conversation.members.add(request.user)

        return render(
            request,
            "messaging/partials/conversation_joined.html",
            {
                "conversation": conversation,
                "success": True,
            },
        )
    except Conversation.DoesNotExist:
        return render(
            request,
            "messaging/partials/conversation_joined.html",
            {
                "error": "Conversation not found",
                "success": False,
            },
        )


@login_required
@require_http_methods(["POST"])
def conversation_mute_htmx(request, conversation_id):
    """HTMX endpoint for muting/unmuting a conversation."""
    try:
        conversation = Conversation.objects.get(id=conversation_id, members=request.user)

        # Toggle mute status (this would need a muted field in the model)
        # For now, we'll just return success
        is_muted = request.POST.get("mute", "false").lower() == "true"

        return render(
            request,
            "messaging/partials/conversation_muted.html",
            {
                "conversation": conversation,
                "is_muted": is_muted,
                "success": True,
            },
        )
    except Conversation.DoesNotExist:
        return render(
            request,
            "messaging/partials/conversation_muted.html",
            {
                "error": "Conversation not found",
                "success": False,
            },
        )


@login_required
@require_http_methods(["GET"])
def conversation_insights_htmx(request, conversation_id):
    """HTMX endpoint for conversation-specific insights."""
    try:
        conversation = Conversation.objects.get(id=conversation_id, members=request.user)

        # Generate conversation insights (placeholder implementation)
        insights = {
            "message_count": 0,  # Would be calculated from conversation.messages.count()
            "active_members": 0,  # Would be calculated from active members
            "recent_activity": [],  # Would be recent messages/activity
        }

        return render(
            request,
            "messaging/partials/conversation_insights.html",
            {
                "conversation": conversation,
                "insights": insights,
            },
        )
    except Conversation.DoesNotExist:
        return render(
            request,
            "messaging/partials/conversation_insights.html",
            {
                "error": "Conversation not found",
            },
        )


@login_required
@require_http_methods(["GET"])
def message_detail_htmx(request, message_id):
    """HTMX endpoint for getting message details."""
    try:
        # This would need a proper Message model
        # For now, we'll create a placeholder
        message_data = {
            "id": message_id,
            "content": "Message content placeholder",
            "author": (request.user.username if request.user.is_authenticated else "Anonymous"),
            "timestamp": "2025-01-01 00:00:00",
        }

        return render(
            request,
            "messaging/partials/message_detail.html",
            {
                "message": message_data,
            },
        )
    except Exception as e:
        return render(
            request,
            "messaging/partials/message_detail.html",
            {
                "error": f"Message not found: {e!s}",
            },
        )


@login_required
@require_http_methods(["GET", "POST"])
def message_edit_htmx(request, message_id):
    """HTMX endpoint for editing messages."""
    try:
        if request.method == "GET":
            # Return edit form
            message_data = {
                "id": message_id,
                "content": "Message content placeholder",
                "author": (request.user.username if request.user.is_authenticated else "Anonymous"),
            }

            return render(
                request,
                "messaging/partials/message_edit_form.html",
                {
                    "message": message_data,
                },
            )

        if request.method == "POST":
            # Process edit
            new_content = request.POST.get("content", "")

            return render(
                request,
                "messaging/partials/message_updated.html",
                {
                    "message_id": message_id,
                    "content": new_content,
                    "success": True,
                },
            )

    except Exception as e:
        return render(
            request,
            "messaging/partials/message_edit_form.html",
            {
                "error": f"Error editing message: {e!s}",
            },
        )


@login_required
@require_http_methods(["GET", "POST"])
def message_reply_htmx(request, message_id):
    """HTMX endpoint for replying to messages."""
    try:
        if request.method == "GET":
            # Return reply form
            original_message = {
                "id": message_id,
                "content": "Original message content placeholder",
                "author": "Original Author",
            }

            return render(
                request,
                "messaging/partials/message_reply_form.html",
                {
                    "original_message": original_message,
                },
            )

        if request.method == "POST":
            # Process reply
            reply_content = request.POST.get("content", "")

            return render(
                request,
                "messaging/partials/message_reply_created.html",
                {
                    "original_message_id": message_id,
                    "reply_content": reply_content,
                    "author": (request.user.username if request.user.is_authenticated else "Anonymous"),
                    "success": True,
                },
            )

    except Exception as e:
        return render(
            request,
            "messaging/partials/message_reply_form.html",
            {
                "error": f"Error creating reply: {e!s}",
            },
        )


@login_required
@require_http_methods(["GET", "POST"])
def message_quote_htmx(request, message_id):
    """HTMX endpoint for quoting messages."""
    try:
        if request.method == "GET":
            # Return quote form with original message
            original_message = {
                "id": message_id,
                "content": "Original message content placeholder",
                "author": "Original Author",
                "timestamp": "2025-01-01 00:00:00",
            }

            return render(
                request,
                "messaging/partials/message_quote_form.html",
                {
                    "original_message": original_message,
                },
            )

        if request.method == "POST":
            # Process quote
            quote_content = request.POST.get("content", "")

            return render(
                request,
                "messaging/partials/message_quote_created.html",
                {
                    "quoted_message_id": message_id,
                    "quote_content": quote_content,
                    "author": (request.user.username if request.user.is_authenticated else "Anonymous"),
                    "success": True,
                },
            )

    except Exception as e:
        return render(
            request,
            "messaging/partials/message_quote_form.html",
            {
                "error": f"Error creating quote: {e!s}",
            },
        )


@login_required
@require_http_methods(["GET", "POST"])
def message_translate_htmx(request, message_id):
    """HTMX endpoint for translating messages."""
    try:
        if request.method == "GET":
            # Return translation options
            original_message = {
                "id": message_id,
                "content": "Original message content placeholder",
                "language": "en",
            }

            return render(
                request,
                "messaging/partials/message_translate_form.html",
                {
                    "original_message": original_message,
                    "available_languages": [
                        ("es", "Spanish"),
                        ("fr", "French"),
                        ("de", "German"),
                        ("it", "Italian"),
                        ("pt", "Portuguese"),
                    ],
                },
            )

        if request.method == "POST":
            # Process translation
            target_language = request.POST.get("target_language", "en")

            # Placeholder translation
            translated_content = f"[Translated to {target_language}] Original message content placeholder"

            return render(
                request,
                "messaging/partials/message_translated.html",
                {
                    "original_message_id": message_id,
                    "translated_content": translated_content,
                    "target_language": target_language,
                    "success": True,
                },
            )

    except Exception as e:
        return render(
            request,
            "messaging/partials/message_translate_form.html",
            {
                "error": f"Error translating message: {e!s}",
            },
        )


@login_required
@require_http_methods(["POST"])
def message_bookmark_htmx(request):
    """HTMX endpoint for bookmarking messages."""
    try:
        message_id = request.POST.get("message_id")
        action = request.POST.get("action", "add")  # add or remove

        if not message_id:
            return render(
                request,
                "messaging/partials/message_bookmark_result.html",
                {
                    "error": "Message ID is required",
                    "success": False,
                },
            )

        # Placeholder bookmark logic
        if action == "add":
            # Add bookmark
            result_message = "Message bookmarked successfully"
            is_bookmarked = True
        else:
            # Remove bookmark
            result_message = "Bookmark removed successfully"
            is_bookmarked = False

        return render(
            request,
            "messaging/partials/message_bookmark_result.html",
            {
                "message_id": message_id,
                "is_bookmarked": is_bookmarked,
                "message": result_message,
                "success": True,
            },
        )

    except Exception as e:
        return render(
            request,
            "messaging/partials/message_bookmark_result.html",
            {
                "error": f"Error bookmarking message: {e!s}",
                "success": False,
            },
        )


@login_required
@require_http_methods(["POST", "DELETE"])
def message_reaction_remove_htmx(request):
    """HTMX endpoint for removing message reactions."""
    try:
        message_id = request.POST.get("message_id") or request.GET.get("message_id")
        reaction_type = request.POST.get("reaction_type") or request.GET.get("reaction_type")

        if not message_id or not reaction_type:
            return render(
                request,
                "messaging/partials/message_reaction_result.html",
                {
                    "error": "Message ID and reaction type are required",
                    "success": False,
                },
            )

        # Placeholder reaction removal logic
        result_message = f"Removed {reaction_type} reaction from message"

        return render(
            request,
            "messaging/partials/message_reaction_result.html",
            {
                "message_id": message_id,
                "reaction_type": reaction_type,
                "action": "removed",
                "message": result_message,
                "success": True,
            },
        )

    except Exception as e:
        return render(
            request,
            "messaging/partials/message_reaction_result.html",
            {
                "error": f"Error removing reaction: {e!s}",
                "success": False,
            },
        )


@login_required
@require_http_methods(["GET"])
def message_reaction_list_htmx(request):
    """HTMX endpoint for listing message reactions."""
    try:
        message_id = request.GET.get("message_id")

        if not message_id:
            return render(
                request,
                "messaging/partials/message_reaction_list.html",
                {
                    "error": "Message ID is required",
                    "reactions": [],
                },
            )

        # Placeholder reaction list
        reactions = [
            {
                "type": "👍",
                "count": 3,
                "users": ["user1", "user2", "user3"],
                "user_reacted": True,
            },
            {
                "type": "❤️",
                "count": 2,
                "users": ["user4", "user5"],
                "user_reacted": False,
            },
            {
                "type": "😂",
                "count": 1,
                "users": ["user6"],
                "user_reacted": False,
            },
        ]

        return render(
            request,
            "messaging/partials/message_reaction_list.html",
            {
                "message_id": message_id,
                "reactions": reactions,
                "success": True,
            },
        )

    except Exception as e:
        return render(
            request,
            "messaging/partials/message_reaction_list.html",
            {
                "error": f"Error loading reactions: {e!s}",
                "reactions": [],
            },
        )


@login_required
@require_http_methods(["POST"])
def start_message_thread_htmx(request, message_id):
    """HTMX endpoint for starting a thread on a specific message."""
    try:
        # Placeholder thread start logic
        thread_data = {
            "thread_id": f"thread-{message_id}",
            "original_message_id": message_id,
            "created_by": (request.user.username if request.user.is_authenticated else "Anonymous"),
            "status": "active",
        }

        return render(
            request,
            "messaging/partials/message_thread_started.html",
            {
                "thread": thread_data,
                "message_id": message_id,
                "success": True,
            },
        )

    except Exception as e:
        return render(
            request,
            "messaging/partials/message_thread_started.html",
            {
                "error": f"Error starting thread: {e!s}",
                "message_id": message_id,
                "success": False,
            },
        )


@login_required
@require_http_methods(["POST"])
def close_message_thread_htmx(request, thread_id):
    """HTMX endpoint for closing a message thread."""
    try:
        # Placeholder thread close logic
        thread_data = {
            "thread_id": thread_id,
            "status": "closed",
            "closed_by": (request.user.username if request.user.is_authenticated else "Anonymous"),
            "closed_at": "2025-01-01 00:00:00",
        }

        return render(
            request,
            "messaging/partials/message_thread_closed.html",
            {
                "thread": thread_data,
                "thread_id": thread_id,
                "success": True,
            },
        )

    except Exception as e:
        return render(
            request,
            "messaging/partials/message_thread_closed.html",
            {
                "error": f"Error closing thread: {e!s}",
                "thread_id": thread_id,
                "success": False,
            },
        )


@login_required
@require_http_methods(["GET"])
def conversation_list_htmx(request):
    """HTMX endpoint for conversation list"""
    conversations = (
        Conversation.objects.filter(participants=request.user, is_active=True)
        .select_related("project", "created_by")
        .prefetch_related("participants")[:20]
    )

    # Add unread count for each conversation
    for conversation in conversations:
        conversation.unread_count = conversation.get_unread_count_for_user(request.user)

    context = {
        "conversations": conversations,
        "user": request.user,
    }

    return render(request, "messaging/shared/components/messages/conversation_list.html", context)


@login_required
@require_http_methods(["GET"])
def message_thread_htmx(request, conversation_id):
    """HTMX endpoint for message thread"""
    try:
        conversation = Conversation.objects.get(id=conversation_id, participants=request.user)

        # Mark conversation as read
        member = conversation.members.filter(user=request.user).first()
        if member:
            member.mark_read()

        # Get messages with pagination
        page = request.GET.get("page", 1)
        messages = conversation.messages.select_related("user").order_by("created_at")
        paginator = Paginator(messages, 50)
        page_obj = paginator.get_page(page)

        context = {
            "conversation": conversation,
            "messages": page_obj,
            "user": request.user,
        }

        return render(request, "messaging/shared/components/messages/message_thread.html", context)

    except Conversation.DoesNotExist:
        return HttpResponse("Conversation not found", status=404)


@login_required
@require_http_methods(["POST"])
def conversation_create_htmx(request):
    """HTMX endpoint for creating new conversations"""
    name = request.POST.get("name", "").strip()
    participant_ids = request.POST.getlist("participants")
    project_id = request.POST.get("project_id")
    is_group = request.POST.get("is_group", "false").lower() == "true"

    if not participant_ids:
        return HttpResponse("Participants required", status=400)

    try:
        # Get participants
        participants = User.objects.filter(id__in=participant_ids)
        if not participants.exists():
            return HttpResponse("Invalid participants", status=400)

        # Get project if specified
        project = None
        if project_id:
            try:
                project = Project.objects.get(id=project_id)
            except Project.DoesNotExist:
                return HttpResponse("Project not found", status=404)

        # Create conversation
        conversation = Conversation.objects.create(
            name=name or "New Conversation",
            created_by=request.user,
            project=project,
            is_group=is_group,
            is_active=True,
        )

        # Add participants
        conversation.participants.add(request.user)
        conversation.participants.add(*participants)

        # Create conversation members
        for participant in participants:
            ConversationMember.objects.create(conversation=conversation, user=participant, joined_at=timezone.now())

        ConversationMember.objects.create(conversation=conversation, user=request.user, joined_at=timezone.now())

        # Return updated conversation list
        return conversation_list_htmx(request)

    except (ConnectionError, TimeoutError, HTTPError) as e:
        return HttpResponse(f"Error creating conversation: {e!s}", status=400)


@login_required
@require_http_methods(["POST"])
def message_create_htmx(request):
    """HTMX endpoint for creating new messages using ChatMessageForm"""
    conversation_id = request.POST.get("conversation_id")

    if not conversation_id:
        return HttpResponse("Conversation ID required", status=400)

    try:
        conversation = Conversation.objects.get(id=conversation_id, participants=request.user)

        # Create form with posted data
        form = ChatMessageForm(data=request.POST, user=request.user, conversation=conversation)

        if form.is_valid():
            # Save the message using the form
            form.save()

            # Update conversation last activity
            conversation.updated_at = timezone.now()
            conversation.save()

            # Return updated message thread
            return message_thread_htmx(request, conversation_id)
        else:
            # Return form errors
            error_messages = []
            for field, errors in form.errors.items():
                for error in errors:
                    error_messages.append(f"{field}: {error}")
            return HttpResponse(f"Validation error: {'; '.join(error_messages)}", status=400)

    except Conversation.DoesNotExist:
        return HttpResponse("Conversation not found", status=404)
    except Exception as e:
        logger.error(f"Error creating message: {e}")
        return HttpResponse(f"Error creating message: {e!s}", status=400)


@login_required
@require_http_methods(["POST"])
def message_markdown_preview_htmx(request):
    """HTMX endpoint for real-time markdown preview of message content"""
    content = request.POST.get("content", "").strip()

    if not content:
        return HttpResponse('<div class="text-muted">Preview will appear here...</div>')

    try:
        # Configure markdown extensions for message preview
        md = markdown.Markdown(
            extensions=[
                "fenced_code",
                "tables",
                "nl2br",
                "sane_lists",
                "codehilite",
            ],
            extension_configs={"codehilite": {"css_class": "highlight", "use_pygments": True}},
        )

        # Convert markdown to HTML
        html_content = md.convert(escape(content))

        # Return preview HTML
        return HttpResponse(f'<div class="markdown-preview-content">{html_content}</div>')

    except Exception as e:
        logger.error(f"Error generating markdown preview: {e}")
        return HttpResponse('<div class="text-danger">Error generating preview</div>')


class MessagesView(LoginRequiredMixin, RoleRequiredMixin, TemplateView):
    required_roles = ["stakeholder"]

    template_name = "messaging/messages.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get user's conversations
        conversations = get_user_conversations(self.request.user)

        # Get unread counts
        unread_counts = get_unread_message_counts(self.request.user)

        context.update(
            {
                "conversations": conversations,
                "unread_counts": unread_counts,
                "active_tab": "messages",
            },
        )

        return context


@login_required
def messages_view(request):
    # Get user's conversations
    conversations = get_user_conversations(request.user)

    # Get unread counts
    unread_counts = get_unread_message_counts(request.user)

    # Get active conversation if specified
    active_conversation_id = request.GET.get("conversation")
    active_conversation = None
    messages = []

    if active_conversation_id:
        try:
            active_conversation = Conversation.objects.get(id=active_conversation_id, members=request.user)
            messages = ChatMessage.objects.filter(conversation=active_conversation).order_by("created_at")

            # Mark messages as read
            unread_messages = messages.filter(message_reads__isnull=True)

            for message in unread_messages:
                MessageRead.objects.create(message=message, user=request.user, read_at=timezone.now())

        except Conversation.DoesNotExist:
            pass

    # Create chat message form for unified markdown editor
    chat_form = ChatMessageForm(user=request.user, conversation=active_conversation)

    # Add preview URL to the form widget
    from django.urls import reverse

    preview_url = reverse("messaging:message_markdown_preview")
    if hasattr(chat_form.fields["content"].widget, "attrs"):
        chat_form.fields["content"].widget.attrs["data-preview-url"] = preview_url

    context = {
        "conversations": conversations,
        "active_conversation": active_conversation,
        "messages": messages,
        "unread_counts": unread_counts,
        "active_tab": "messages",
        "chat_form": chat_form,
    }

    return render(request, "messaging/messages.html", context)


@login_required
def messages_list_htmx(request):
    # Get category filter
    category = request.GET.get("category", "all")

    # Get search query
    search_query = request.GET.get("search", "")

    # Get user's conversations
    conversations = get_user_conversations(request.user, category, search_query)

    # Get unread counts
    unread_counts = get_unread_message_counts(request.user)

    context = {
        "conversations": conversations,
        "unread_counts": unread_counts,
        "active_category": category,
    }

    return render(request, "messaging/partials/conversation_list.html", context)




@login_required
def message_send_htmx(request):
    if request.method == "POST":
        conversation_id = request.POST.get("conversation_id")
        content = request.POST.get("content")

        if not content or not conversation_id:
            return HttpResponse("Message content and conversation ID are required", status=400)

        try:
            # Get conversation and verify user has access
            conversation = Conversation.objects.get(id=conversation_id, members=request.user)

            # Create message
            message = ChatMessage.objects.create(sender=request.user, conversation=conversation, content=content)

            # Process mentions
            mention_pattern = r"@(\w+)"
            mentions = re.findall(mention_pattern, content)

            for username in mentions:
                try:
                    mentioned_user = User.objects.get(username=username)
                    if mentioned_user in conversation.members.all():
                        MessageMention.objects.create(message=message, user=mentioned_user)

                        # Create notification for mention
                        Notification.objects.create(
                            user=mentioned_user,
                            sender=request.user,
                            notification_type="mention",
                            content=f"{request.user.get_full_name()} mentioned you in a message",
                            link=f"/messages/?conversation={conversation.id}",
                        )
                except User.DoesNotExist:
                    pass

            # Process reactions (emoji)
            emoji_pattern = r":([a-z_]+):"
            emojis = re.findall(emoji_pattern, content)

            for emoji in emojis:
                MessageReaction.objects.create(message=message, user=request.user, reaction=emoji)

            # Update conversation last activity
            conversation.last_activity = timezone.now()
            conversation.save()

            # Create activity record
            if conversation.project:
                Activity.objects.create(
                    user=request.user,
                    action="sent",
                    target_model="Message",
                    target_id=message.id,
                    target_name="message",
                    project=conversation.project,
                )

            # Return the new message
            return render(
                request,
                "messaging/partials/message_item.html",
                {"message": message, "conversation": conversation},
            )

        except Conversation.DoesNotExist:
            return HttpResponse("Conversation not found", status=404)

    return HttpResponse("Invalid request", status=400)


@login_required
def message_search_htmx(request):
    search_query = request.GET.get("query", "")

    if not search_query or len(search_query) < 3:
        return render(
            request,
            "messaging/partials/message_search_results.html",
            {"results": [], "query": search_query},
        )

    # Search for messages
    messages = ChatMessage.objects.filter(
        Q(content__icontains=search_query),
        Q(conversation__members=request.user),
    ).order_by("-created_at")[:20]

    # Group by conversation
    results = {}

    for message in messages:
        if message.conversation_id not in results:
            results[message.conversation_id] = {
                "conversation": message.conversation,
                "messages": [],
            }

        results[message.conversation_id]["messages"].append(message)

    context = {"results": results.values(), "query": search_query}

    return render(request, "messaging/partials/message_search_results.html", context)




@login_required
def team_chat_partial(request):
    # Get project ID
    project_id = request.GET.get("project_id")

    if not project_id:
        return HttpResponse("Project ID is required", status=400)

    try:
        # Get project and verify user has access
        project = Project.objects.get(id=project_id)

        if not user_has_project_access(request.user, project):
            return HttpResponse("Access denied", status=403)

        # Get or create project conversation
        conversation, created = Conversation.objects.get_or_create(
            project=project,
            is_project_conversation=True,
            defaults={"name": f"{project.name} Team Chat", "created_by": request.user},
        )

        # If conversation was just created, add project members
        if created:
            # Add project members to conversation
            for member in project.team_members.all():
                conversation.members.add(member)

                # Create conversation member record
                ConversationMember.objects.create(conversation=conversation, user=member)

            # Add project creator if not already a member
            if project.created_by not in project.team_members.all():
                conversation.members.add(project.created_by)

                # Create conversation member record
                ConversationMember.objects.create(
                    conversation=conversation,
                    user=project.created_by,
                )

            # Create welcome message
            ChatMessage.objects.create(
                sender=request.user,
                conversation=conversation,
                content=f"Project team chat started by {request.user.get_full_name()}",
            )

        # Get messages
        messages = ChatMessage.objects.filter(conversation=conversation).order_by("created_at")

        # Mark messages as read
        unread_messages = messages.filter(message_reads__isnull=True)

        for message in unread_messages:
            MessageRead.objects.create(message=message, user=request.user, read_at=timezone.now())

        context = {
            "conversation": conversation,
            "messages": messages,
            "project": project,
        }

        return render(request, "projects/partials/team_chat.html", context)

    except Project.DoesNotExist:
        return HttpResponse("Project not found", status=404)


@login_required
def send_team_message(request):
    if request.method == "POST":
        conversation_id = request.POST.get("conversation_id")
        content = request.POST.get("content")

        if not content or not conversation_id:
            return HttpResponse("Message content and conversation ID are required", status=400)

        try:
            # Get conversation and verify user has access
            conversation = Conversation.objects.get(id=conversation_id, members=request.user)

            # Create message
            message = ChatMessage.objects.create(sender=request.user, conversation=conversation, content=content)

            # Process mentions
            mention_pattern = r"@(\w+)"
            mentions = re.findall(mention_pattern, content)

            for username in mentions:
                try:
                    mentioned_user = User.objects.get(username=username)
                    if mentioned_user in conversation.members.all():
                        MessageMention.objects.create(message=message, user=mentioned_user)

                        # Create notification for mention
                        Notification.objects.create(
                            user=mentioned_user,
                            sender=request.user,
                            notification_type="mention",
                            content=f"{request.user.get_full_name()} mentioned you in a message",
                            link=(
                                f"/projects/{conversation.project.id}/chat"
                                if conversation.project
                                else f"/messages/?conversation={conversation.id}"
                            ),
                        )
                except User.DoesNotExist:
                    pass

            # Update conversation last activity
            conversation.last_activity = timezone.now()
            conversation.save()

            # Create activity record
            if conversation.project:
                Activity.objects.create(
                    user=request.user,
                    action="sent",
                    target_model="Message",
                    target_id=message.id,
                    target_name="team message",
                    project=conversation.project,
                )

            # Return the new message
            return render(
                request,
                "projects/partials/team_message_item.html",
                {"message": message},
            )

        except Conversation.DoesNotExist:
            return HttpResponse("Conversation not found", status=404)

    return HttpResponse("Invalid request", status=400)


@login_required
def message_mark_read_htmx(request, message_id):
    try:
        # Get message and verify user has access
        message = ChatMessage.objects.get(id=message_id, conversation__members=request.user)

        # Check if already read
        if MessageRead.objects.filter(message=message, user=request.user).exists():
            return HttpResponse("Message already marked as read", status=200)

        # Mark as read
        MessageRead.objects.create(message=message, user=request.user, read_at=timezone.now())

        # Broadcast read receipt update via SSE
        from asgiref.sync import async_to_sync
        from channels.layers import get_channel_layer

        channel_layer = get_channel_layer()
        if channel_layer:
            group_name = f"chat_{message.conversation.id}"
            async_to_sync(channel_layer.group_send)(
                group_name,
                {
                    "type": "message_read",
                    "message_id": str(message.id),
                    "user_id": request.user.id,
                    "username": request.user.username,
                    "timestamp": timezone.now().isoformat(),
                },
            )

        return HttpResponse("Message marked as read", status=200)

    except ChatMessage.DoesNotExist:
        return HttpResponse("Message not found", status=404)


@login_required
def message_read_status_htmx(request, message_id):
    """Return read receipt status for a message as HTML."""
    try:
        # Get message and verify user has access
        message = ChatMessage.objects.get(id=message_id, conversation__members=request.user).prefetch_related(
            "read_status__user"
        )

        return render(
            request,
            "common/htmx_partials/realtime/read_receipt.html",
            {
                "message": message,
            },
        )
    except ChatMessage.DoesNotExist:
        return HttpResponse("", status=200)  # Return empty response if message not found


@login_required
def conversation_mark_read_htmx(request, conversation_id):
    try:
        # Get conversation and verify user has access
        conversation = Conversation.objects.get(id=conversation_id, members=request.user)

        # Get unread messages
        unread_messages = ChatMessage.objects.filter(conversation=conversation, message_reads__isnull=True).exclude(
            sender=request.user,
        )

        # Mark all as read
        for message in unread_messages:
            MessageRead.objects.create(message=message, user=request.user, read_at=timezone.now())

        # Get updated unread counts
        unread_counts = get_unread_message_counts(request.user)

        return JsonResponse(
            {
                "status": "success",
                "marked_read": unread_messages.count(),
                "unread_counts": unread_counts,
            },
        )

    except Conversation.DoesNotExist:
        return HttpResponse("Conversation not found", status=404)


@login_required
def message_reaction_add_htmx(request):
    if request.method == "POST":
        message_id = request.POST.get("message_id")
        reaction = request.POST.get("reaction")

        if not message_id or not reaction:
            return HttpResponse("Message ID and reaction are required", status=400)

        try:
            # Get message and verify user has access
            message = ChatMessage.objects.get(id=message_id, conversation__members=request.user)

            # Check if reaction already exists
            existing_reaction = MessageReaction.objects.filter(
                message=message,
                user=request.user,
                reaction=reaction,
            ).first()

            if existing_reaction:
                # Remove reaction if it already exists
                existing_reaction.delete()
            else:
                # Add reaction
                MessageReaction.objects.create(message=message, user=request.user, reaction=reaction)

            # Get updated reactions
            reactions = MessageReaction.objects.filter(message=message).values("reaction").annotate(count=Count("id"))

            # Format reactions for response
            formatted_reactions = []
            for r in reactions:
                formatted_reactions.append(
                    {
                        "reaction": r["reaction"],
                        "count": r["count"],
                        "users": list(
                            MessageReaction.objects.filter(message=message, reaction=r["reaction"]).values_list(
                                "user__username",
                                flat=True,
                            ),
                        ),
                    },
                )

            return render(
                request,
                "messaging/partials/message_reactions.html",
                {"message": message, "reactions": formatted_reactions},
            )

        except ChatMessage.DoesNotExist:
            return HttpResponse("Message not found", status=404)

    return HttpResponse("Invalid request", status=400)


@login_required
def message_reaction_picker_htmx(request):
    message_id = request.GET.get("message_id")

    if not message_id:
        return HttpResponse("Message ID is required", status=400)

    try:
        # Get message and verify user has access
        message = ChatMessage.objects.get(id=message_id, conversation__members=request.user)

        # Get common reactions
        common_reactions = [
            {"code": "thumbs_up", "emoji": "👍"},
            {"code": "thumbs_down", "emoji": "👎"},
            {"code": "heart", "emoji": "❤️"},
            {"code": "laugh", "emoji": "😂"},
            {"code": "surprised", "emoji": "😮"},
            {"code": "sad", "emoji": "😢"},
            {"code": "angry", "emoji": "😡"},
            {"code": "clap", "emoji": "👏"},
            {"code": "fire", "emoji": "🔥"},
            {"code": "tada", "emoji": "🎉"},
        ]

        return render(
            request,
            "messaging/partials/reaction_picker.html",
            {"message": message, "common_reactions": common_reactions},
        )

    except ChatMessage.DoesNotExist:
        return HttpResponse("Message not found", status=404)


@login_required
def message_file_upload_htmx(request):
    if request.method == "POST":
        conversation_id = request.POST.get("conversation_id")
        file = request.FILES.get("file")

        if not conversation_id or not file:
            return HttpResponse("Conversation ID and file are required", status=400)

        try:
            # Get conversation and verify user has access
            conversation = Conversation.objects.get(id=conversation_id, members=request.user)

            # Create document
            document = Document.objects.create(
                name=file.name,
                file=file,
                uploaded_by=request.user,
                project=conversation.project,
            )

            # Create message with document reference
            message = ChatMessage.objects.create(
                sender=request.user,
                conversation=conversation,
                content=f"Shared a file: {file.name}",
                document=document,
            )

            # Update conversation last activity
            conversation.last_activity = timezone.now()
            conversation.save()

            # Create activity record
            if conversation.project:
                Activity.objects.create(
                    user=request.user,
                    action="shared",
                    target_model="Document",
                    target_id=document.id,
                    target_name=document.name,
                    project=conversation.project,
                )

            # Return the new message
            return render(
                request,
                "messaging/partials/message_item.html",
                {"message": message, "conversation": conversation},
            )

        except Conversation.DoesNotExist:
            return HttpResponse("Conversation not found", status=404)

    return render(request, "messaging/partials/file_upload_form.html")


@login_required
def message_thread_create_htmx(request):
    if request.method == "POST":
        message_id = request.POST.get("message_id")

        if not message_id:
            return HttpResponse("Message ID is required", status=400)

        try:
            # Get message and verify user has access
            message = ChatMessage.objects.get(id=message_id, conversation__members=request.user)

            # Create thread if it doesn't exist
            if not message.thread:
                thread = MessageThread.objects.create(original_message=message, created_by=request.user)
                message.thread = thread
                message.save()

            return render(
                request,
                "messaging/partials/message_thread_form.html",
                {"message": message},
            )

        except ChatMessage.DoesNotExist:
            return HttpResponse("Message not found", status=404)

    return HttpResponse("Invalid request", status=400)


@login_required
def message_thread_reply_htmx(request):
    if request.method == "POST":
        thread_id = request.POST.get("thread_id")
        content = request.POST.get("content")

        if not thread_id or not content:
            return HttpResponse("Thread ID and content are required", status=400)

        try:
            # Get thread and verify user has access
            thread = MessageThread.objects.get(id=thread_id, original_message__conversation__members=request.user)

            # Create reply
            reply = ChatMessage.objects.create(
                sender=request.user,
                conversation=thread.original_message.conversation,
                content=content,
                parent_thread=thread,
            )

            # Notify original message sender if different from current user
            if thread.original_message.sender != request.user:
                Notification.objects.create(
                    user=thread.original_message.sender,
                    sender=request.user,
                    notification_type="thread_reply",
                    content=f"{request.user.get_full_name()} replied to your message",
                    link=f"/messages/?conversation={thread.original_message.conversation.id}&thread={thread.id}",
                )

            # Return the new reply
            return render(
                request,
                "messaging/shared/components/messages/thread_reply_item.html",
                {"reply": reply, "thread": thread},
            )

        except MessageThread.DoesNotExist:
            return HttpResponse("Thread not found", status=404)

    return HttpResponse("Invalid request", status=400)


@login_required
def message_thread_view_htmx(request, thread_id):
    try:
        # Get thread and verify user has access
        thread = MessageThread.objects.get(id=thread_id, original_message__conversation__members=request.user)

        # Get replies
        replies = ChatMessage.objects.filter(parent_thread=thread).order_by("created_at")

        context = {
            "thread": thread,
            "original_message": thread.original_message,
            "replies": replies,
        }

        return render(request, "messaging/partials/message_thread_view.html", context)

    except MessageThread.DoesNotExist:
        return HttpResponse("Thread not found", status=404)


@login_required
def message_thread_toggle_htmx(request):
    thread_id = request.GET.get("thread_id")

    if not thread_id:
        return HttpResponse("Thread ID is required", status=400)

    try:
        # Get thread and verify user has access
        thread = MessageThread.objects.get(id=thread_id, original_message__conversation__members=request.user)

        # Get replies
        replies = ChatMessage.objects.filter(parent_thread=thread).order_by("created_at")

        context = {
            "thread": thread,
            "original_message": thread.original_message,
            "replies": replies,
        }

        return render(request, "messaging/partials/message_thread_collapsed.html", context)

    except MessageThread.DoesNotExist:
        return HttpResponse("Thread not found", status=404)


@login_required
def user_mention_search_htmx(request):
    query = request.GET.get("query", "")
    conversation_id = request.GET.get("conversation_id")

    if not conversation_id:
        return HttpResponse("Conversation ID is required", status=400)

    try:
        # Get conversation and verify user has access
        conversation = Conversation.objects.get(id=conversation_id, members=request.user)

        # Search for members
        if query:
            members = conversation.members.filter(
                Q(username__icontains=query) | Q(first_name__icontains=query) | Q(last_name__icontains=query),
            ).exclude(id=request.user.id)[:10]
        else:
            members = conversation.members.exclude(id=request.user.id)[:10]

        return render(request, "messaging/partials/mention_suggestions.html", {"members": members})

    except Conversation.DoesNotExist:
        return HttpResponse("Conversation not found", status=404)


@login_required
def message_mentions_list_htmx(request):
    # Get mentions for the current user
    mentions = MessageMention.objects.filter(user=request.user).order_by("-message__created_at")[:20]

    return render(request, "messaging/partials/mentions_list.html", {"mentions": mentions})


# Helper functions
def get_user_conversations(user, category="all", search_query=""):
    """Get conversations for a user with optional filtering."""
    # Base queryset
    queryset = Conversation.objects.filter(members=user)

    # Apply category filter
    if category == "direct":
        queryset = queryset.filter(is_group=False)
    elif category == "group":
        queryset = queryset.filter(is_group=True)
    elif category == "project":
        queryset = queryset.filter(project__isnull=False)

    # Apply search filter
    if search_query:
        queryset = queryset.filter(
            Q(name__icontains=search_query)
            | Q(members__first_name__icontains=search_query)
            | Q(members__last_name__icontains=search_query)
            | Q(members__username__icontains=search_query)
            | Q(chatmessage__content__icontains=search_query),
        ).distinct()

    # Order by last activity
    queryset = queryset.order_by("-last_activity")

    # Annotate with last message
    conversations = []

    for conversation in queryset:
        # Get last message
        last_message = ChatMessage.objects.filter(conversation=conversation).order_by("-created_at").first()

        # Get unread count
        unread_count = (
            ChatMessage.objects.filter(conversation=conversation, message_reads__isnull=True)
            .exclude(sender=user)
            .count()
        )

        # Get other members (for direct messages)
        other_members = conversation.members.exclude(id=user.id)

        conversations.append(
            {
                "conversation": conversation,
                "last_message": last_message,
                "unread_count": unread_count,
                "other_members": other_members,
            },
        )

    return conversations


def get_last_message(user, channel, project_id=None):
    """Get the last message for a user in a specific channel."""
    if channel == "project" and project_id:
        # Get project conversation
        conversation = Conversation.objects.filter(
            project_id=project_id,
            is_project_conversation=True,
            members=user,
        ).first()

        if conversation:
            return ChatMessage.objects.filter(conversation=conversation).order_by("-created_at").first()

    return None


def get_unread_count(user, channel, project_id=None):
    """Get unread message count for a user in a specific channel."""
    if channel == "project" and project_id:
        # Get project conversation
        conversation = Conversation.objects.filter(
            project_id=project_id,
            is_project_conversation=True,
            members=user,
        ).first()

        if conversation:
            return ChatMessage.objects.filter(conversation=conversation, message_reads__isnull=True).count()

    return 0


def get_unread_message_counts(user):
    """Get unread message counts for a user across different categories."""
    return {
        "all": ChatMessage.objects.filter(conversation__members=user, message_reads__isnull=True).count(),
        "direct": ChatMessage.objects.filter(
            conversation__members=user,
            conversation__is_group=False,
            message_reads__isnull=True,
        ).count(),
        "group": ChatMessage.objects.filter(
            conversation__members=user,
            conversation__is_group=True,
            message_reads__isnull=True,
        ).count(),
        "project": ChatMessage.objects.filter(
            conversation__members=user,
            conversation__project__isnull=False,
            message_reads__isnull=True,
        ).count(),
    }


@login_required
@require_http_methods(["GET"])
def conversation_members_htmx(request, conversation_id):
    """HTMX endpoint for conversation members"""
    try:
        conversation = Conversation.objects.get(id=conversation_id, participants=request.user)

        members = conversation.members.select_related("user").order_by("joined_at")

        context = {
            "conversation": conversation,
            "members": members,
            "user": request.user,
        }

        return render(
            request,
            "messaging/shared/components/messages/conversation_members.html",
            context,
        )

    except Conversation.DoesNotExist:
        return HttpResponse("Conversation not found", status=404)


@login_required
@require_http_methods(["POST"])
def conversation_leave_htmx(request, conversation_id):
    """HTMX endpoint for leaving a conversation"""
    try:
        conversation = Conversation.objects.get(id=conversation_id, participants=request.user)

        # Remove user from conversation
        member = conversation.members.filter(user=request.user).first()
        if member:
            member.delete()

        response = HttpResponse("")
        response["HX-Trigger"] = "conversationLeft"
        return response

    except Conversation.DoesNotExist:
        return HttpResponse("Conversation not found", status=404)


@login_required
@require_http_methods(["DELETE", "POST"])
def message_delete_htmx(request, message_id):
    """HTMX endpoint for deleting messages"""
    try:
        message = ChatMessage.objects.get(
            id=message_id,
            user=request.user,  # Only allow users to delete their own messages
        )

        message.delete()

        return HttpResponse("")  # Empty response removes the message from DOM

    except ChatMessage.DoesNotExist:
        return HttpResponse("Message not found", status=404)


@login_required
def collaboration_settings_htmx(request):
    """Update user collaboration settings"""
    from .models import CollaborationSettings

    if request.method == "POST":
        try:
            settings, created = CollaborationSettings.objects.get_or_create(user=request.user)

            # Update settings from form data
            settings.auto_collapse_threads = request.POST.get("auto_collapse_threads") == "on"
            settings.show_thread_previews = request.POST.get("show_thread_previews") == "on"
            settings.mention_notifications = request.POST.get("mention_notifications") == "on"
            settings.mention_sound = request.POST.get("mention_sound") == "on"
            settings.mention_email = request.POST.get("mention_email") == "on"
            settings.show_reaction_tooltips = request.POST.get("show_reaction_tooltips") == "on"
            settings.reaction_notifications = request.POST.get("reaction_notifications") == "on"
            settings.custom_emoji_set = request.POST.get("custom_emoji_set", "default")
            settings.compact_message_view = request.POST.get("compact_message_view") == "on"
            settings.show_message_timestamps = request.POST.get("show_message_timestamps") == "on"
            settings.show_user_status = request.POST.get("show_user_status") == "on"
            settings.allow_direct_messages = request.POST.get("allow_direct_messages") == "on"
            settings.show_read_receipts = request.POST.get("show_read_receipts") == "on"
            settings.default_workspace_view = request.POST.get("default_workspace_view", "conversations")

            settings.save()

            return render(
                request,
                "messaging/shared/components/messages/settings_saved.html",
                {
                    "settings": settings,
                    "message": "Collaboration settings saved successfully!",
                },
            )

        except Exception:
            logger.exception("Error saving collaboration settings")
            return JsonResponse({"error": "Failed to save settings"}, status=500)

    # GET request - show settings form
    try:
        settings = CollaborationSettings.get_or_create_for_user(request.user)
    except Exception:
        logger.exception("Error loading collaboration settings")
        settings = CollaborationSettings.objects.create(user=request.user)

    return render(
        request,
        "messaging/shared/components/messages/collaboration_settings.html",
        {
            "settings": settings,
            "current_user": request.user,
        },
    )


def user_has_project_access(user, project):
    """Check if a user has access to a project."""
    return user.is_staff or project.created_by == user or project.team_members.filter(user_id=user.id).exists()


def create_message_notification(request):
    """Create notification for new message (called by messaging system)"""
    try:
        message_id = request.POST.get("message_id")
        conversation_id = request.POST.get("conversation_id")

        if message_id:
            try:
                message = ChatMessage.objects.get(id=message_id)

                if conversation_id:
                    try:
                        conversation = Conversation.objects.get(id=conversation_id)
                        # Notify conversation participants about new message
                        from apps.notifications.compatibility import create_notification

                        # Create notification for conversation participants
                        for participant in conversation.members.exclude(user=request.user):
                            create_notification(
                                recipient=participant.user,
                                notification_type="new_message",
                                title="New Message",
                                message=f"New message in {conversation.name or 'conversation'}",
                                data={
                                    "message_id": message.id,
                                    "conversation_id": conversation.id,
                                    "sender": request.user.username,
                                },
                                related_object=message,
                            )
                    except Conversation.DoesNotExist:
                        # Notify about new message without conversation context
                        from apps.notifications.compatibility import create_notification

                        # Create notification for message recipient
                        create_notification(
                            recipient=message.recipient,
                            notification_type="new_message",
                            title="New Message",
                            message=f"New message from {request.user.get_full_name() or request.user.username}",
                            data={
                                "message_id": message.id,
                                "sender": request.user.username,
                            },
                            related_object=message,
                        )
                else:
                    # Notify about new message
                    from apps.notifications.compatibility import create_notification

                    # Create notification for message recipient
                    if hasattr(message, "recipient") and message.recipient:
                        create_notification(
                            recipient=message.recipient,
                            notification_type="new_message",
                            title="New Message",
                            message=f"New message from {request.user.get_full_name() or request.user.username}",
                            data={
                                "message_id": message.id,
                                "sender": request.user.username,
                            },
                            related_object=message,
                        )

                return JsonResponse({"success": True})

            except ChatMessage.DoesNotExist:
                return JsonResponse({"success": False, "error": "Message not found"})

        return JsonResponse({"success": False, "error": "Message ID required"})

    except Exception as e:
        logger.exception("Error creating message notification")
        return JsonResponse({"success": False, "error": str(e)})


# ========== REQUESTED MESSAGING VIEWS ==========
# The following 7 views were specifically requested for extraction:


@login_required
def messaging_chat_interface(request):
    """Main messaging chat interface"""
    # Get user's conversations with better organization
    conversations = (
        Conversation.objects.filter(participants=request.user, is_active=True)
        .select_related("project", "created_by")
        .prefetch_related("participants")
        .order_by("-last_message_at")[:50]
    )

    # Get active conversation from URL parameter
    active_conversation_id = request.GET.get("conversation")
    active_conversation = None
    messages = []

    if active_conversation_id:
        try:
            active_conversation = conversations.get(id=active_conversation_id)
            # Mark conversation as read
            member = active_conversation.members.filter(user=request.user).first()
            if member:
                member.mark_read()

            # Get messages
            messages = active_conversation.messages.select_related("user").order_by("created_at")
        except Conversation.DoesNotExist:
            pass
    elif conversations.exists():
        active_conversation = conversations.first()
        messages = active_conversation.messages.select_related("user").order_by("created_at")

    # Get unread whisper count
    unread_whispers = WhisperMessage.objects.filter(recipient=request.user, is_read=False).count()

    # Create chat message form for unified markdown editor
    chat_form = ChatMessageForm(user=request.user, conversation=active_conversation)

    # Add preview URL to the form widget
    from django.urls import reverse

    preview_url = reverse("messaging:message_markdown_preview")
    if hasattr(chat_form.fields["content"].widget, "attrs"):
        chat_form.fields["content"].widget.attrs["data-preview-url"] = preview_url

    context = {
        "conversations": conversations,
        "active_conversation": active_conversation,
        "messages": messages,
        "unread_whispers": unread_whispers,
        "user": request.user,
        "chat_form": chat_form,
    }

    return render(request, "messaging/chat_interface.html", context)


@login_required
@require_http_methods(["POST"])
def messaging_whisper_create_htmx(request):
    """Create a new whisper message (HTMX endpoint)"""
    recipient_id = request.POST.get("recipient_id")
    message_text = request.POST.get("message", "").strip()

    if not recipient_id or not message_text:
        return HttpResponse("Recipient and message content are required", status=400)

    try:
        recipient = User.objects.get(id=recipient_id)

        # Create the whisper message
        whisper = WhisperMessage.objects.create(sender=request.user, recipient=recipient, message=message_text)

        # Return success response
        return render(
            request,
            "components/whisper_created.html",
            {
                "whisper": whisper,
                "success_message": f"Whisper sent to {recipient.get_full_name()}",
            },
        )

    except User.DoesNotExist:
        return HttpResponse("Recipient not found", status=404)
    except (ConnectionError, TimeoutError, HTTPError) as e:
        return HttpResponse(f"Error creating whisper: {e!s}", status=400)


@login_required
@require_http_methods(["DELETE", "POST"])
def messaging_whisper_delete_htmx(request, whisper_id):
    """Delete a whisper message (HTMX endpoint)"""
    try:
        whisper = WhisperMessage.objects.filter(Q(sender=request.user) | Q(recipient=request.user)).get(id=whisper_id)

        whisper.delete()

        return HttpResponse("")  # Empty response removes the element from DOM

    except WhisperMessage.DoesNotExist:
        return HttpResponse("Whisper not found", status=404)
    except (ConnectionError, TimeoutError, HTTPError) as e:
        return HttpResponse(f"Error deleting whisper: {e!s}", status=400)


@login_required
@require_http_methods(["GET"])
def messaging_whisper_list_htmx(request):
    """Get list of whisper messages (HTMX endpoint)"""
    # Get filter parameters
    filter_type = request.GET.get("filter", "received")  # 'received', 'sent', 'all'
    page = request.GET.get("page", 1)

    # Build queryset based on filter
    if filter_type == "sent":
        whispers = WhisperMessage.objects.filter(sender=request.user).select_related("recipient")
    elif filter_type == "all":
        whispers = WhisperMessage.objects.filter(Q(sender=request.user) | Q(recipient=request.user)).select_related(
            "sender",
            "recipient",
        )
    else:  # default to 'received'
        whispers = WhisperMessage.objects.filter(recipient=request.user).select_related("sender")

    whispers = whispers.order_by("-created_at")

    # Paginate
    paginator = Paginator(whispers, 20)
    page_obj = paginator.get_page(page)

    context = {
        "whispers": page_obj,
        "filter_type": filter_type,
        "user": request.user,
    }

    return render(request, "components/whisper_list.html", context)


@login_required
@require_http_methods(["POST"])
def messaging_whisper_read_htmx(request, whisper_id):
    """Mark a whisper message as read (HTMX endpoint)"""
    try:
        whisper = WhisperMessage.objects.get(id=whisper_id, recipient=request.user)

        if not whisper.is_read:
            whisper.mark_read()

        return render(
            request,
            "components/whisper_item.html",
            {"whisper": whisper, "show_read_status": True},
        )

    except WhisperMessage.DoesNotExist:
        return HttpResponse("Whisper not found", status=404)
    except (FileNotFoundError, PermissionError, OSError) as e:
        return HttpResponse(f"Error marking whisper as read: {e!s}", status=400)


class ConversationListView(LoginRequiredMixin, RoleRequiredMixin, ListView):
    """List view for user's conversations"""

    required_roles = ["stakeholder"]

    model = Conversation
    template_name = "messaging/conversation_list.html"
    context_object_name = "conversations"
    paginate_by = 20

    def get_queryset(self):
        """Get conversations for the current user"""
        queryset = (
            Conversation.objects.filter(participants=self.request.user, is_active=True)
            .select_related("project", "created_by")
            .prefetch_related("participants")
            .order_by("-last_message_at")
        )

        # Apply search filter if provided
        search = self.request.GET.get("search")
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search)
                | Q(participants__first_name__icontains=search)
                | Q(participants__last_name__icontains=search)
                | Q(participants__username__icontains=search),
            ).distinct()

        # Apply type filter if provided
        conv_type = self.request.GET.get("type")
        if conv_type and conv_type in dict(Conversation.CONVERSATION_TYPES):
            queryset = queryset.filter(conversation_type=conv_type)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Add unread counts for each conversation
        for conversation in context["conversations"]:
            conversation.unread_count = conversation.get_unread_count_for_user(self.request.user)

        # Add filter options
        context.update(
            {
                "conversation_types": Conversation.CONVERSATION_TYPES,
                "current_search": self.request.GET.get("search", ""),
                "current_type": self.request.GET.get("type", ""),
                "total_unread": sum(conv.unread_count for conv in context["conversations"]),
            },
        )

        return context


class ConversationDetailView(LoginRequiredMixin, RoleRequiredMixin, DetailView):
    """Detail view for a specific conversation"""

    required_roles = ["stakeholder"]

    model = Conversation
    template_name = "messaging/conversation_detail.html"
    context_object_name = "conversation"

    def get_queryset(self):
        """Ensure user has access to the conversation"""
        return (
            Conversation.objects.filter(participants=self.request.user)
            .select_related("project", "created_by")
            .prefetch_related("participants", "members")
        )

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        conversation = context["conversation"]

        # Mark conversation as read
        member = conversation.members.filter(user=self.request.user).first()
        if member:
            member.mark_read()

        # Get messages with pagination
        page = self.request.GET.get("page", 1)
        messages = (
            conversation.messages.select_related("user")
            .prefetch_related("attachments", "reactions", "mentions")
            .order_by("created_at")
        )

        paginator = Paginator(messages, 50)
        page_obj = paginator.get_page(page)

        # Get conversation members
        members = conversation.members.select_related("user").order_by("joined_at")

        # Check if user can manage conversation
        user_member = conversation.members.filter(user=self.request.user).first()
        can_manage = (user_member and user_member.is_admin) or conversation.created_by == self.request.user

        context.update(
            {
                "messages": page_obj,
                "members": members,
                "can_manage": can_manage,
                "user_member": user_member,
                "other_participants": conversation.participants.exclude(id=self.request.user.id),
            },
        )

        return context


def send_missed_message_notifications(request):
    """Send missed message notifications (typically called by cron job)"""
    try:
        hours_ago = int(request.POST.get("hours_ago", 1))

        # Send notifications for users who haven't been active
        for user in User.objects.filter(is_active=True):
            # Check if user has been inactive
            if hasattr(user, "last_activity") and user.last_activity:
                time_since_activity = timezone.now() - user.last_activity
                if time_since_activity.total_seconds() > hours_ago * 3600:
                    # Notify user about missed messages
                    from apps.notifications.compatibility import create_notification

                    # Create notification for missed messages
                    create_notification(
                        recipient=user,
                        notification_type="missed_messages",
                        title="Missed Messages",
                        message=f"You have missed messages from the last {hours_ago} hours",
                        data={
                            "hours_ago": hours_ago,
                            "notification_time": timezone.now().isoformat(),
                        },
                    )

        return JsonResponse({"success": True})

    except Exception as e:
        logger.exception("Error sending missed message notifications")
        return JsonResponse({"success": False, "error": str(e)})


# ========== PROJECT-SPECIFIC MESSAGING VIEWS ==========


class ProjectChatView(LoginRequiredMixin, RoleRequiredMixin, TemplateView):
    """Project-specific chat interface"""

    required_roles = ["stakeholder"]

    template_name = "messaging/project_chat.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        project_id = self.kwargs["project_id"]

        try:
            project = Project.objects.get(id=project_id)

            # Check user access
            if not user_has_project_access(self.request.user, project):
                raise PermissionDenied("You don't have access to this project chat.")

            # Get or create project conversation
            conversation, created = Conversation.objects.get_or_create(
                project=project,
                conversation_type="project",
                defaults={
                    "name": f"{project.name} Team Chat",
                    "created_by": self.request.user,
                    "is_active": True,
                },
            )

            # Add project team members to conversation if just created
            if created:
                if hasattr(project, "team_members"):
                    conversation.participants.add(*project.team_members.all())
                conversation.participants.add(self.request.user)

                # Create conversation members
                for user in conversation.participants.all():
                    ConversationMember.objects.get_or_create(
                        conversation=conversation,
                        user=user,
                        defaults={"joined_at": timezone.now()},
                    )

            # Get messages
            messages = conversation.messages.select_related("user").order_by("created_at")

            # Get conversation members
            members = conversation.members.select_related("user").order_by("joined_at")

            context.update(
                {
                    "project": project,
                    "conversation": conversation,
                    "messages": messages,
                    "members": members,
                    "user": self.request.user,
                },
            )

        except Project.DoesNotExist:
            raise Http404("Project not found")

        return context


class ProjectCommentsView(LoginRequiredMixin, RoleRequiredMixin, TemplateView):
    """Project comments and discussion view"""

    required_roles = ["stakeholder"]

    template_name = "messaging/project_comments.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        project_id = self.kwargs["project_id"]

        try:
            project = Project.objects.get(id=project_id)

            # Check user access
            if not user_has_project_access(self.request.user, project):
                raise PermissionDenied("You don't have access to this project.")

            # Get project comments
            comments = Comment.objects.filter(project=project).select_related("user").order_by("-created_at")

            # Paginate comments
            page = self.request.GET.get("page", 1)
            paginator = Paginator(comments, 20)
            page_obj = paginator.get_page(page)

            context.update(
                {
                    "project": project,
                    "comments": page_obj,
                    "user": self.request.user,
                },
            )

        except Project.DoesNotExist:
            raise Http404("Project not found")

        return context

    def post(self, request, *args, **kwargs):
        """Handle new comment creation"""
        project_id = self.kwargs["project_id"]

        try:
            project = Project.objects.get(id=project_id)

            # Check user access
            if not user_has_project_access(request.user, project):
                raise PermissionDenied("You don't have access to this project.")

            content = request.POST.get("content", "").strip()
            if content:
                Comment.objects.create(
                    user=request.user,
                    project=project,
                    content=content,
                    created_at=timezone.now(),
                )

                # Create activity record
                Activity.objects.create(
                    user=request.user,
                    action="commented",
                    target_model="Project",
                    target_id=project.id,
                    target_name=project.name,
                    project=project,
                    details=f"Added comment: {content[:50]}...",
                )

            return redirect("CLEAR:project_comments", project_id=project_id)

        except Project.DoesNotExist:
            raise Http404("Project not found")


class MeetingsView(LoginRequiredMixin, RoleRequiredMixin, TemplateView):
    """Meeting management interface"""

    required_roles = ["stakeholder"]

    template_name = "messaging/meetings.html"


class WhisperInterfaceView(LoginRequiredMixin, RoleRequiredMixin, TemplateView):
    """Interface for whisper messaging system."""

    required_roles = ["stakeholder"]

    template_name = "messaging/whisper_interface.html"


class NotificationListView(LoginRequiredMixin, RoleRequiredMixin, ListView):
    """List view for user notifications."""

    required_roles = ["stakeholder"]

    model = Notification
    template_name = "messaging/notification_list.html"
    context_object_name = "notifications"
    paginate_by = 20

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get user's meetings (simplified implementation)
        # In a real implementation, you'd have a Meeting model
        meetings = [
            {
                "id": 1,
                "title": "Project Kickoff",
                "date": timezone.now() + timezone.timedelta(days=1),
                "participants": 5,
                "project": "Infrastructure Project A",
            },
            {
                "id": 2,
                "title": "Weekly Standup",
                "date": timezone.now() + timezone.timedelta(days=7),
                "participants": 8,
                "project": "Utility Coordination B",
            },
        ]

        context.update(
            {
                "meetings": meetings,
                "upcoming_count": len(meetings),
            },
        )

        return context


# ========== ADDITIONAL WHISPER MESSAGING VIEWS ==========


@login_required
@require_http_methods(["GET"])
def whisper_search_htmx(request):
    """Search for users to whisper to (HTMX endpoint)"""
    query = request.GET.get("q", "").strip()

    if not query or len(query) < 2:
        return render(
            request,
            "users/shared/components/whispers/user_search_results.html",
            {"users": [], "query": query},
        )

    # Search for users excluding current user
    users = (
        User.objects.filter(
            Q(username__icontains=query)
            | Q(first_name__icontains=query)
            | Q(last_name__icontains=query)
            | Q(email__icontains=query),
        )
        .exclude(id=request.user.id)
        .distinct()[:10]
    )

    context = {"users": users, "query": query}

    return render(request, "users/shared/components/whispers/user_search_results.html", context)


@login_required
@require_http_methods(["POST"])
def whisper_send_htmx(request):
    """Send a whisper message to a user (HTMX endpoint)"""
    recipient_id = request.POST.get("recipient_id")
    message_text = request.POST.get("message", "").strip()

    if not recipient_id or not message_text:
        return HttpResponse("Recipient and message are required", status=400)

    try:
        recipient = User.objects.get(id=recipient_id)

        # Create the whisper
        whisper = WhisperMessage.objects.create(sender=request.user, recipient=recipient, message=message_text)

        # Return the new whisper item
        return render(
            request,
            "messaging/shared/components/whispers/whisper_item.html",
            {"whisper": whisper, "user": request.user},
        )

    except User.DoesNotExist:
        return HttpResponse("Recipient not found", status=404)
    except (ConnectionError, TimeoutError, HTTPError) as e:
        return HttpResponse(f"Error sending whisper: {e!s}", status=400)


@login_required
@require_http_methods(["GET"])
def whisper_conversation_htmx(request, user_id):
    """Get whisper conversation with a specific user (HTMX endpoint)"""
    try:
        other_user = User.objects.get(id=user_id)

        # Get all whispers between the two users
        whispers = WhisperMessage.objects.filter(
            Q(sender=request.user, recipient=other_user) | Q(sender=other_user, recipient=request.user),
        ).order_by("created_at")

        # Mark received whispers as read
        WhisperMessage.objects.filter(sender=other_user, recipient=request.user, is_read=False).update(
            is_read=True,
            read_at=timezone.now(),
        )

        context = {"whispers": whispers, "other_user": other_user, "user": request.user}

        return render(
            request,
            "messaging/shared/components/whispers/whisper_conversation.html",
            context,
        )

    except User.DoesNotExist:
        return HttpResponse("User not found", status=404)


@login_required
@require_http_methods(["DELETE", "POST"])
def whisper_conversation_delete_htmx(request, user_id):
    """Delete entire whisper conversation with a user (HTMX endpoint)"""
    try:
        other_user = User.objects.get(id=user_id)

        # Delete all whispers between the two users
        WhisperMessage.objects.filter(
            Q(sender=request.user, recipient=other_user) | Q(sender=other_user, recipient=request.user),
        ).delete()

        # Return empty response to remove from UI
        return HttpResponse("")

    except User.DoesNotExist:
        return HttpResponse("User not found", status=404)
    except (ConnectionError, TimeoutError, HTTPError) as e:
        return HttpResponse(f"Error deleting conversation: {e!s}", status=400)


@login_required
@require_http_methods(["GET"])
def user_list_for_whisper_htmx(request):
    """Get list of users available for whispering (HTMX endpoint)"""
    search_query = request.GET.get("search", "").strip()

    # Get users the current user has interacted with or can whisper to
    users_queryset = User.objects.exclude(id=request.user.id).filter(is_active=True)

    # Apply search filter if provided
    if search_query:
        users_queryset = users_queryset.filter(
            Q(username__icontains=search_query)
            | Q(first_name__icontains=search_query)
            | Q(last_name__icontains=search_query)
            | Q(email__icontains=search_query),
        )

    # Get users with recent whisper activity
    recent_whisper_users = (
        User.objects.filter(Q(sent_whispers__recipient=request.user) | Q(received_whispers__sender=request.user))
        .distinct()
        .exclude(id=request.user.id)[:5]
    )

    # Get all active users for the full list
    all_users = users_queryset.order_by("first_name", "last_name")[:20]

    context = {
        "recent_users": recent_whisper_users,
        "all_users": all_users,
        "search_query": search_query,
        "user": request.user,
    }

    return render(request, "users/shared/components/whispers/user_list.html", context)


# ========== ADVANCED CHAT FUNCTIONALITY ==========
# These features were extracted from CLEAR views and need to be added


@login_required
@require_http_methods(["POST"])
def message_batch_action_htmx(request):
    """Handle batch actions on messages (mark as read, delete, etc.)"""
    action = request.POST.get("action")
    message_ids = request.POST.getlist("message_ids")

    if not action or not message_ids:
        return HttpResponse("Action and message IDs required", status=400)

    try:
        messages = ChatMessage.objects.filter(id__in=message_ids, conversation__participants=request.user)

        if action == "mark_read":
            for message in messages:
                MessageRead.objects.get_or_create(
                    message=message,
                    user=request.user,
                    defaults={"read_at": timezone.now()},
                )
            return JsonResponse(
                {
                    "status": "success",
                    "action": "marked_read",
                    "count": len(message_ids),
                },
            )

        if action == "delete" and messages.filter(user=request.user).count() == len(message_ids):
            messages.filter(user=request.user).delete()
            return JsonResponse({"status": "success", "action": "deleted", "count": len(message_ids)})

        return HttpResponse("Invalid action or insufficient permissions", status=400)

    except (json.JSONDecodeError, ValueError) as e:
        return HttpResponse(f"Error performing batch action: {e!s}", status=400)


@login_required
@require_http_methods(["GET"])
def message_export_htmx(request, conversation_id):
    """Export conversation messages to different formats"""
    format_type = request.GET.get("format", "json")  # json, csv, txt

    try:
        conversation = Conversation.objects.get(id=conversation_id, participants=request.user)

        messages = conversation.messages.select_related("user").order_by("created_at")

        if format_type == "json":
            data = {
                "conversation": {
                    "id": str(conversation.id),
                    "name": conversation.name,
                    "created_at": conversation.created_at.isoformat(),
                },
                "messages": [
                    {
                        "id": str(msg.id),
                        "user": msg.user.get_full_name() or msg.user.username,
                        "content": msg.content,
                        "created_at": msg.created_at.isoformat(),
                    }
                    for msg in messages
                ],
            }
            response = JsonResponse(data)
            response["Content-Disposition"] = f'attachment; filename="conversation_{conversation.id}.json"'
            return response

        if format_type == "csv":
            import csv
            from io import StringIO

            output = StringIO()
            writer = csv.writer(output)
            writer.writerow(["Timestamp", "User", "Message"])

            for msg in messages:
                writer.writerow(
                    [
                        msg.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                        msg.user.get_full_name() or msg.user.username,
                        msg.content,
                    ],
                )

            response = HttpResponse(output.getvalue(), content_type="text/csv")
            response["Content-Disposition"] = f'attachment; filename="conversation_{conversation.id}.csv"'
            return response

        # txt format
        content = f"Conversation: {conversation.name}\n"
        content += f"Exported: {timezone.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"

        for msg in messages:
            content += f"[{msg.created_at.strftime('%Y-%m-%d %H:%M:%S')}] {msg.user.get_full_name() or msg.user.username}: {msg.content}\n"

        response = HttpResponse(content, content_type="text/plain")
        response["Content-Disposition"] = f'attachment; filename="conversation_{conversation.id}.txt"'
        return response

    except Conversation.DoesNotExist:
        return HttpResponse("Conversation not found", status=404)


@login_required
@require_http_methods(["POST"])
def message_forward_htmx(request):
    """Forward messages to other conversations"""
    message_ids = request.POST.getlist("message_ids")
    target_conversation_id = request.POST.get("target_conversation_id")

    if not message_ids or not target_conversation_id:
        return HttpResponse("Message IDs and target conversation required", status=400)

    try:
        # Verify user has access to source messages
        source_messages = ChatMessage.objects.filter(
            id__in=message_ids,
            conversation__participants=request.user,
        ).select_related("user", "conversation")

        # Verify user has access to target conversation
        target_conversation = Conversation.objects.get(id=target_conversation_id, participants=request.user)

        # Create forwarded messages
        forwarded_count = 0
        for message in source_messages:
            forwarded_content = f"[Forwarded from {message.conversation.name}]\n{message.content}"

            ChatMessage.objects.create(
                user=request.user,
                conversation=target_conversation,
                content=forwarded_content,
                message_type="text",
                created_at=timezone.now(),
            )
            forwarded_count += 1

        # Update target conversation activity
        target_conversation.last_message_at = timezone.now()
        target_conversation.save()

        return JsonResponse(
            {
                "status": "success",
                "forwarded_count": forwarded_count,
                "target_conversation": target_conversation.name,
            },
        )

    except Conversation.DoesNotExist:
        return HttpResponse("Target conversation not found", status=404)
    except (ConnectionError, TimeoutError, HTTPError) as e:
        return HttpResponse(f"Error forwarding messages: {e!s}", status=400)


@login_required
@require_http_methods(["GET"])
def message_search_advanced_htmx(request):
    """Advanced message search with filters"""
    query = request.GET.get("q", "").strip()
    conversation_id = request.GET.get("conversation_id")
    user_id = request.GET.get("user_id")
    date_from = request.GET.get("date_from")
    date_to = request.GET.get("date_to")
    message_type = request.GET.get("message_type")
    has_attachments = request.GET.get("has_attachments") == "true"

    if not query and not any([conversation_id, user_id, date_from, date_to]):
        return render(
            request,
            "messaging/partials/advanced_search_results.html",
            {"results": [], "query": query},
        )

    # Build query
    messages = ChatMessage.objects.filter(conversation__participants=request.user).select_related(
        "user",
        "conversation",
    )

    if query:
        messages = messages.filter(content__icontains=query)

    if conversation_id:
        messages = messages.filter(conversation_id=conversation_id)

    if user_id:
        messages = messages.filter(user_id=user_id)

    if date_from:
        try:
            from datetime import datetime

            date_from_parsed = datetime.strptime(date_from, "%Y-%m-%d").date()
            messages = messages.filter(created_at__date__gte=date_from_parsed)
        except ValueError:
            pass

    if date_to:
        try:
            from datetime import datetime

            date_to_parsed = datetime.strptime(date_to, "%Y-%m-%d").date()
            messages = messages.filter(created_at__date__lte=date_to_parsed)
        except ValueError:
            pass

    if message_type:
        messages = messages.filter(message_type=message_type)

    if has_attachments:
        messages = messages.filter(attachments__isnull=False)

    # Limit results and order by relevance
    messages = messages.order_by("-created_at")[:50]

    # Group by conversation for better UX
    results_by_conversation = {}
    for message in messages:
        conv_id = message.conversation.id
        if conv_id not in results_by_conversation:
            results_by_conversation[conv_id] = {
                "conversation": message.conversation,
                "messages": [],
            }
        results_by_conversation[conv_id]["messages"].append(message)

    context = {
        "results": results_by_conversation.values(),
        "query": query,
        "total_results": messages.count(),
    }

    return render(request, "messaging/partials/advanced_search_results.html", context)


@login_required
@require_http_methods(["POST"])
def conversation_archive_htmx(request, conversation_id):
    """Archive/unarchive a conversation"""
    try:
        conversation = Conversation.objects.get(id=conversation_id, participants=request.user)

        # Toggle archive status
        conversation.is_active = not conversation.is_active
        conversation.save()

        action = "archived" if not conversation.is_active else "unarchived"

        return JsonResponse(
            {
                "status": "success",
                "action": action,
                "conversation_id": str(conversation.id),
            },
        )

    except Conversation.DoesNotExist:
        return HttpResponse("Conversation not found", status=404)


@login_required
@require_http_methods(["POST"])
def conversation_pin_htmx(request, conversation_id):
    """Pin/unpin a conversation for the user"""
    try:
        conversation = Conversation.objects.get(id=conversation_id, participants=request.user)

        member = conversation.members.filter(user=request.user).first()
        if member:
            # Toggle pin status (assuming we add a pinned field to ConversationMember)
            # For now, we'll use a simple approach
            is_pinned = hasattr(member, "is_pinned") and member.is_pinned
            if hasattr(member, "is_pinned"):
                member.is_pinned = not is_pinned
                member.save()

            action = "pinned" if not is_pinned else "unpinned"

            return JsonResponse(
                {
                    "status": "success",
                    "action": action,
                    "conversation_id": str(conversation.id),
                },
            )
        return HttpResponse("Not a member of this conversation", status=403)

    except Conversation.DoesNotExist:
        return HttpResponse("Conversation not found", status=404)


@login_required
@require_http_methods(["GET"])
def conversation_analytics_htmx(request, conversation_id):
    """Get analytics for a conversation"""
    try:
        conversation = Conversation.objects.get(id=conversation_id, participants=request.user)

        # Calculate analytics
        total_messages = conversation.messages.count()
        total_participants = conversation.participants.count()

        # Message distribution by user
        message_stats = (
            conversation.messages.values("user__username", "user__first_name", "user__last_name")
            .annotate(message_count=Count("id"))
            .order_by("-message_count")
        )

        # Activity timeline (messages per day for last 30 days)
        from datetime import timedelta

        thirty_days_ago = timezone.now() - timedelta(days=30)
        daily_activity = (
            conversation.messages.filter(created_at__gte=thirty_days_ago)
            .extra(select={"day": "date(created_at)"})
            .values("day")
            .annotate(count=Count("id"))
            .order_by("day")
        )

        # Most active times (by hour)
        hourly_activity = (
            conversation.messages.extra(select={"hour": "extract(hour from created_at)"})
            .values("hour")
            .annotate(count=Count("id"))
            .order_by("hour")
        )

        context = {
            "conversation": conversation,
            "total_messages": total_messages,
            "total_participants": total_participants,
            "message_stats": message_stats,
            "daily_activity": list(daily_activity),
            "hourly_activity": list(hourly_activity),
            "created_date": conversation.created_at,
            "last_activity": conversation.last_message_at,
        }

        return render(request, "messaging/partials/conversation_analytics.html", context)

    except Conversation.DoesNotExist:
        return HttpResponse("Conversation not found", status=404)


@login_required
@require_http_methods(["POST"])
def message_priority_set_htmx(request):
    """Set priority for messages"""
    message_id = request.POST.get("message_id")
    priority = request.POST.get("priority", "normal")  # low, normal, high, urgent

    if not message_id:
        return HttpResponse("Message ID required", status=400)

    if priority not in ["low", "normal", "high", "urgent"]:
        return HttpResponse("Invalid priority level", status=400)

    try:
        message = ChatMessage.objects.get(
            id=message_id,
            user=request.user,  # Only allow setting priority on own messages
        )

        # Set priority (assuming we add priority field to ChatMessage)
        if hasattr(message, "priority"):
            message.priority = priority
            message.save()

        return JsonResponse({"status": "success", "message_id": str(message.id), "priority": priority})

    except ChatMessage.DoesNotExist:
        return HttpResponse("Message not found or not yours", status=404)


# ========== TEAM COMMUNICATION FEATURES ==========


@login_required
@require_http_methods(["POST"])
def create_team_channel_htmx(request):
    """Create a new team communication channel"""
    try:
        channel_name = request.POST.get("name", "").strip()
        project_id = request.POST.get("project_id")
        request.POST.get("description", "").strip()
        request.POST.get("is_private", "false").lower() == "true"

        if not channel_name:
            return HttpResponse("Channel name is required", status=400)

        # Get project if specified
        project = None
        if project_id:
            try:
                project = Project.objects.get(id=project_id)
                # Check user has access to project
                if not user_has_project_access(request.user, project):
                    return HttpResponse("No access to project", status=403)
            except Project.DoesNotExist:
                return HttpResponse("Project not found", status=404)

        # Create channel conversation
        conversation = Conversation.objects.create(
            name=channel_name,
            conversation_type="channel",
            created_by=request.user,
            project=project,
            is_active=True,
        )

        # Add creator as admin
        ConversationMember.objects.create(
            conversation=conversation,
            user=request.user,
            is_admin=True,
            joined_at=timezone.now(),
        )

        # Add project team members if it's a project channel
        if project and hasattr(project, "team_members"):
            for member in project.team_members.all():
                if member != request.user:
                    ConversationMember.objects.create(conversation=conversation, user=member, joined_at=timezone.now())

        # Create welcome message
        ChatMessage.objects.create(
            user=request.user,
            conversation=conversation,
            content=f"Welcome to {channel_name}! This channel was created by {request.user.get_full_name()}.",
            message_type="system",
        )

        return render(
            request,
            "messaging/shared/components/messages/conversation_item.html",
            {"conversation": conversation, "user": request.user},
        )

    except Exception as e:
        logger.exception("Error creating team channel")
        return HttpResponse(f"Error creating channel: {e!s}", status=400)


@login_required
@require_http_methods(["POST"])
def join_team_channel_htmx(request, conversation_id):
    """Join a team channel"""
    try:
        conversation = Conversation.objects.get(id=conversation_id, conversation_type="channel", is_active=True)

        # Check if user can join (public channels or project members)
        can_join = True
        if conversation.project:
            can_join = user_has_project_access(request.user, conversation.project)

        if not can_join:
            return HttpResponse("Cannot join this channel", status=403)

        # Add user to channel
        member, created = ConversationMember.objects.get_or_create(
            conversation=conversation,
            user=request.user,
            defaults={"joined_at": timezone.now()},
        )

        if created:
            # Create join message
            ChatMessage.objects.create(
                user=request.user,
                conversation=conversation,
                content=f"{request.user.get_full_name()} joined the channel.",
                message_type="system",
            )

        return JsonResponse(
            {
                "status": "success",
                "message": "Joined channel successfully",
                "conversation_id": str(conversation.id),
            },
        )

    except Conversation.DoesNotExist:
        return HttpResponse("Channel not found", status=404)


@login_required
@require_http_methods(["GET"])
def team_channels_list_htmx(request):
    """Get list of available team channels"""
    try:
        # Get user's project channels
        project_channels = (
            Conversation.objects.filter(
                conversation_type="channel",
                is_active=True,
                project__in=Project.objects.filter(Q(participants=request.user) | Q(created_by=request.user)),
            )
            .select_related("project", "created_by")
            .prefetch_related("participants")
            .order_by("-last_message_at")
        )

        # Get public channels user is not in
        available_channels = (
            Conversation.objects.filter(
                conversation_type="channel",
                is_active=True,
                project__isnull=True,  # Public channels
            )
            .exclude(participants=request.user)
            .select_related("created_by")
            .order_by("-last_message_at")[:10]
        )

        context = {
            "project_channels": project_channels,
            "available_channels": available_channels,
            "user": request.user,
        }

        return render(request, "messaging/shared/components/team_channels_list.html", context)

    except Exception:
        logger.exception("Error loading team channels")
        return HttpResponse("Error loading channels", status=500)


@login_required
@require_http_methods(["GET"])
def mention_suggestions_htmx(request):
    """Get mention suggestions for current context"""
    try:
        query = request.GET.get("q", "").strip()
        conversation_id = request.GET.get("conversation_id")
        limit = int(request.GET.get("limit", 10))

        suggestions = []

        if conversation_id:
            # Get conversation participants
            try:
                conversation = Conversation.objects.get(id=conversation_id, participants=request.user)

                participants = conversation.participants.exclude(id=request.user.id)

                if query:
                    participants = participants.filter(
                        Q(username__icontains=query) | Q(first_name__icontains=query) | Q(last_name__icontains=query),
                    )

                suggestions = participants[:limit]

            except Conversation.DoesNotExist:
                pass
        elif query:
            suggestions = User.objects.filter(
                Q(username__icontains=query) | Q(first_name__icontains=query) | Q(last_name__icontains=query),
                is_active=True,
            ).exclude(id=request.user.id)[:limit]

        context = {"suggestions": suggestions, "query": query}

        return render(
            request,
            "messaging/shared/components/messages/mention_suggestions.html",
            context,
        )

    except Exception:
        logger.exception("Error getting mention suggestions")
        return HttpResponse("Error loading suggestions", status=500)


# ========== MESSAGE THREADING SYSTEM ==========


@login_required
@require_http_methods(["POST"])
def create_message_thread_htmx(request, message_id):
    """Create a new message thread"""
    try:
        root_message = ChatMessage.objects.get(id=message_id, conversation__participants=request.user)

        # Check if thread already exists
        thread, created = MessageThread.objects.get_or_create(
            root_message=root_message,
            defaults={
                "title": (
                    root_message.content[:50] + "..." if len(root_message.content) > 50 else root_message.content
                ),
            },
        )

        # Add user to thread participants
        thread.participants.add(request.user)

        return render(
            request,
            "messaging/shared/components/messages/thread_created.html",
            {"thread": thread, "root_message": root_message, "user": request.user},
        )

    except ChatMessage.DoesNotExist:
        return HttpResponse("Message not found", status=404)


@login_required
@require_http_methods(["POST"])
def reply_to_thread_htmx(request, thread_id):
    """Reply to a message thread"""
    try:
        thread = MessageThread.objects.get(id=thread_id, root_message__conversation__participants=request.user)

        content = request.POST.get("content", "").strip()
        if not content:
            return HttpResponse("Reply content required", status=400)

        # Create thread reply
        reply = ChatMessage.objects.create(
            user=request.user,
            conversation=thread.root_message.conversation,
            content=content,
            reply_to=thread.root_message,
            message_type="text",
        )

        # Update thread stats
        thread.reply_count = thread.root_message.replies.count()
        thread.last_reply_at = timezone.now()
        thread.save()

        # Add user to thread participants
        thread.participants.add(request.user)

        # Notify thread participants
        for participant in thread.participants.exclude(id=request.user.id):
            Notification.objects.create(
                recipient=participant,
                sender=request.user,
                notification_type="thread_reply",
                title="New thread reply",
                message=f"{request.user.get_full_name()} replied to a thread you're following",
                action_url=f"/messaging/?conversation={thread.root_message.conversation.id}&thread={thread.id}",
            )

        return render(
            request,
            "messaging/shared/components/messages/thread_reply.html",
            {"reply": reply, "thread": thread, "user": request.user},
        )

    except MessageThread.DoesNotExist:
        return HttpResponse("Thread not found", status=404)


@login_required
@require_http_methods(["POST"])
def thread_subscribe_htmx(request, thread_id):
    """Subscribe or unsubscribe from a message thread."""
    try:
        thread = MessageThread.objects.get(id=thread_id)
        action = request.POST.get("action", "subscribe")  # subscribe or unsubscribe

        if action == "subscribe":
            # Add user to thread participants
            thread.participants.add(request.user)
            message = "Successfully subscribed to thread"
            is_subscribed = True
        else:
            # Remove user from thread participants
            thread.participants.remove(request.user)
            message = "Successfully unsubscribed from thread"
            is_subscribed = False

        return render(
            request,
            "messaging/partials/thread_subscription_result.html",
            {
                "thread": thread,
                "is_subscribed": is_subscribed,
                "message": message,
                "success": True,
            },
        )

    except MessageThread.DoesNotExist:
        return render(
            request,
            "messaging/partials/thread_subscription_result.html",
            {
                "error": "Thread not found",
                "success": False,
            },
        )

    except Exception as e:
        return render(
            request,
            "messaging/partials/thread_subscription_result.html",
            {
                "error": f"Error updating subscription: {e!s}",
                "success": False,
            },
        )


@login_required
@require_http_methods(["GET"])
def message_thread_list_htmx(request):
    """HTMX endpoint for listing message threads."""
    try:
        # Get filter parameters
        conversation_id = request.GET.get("conversation_id")
        status = request.GET.get("status", "all")  # all, open, closed
        page = int(request.GET.get("page", 1))
        per_page = 20

        # Base queryset for threads
        threads = (
            MessageThread.objects.filter(root_message__conversation__participants=request.user)
            .select_related("root_message", "root_message__user", "root_message__conversation")
            .prefetch_related("participants")
        )

        # Filter by conversation if specified
        if conversation_id:
            threads = threads.filter(root_message__conversation_id=conversation_id)

        # Filter by status
        if status == "open":
            threads = threads.filter(is_closed=False)
        elif status == "closed":
            threads = threads.filter(is_closed=True)

        # Order by last activity
        threads = threads.order_by("-last_reply_at", "-created_at")

        # Paginate
        from django.core.paginator import Paginator

        paginator = Paginator(threads, per_page)
        page_obj = paginator.get_page(page)

        return render(
            request,
            "messaging/partials/thread_list.html",
            {
                "threads": page_obj,
                "status_filter": status,
                "conversation_id": conversation_id,
                "has_next": page_obj.has_next(),
                "has_previous": page_obj.has_previous(),
                "page_number": page_obj.number,
                "total_pages": paginator.num_pages,
            },
        )

    except Exception as e:
        return render(
            request,
            "messaging/partials/thread_list.html",
            {
                "error": f"Error loading threads: {e!s}",
                "threads": [],
            },
        )


@login_required
@require_http_methods(["GET"])
def unread_threads_htmx(request):
    """HTMX endpoint for listing unread message threads."""
    try:
        # Get filter parameters
        conversation_id = request.GET.get("conversation_id")
        page = int(request.GET.get("page", 1))
        per_page = 20

        # Base queryset for unread threads
        # A thread is considered unread if:
        # 1. User is a participant
        # 2. There are replies after the user's last read timestamp
        # 3. The thread is not closed (unless user wants to see closed unread)
        threads = (
            MessageThread.objects.filter(participants=request.user, is_closed=False, last_reply_at__isnull=False)
            .select_related("root_message", "root_message__user", "root_message__conversation")
            .prefetch_related("participants")
        )

        # Filter by conversation if specified
        if conversation_id:
            threads = threads.filter(root_message__conversation_id=conversation_id)

        # Order by last activity (most recent first)
        threads = threads.order_by("-last_reply_at")

        # Paginate
        from django.core.paginator import Paginator

        paginator = Paginator(threads, per_page)
        page_obj = paginator.get_page(page)

        # Get unread count for each thread (placeholder logic)
        for thread in page_obj:
            # In a real implementation, this would check against user's read timestamps
            thread.unread_count = thread.reply_count  # Placeholder

        return render(
            request,
            "messaging/partials/unread_threads_list.html",
            {
                "threads": page_obj,
                "conversation_id": conversation_id,
                "has_next": page_obj.has_next(),
                "has_previous": page_obj.has_previous(),
                "page_number": page_obj.number,
                "total_pages": paginator.num_pages,
                "total_unread": threads.count(),
            },
        )

    except Exception as e:
        return render(
            request,
            "messaging/partials/unread_threads_list.html",
            {
                "error": f"Error loading unread threads: {e!s}",
                "threads": [],
                "total_unread": 0,
            },
        )


@login_required
@require_http_methods(["POST"])
def leave_team_channel_htmx(request, conversation_id):
    """HTMX endpoint for leaving a team channel."""
    try:
        conversation = Conversation.objects.get(
            id=conversation_id,
            participants=request.user,
            conversation_type="team_channel",
        )

        # Remove user from conversation participants
        conversation.participants.remove(request.user)

        # Create a system message about the user leaving
        ChatMessage.objects.create(
            conversation=conversation,
            user=request.user,
            content=f"{request.user.get_full_name()} left the channel",
            message_type="system",
        )

        # Notify remaining participants
        for participant in conversation.participants.exclude(id=request.user.id):
            Notification.objects.create(
                recipient=participant,
                sender=request.user,
                notification_type="channel_leave",
                title="User left channel",
                message=f"{request.user.get_full_name()} left {conversation.name}",
                action_url=f"/messaging/?conversation={conversation.id}",
            )

        return render(
            request,
            "messaging/partials/channel_leave_result.html",
            {
                "conversation": conversation,
                "message": f"Successfully left {conversation.name}",
                "success": True,
            },
        )

    except Conversation.DoesNotExist:
        return render(
            request,
            "messaging/partials/channel_leave_result.html",
            {
                "error": "Channel not found or you're not a member",
                "success": False,
            },
        )

    except Exception as e:
        return render(
            request,
            "messaging/partials/channel_leave_result.html",
            {
                "error": f"Error leaving channel: {e!s}",
                "success": False,
            },
        )


@login_required
@require_http_methods(["GET", "POST"])
def invite_to_channel_htmx(request, conversation_id):
    """HTMX endpoint for inviting users to a team channel."""
    try:
        conversation = Conversation.objects.get(
            id=conversation_id,
            participants=request.user,
            conversation_type="team_channel",
        )

        if request.method == "GET":
            # Return the invite form
            # Get users in the same organization who aren't already in the channel
            available_users = conversation.organization.users.exclude(id__in=conversation.participants.all()).exclude(
                id=request.user.id
            )

            return render(
                request,
                "messaging/partials/channel_invite_form.html",
                {
                    "conversation": conversation,
                    "available_users": available_users,
                },
            )

        if request.method == "POST":
            # Process the invitation
            user_ids = request.POST.getlist("user_ids")

            if not user_ids:
                return render(
                    request,
                    "messaging/partials/channel_invite_result.html",
                    {
                        "error": "Please select at least one user to invite",
                        "success": False,
                    },
                )

            # Add users to the channel
            invited_users = []
            for user_id in user_ids:
                try:
                    user = conversation.organization.users.get(id=user_id)
                    conversation.participants.add(user)
                    invited_users.append(user)

                    # Create notification for invited user
                    Notification.objects.create(
                        recipient=user,
                        sender=request.user,
                        notification_type="channel_invite",
                        title="Channel invitation",
                        message=f"{request.user.get_full_name()} invited you to {conversation.name}",
                        action_url=f"/messaging/?conversation={conversation.id}",
                    )

                    # Create system message
                    ChatMessage.objects.create(
                        conversation=conversation,
                        user=request.user,
                        content=f"{request.user.get_full_name()} invited {user.get_full_name()} to the channel",
                        message_type="system",
                    )

                except Exception:
                    continue  # Skip invalid user IDs

            return render(
                request,
                "messaging/partials/channel_invite_result.html",
                {
                    "conversation": conversation,
                    "invited_users": invited_users,
                    "message": f"Successfully invited {len(invited_users)} user(s) to {conversation.name}",
                    "success": True,
                },
            )

    except Conversation.DoesNotExist:
        return render(
            request,
            "messaging/partials/channel_invite_result.html",
            {
                "error": "Channel not found or you don't have permission",
                "success": False,
            },
        )

    except Exception as e:
        return render(
            request,
            "messaging/partials/channel_invite_result.html",
            {
                "error": f"Error processing invitation: {e!s}",
                "success": False,
            },
        )


class ProjectChannelsView(LoginRequiredMixin, RoleRequiredMixin, TemplateView):
    """View for managing project-specific channels."""

    required_roles = ["stakeholder"]
    template_name = "messaging/project_channels.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        project_id = self.kwargs.get("project_id")

        try:
            # Get the project (assuming there's a Project model)
            from apps.projects.models import Project

            project = Project.objects.get(id=project_id, organization=self.request.user.organization)

            # Get project channels
            project_channels = (
                Conversation.objects.filter(
                    conversation_type="team_channel",
                    project_id=project_id,
                    participants=self.request.user,
                )
                .select_related("created_by")
                .prefetch_related("participants")
            )

            # Get channel statistics
            channel_stats = {}
            for channel in project_channels:
                channel_stats[channel.id] = {
                    "member_count": channel.participants.count(),
                    "message_count": (channel.messages.count() if hasattr(channel, "messages") else 0),
                    "last_activity": channel.updated_at,
                }

            context.update(
                {
                    "project": project,
                    "project_channels": project_channels,
                    "channel_stats": channel_stats,
                    "can_create_channels": self.request.user.get_role_level(self.request.user.organization.id)
                    <= 30,  # Department Manager or higher
                }
            )

        except Exception as e:
            context.update(
                {
                    "error": f"Error loading project channels: {e!s}",
                    "project_channels": [],
                    "channel_stats": {},
                }
            )

        return context

    def post(self, request, *args, **kwargs):
        """Handle channel creation for the project."""
        try:
            project_id = self.kwargs.get("project_id")
            channel_name = request.POST.get("channel_name", "").strip()
            channel_description = request.POST.get("channel_description", "").strip()

            if not channel_name:
                return JsonResponse({"success": False, "error": "Channel name is required"})

            # Create the channel
            channel = Conversation.objects.create(
                name=channel_name,
                description=channel_description,
                conversation_type="team_channel",
                project_id=project_id,
                organization=request.user.organization,
                created_by=request.user,
            )

            # Add creator as participant
            channel.participants.add(request.user)

            return JsonResponse(
                {
                    "success": True,
                    "message": f"Channel '{channel_name}' created successfully",
                    "channel_id": str(channel.id),
                }
            )

        except Exception as e:
            return JsonResponse({"success": False, "error": f"Error creating channel: {e!s}"})


@login_required
@require_http_methods(["GET"])
def team_status_partial(request):
    """HTMX partial for team status overview."""
    try:
        # Get project ID if provided
        project_id = request.GET.get("project_id")

        # Get user's organization
        organization = request.user.organization

        # Get team members and their status
        team_members = organization.users.select_related("profile").prefetch_related("user_roles")

        # Get recent activity for team members
        member_status = []
        for member in team_members:
            # Get last activity (placeholder logic)
            last_activity = timezone.now() - timezone.timedelta(minutes=30)  # Placeholder

            # Determine online status
            is_online = (timezone.now() - last_activity).total_seconds() < 300  # 5 minutes

            member_status.append(
                {
                    "user": member,
                    "is_online": is_online,
                    "last_activity": last_activity,
                    "current_project": project_id if project_id else None,
                }
            )

        # Get team statistics
        team_stats = {
            "total_members": team_members.count(),
            "online_members": sum(1 for status in member_status if status["is_online"]),
            "active_conversations": Conversation.objects.filter(
                organization=organization, participants__in=team_members
            ).count(),
            "recent_messages": ChatMessage.objects.filter(
                conversation__organization=organization,
                created_at__gte=timezone.now() - timezone.timedelta(hours=24),
            ).count(),
        }

        return render(
            request,
            "messaging/partials/team_status.html",
            {
                "member_status": member_status,
                "team_stats": team_stats,
                "project_id": project_id,
                "organization": organization,
            },
        )

    except Exception as e:
        return render(
            request,
            "messaging/partials/team_status.html",
            {
                "error": f"Error loading team status: {e!s}",
                "member_status": [],
                "team_stats": {},
            },
        )


@login_required
@require_http_methods(["GET"])
def whisper_detail_htmx(request, whisper_id):
    """HTMX endpoint for getting whisper message details."""
    try:
        whisper = WhisperMessage.objects.get(id=whisper_id, recipient=request.user)

        # Mark as read if not already read
        if not whisper.is_read:
            whisper.is_read = True
            whisper.read_at = timezone.now()
            whisper.save()

        return render(
            request,
            "messaging/partials/whisper_detail.html",
            {
                "whisper": whisper,
                "can_reply": True,
            },
        )

    except WhisperMessage.DoesNotExist:
        return render(
            request,
            "messaging/partials/whisper_detail.html",
            {
                "error": "Whisper message not found",
                "whisper": None,
            },
        )

    except Exception as e:
        return render(
            request,
            "messaging/partials/whisper_detail.html",
            {
                "error": f"Error loading whisper: {e!s}",
                "whisper": None,
            },
        )


@login_required
@require_http_methods(["GET", "POST"])
def whisper_reply_htmx(request, whisper_id):
    """HTMX endpoint for replying to a whisper message."""
    try:
        original_whisper = WhisperMessage.objects.get(id=whisper_id, recipient=request.user)

        if request.method == "GET":
            # Return the reply form
            return render(
                request,
                "messaging/partials/whisper_reply_form.html",
                {
                    "original_whisper": original_whisper,
                },
            )

        if request.method == "POST":
            # Process the reply
            content = request.POST.get("content", "").strip()

            if not content:
                return render(
                    request,
                    "messaging/partials/whisper_reply_result.html",
                    {
                        "error": "Reply content is required",
                        "success": False,
                    },
                )

            # Create the reply whisper
            reply_whisper = WhisperMessage.objects.create(
                sender=request.user,
                recipient=original_whisper.sender,
                content=content,
                reply_to=original_whisper,
            )

            # Create notification for the original sender
            Notification.objects.create(
                recipient=original_whisper.sender,
                sender=request.user,
                notification_type="whisper_reply",
                title="Whisper reply",
                message=f"{request.user.get_full_name()} replied to your whisper",
                action_url=f"/messaging/whispers/{reply_whisper.id}/",
            )

            return render(
                request,
                "messaging/partials/whisper_reply_result.html",
                {
                    "reply_whisper": reply_whisper,
                    "original_whisper": original_whisper,
                    "message": "Reply sent successfully",
                    "success": True,
                },
            )

    except WhisperMessage.DoesNotExist:
        return render(
            request,
            "messaging/partials/whisper_reply_result.html",
            {
                "error": "Original whisper not found",
                "success": False,
            },
        )

    except Exception as e:
        return render(
            request,
            "messaging/partials/whisper_reply_result.html",
            {
                "error": f"Error sending reply: {e!s}",
                "success": False,
            },
        )


@login_required
@require_http_methods(["GET"])
def my_whisper_conversations_htmx(request):
    """
    HTMX endpoint for getting user's whisper conversations.
    Returns conversations grouped by partner with unread counts.
    """
    try:
        user = request.user

        # Get all whisper messages for the user (sent and received)
        sent_whispers = WhisperMessage.objects.filter(sender=user).select_related("recipient")
        received_whispers = WhisperMessage.objects.filter(recipient=user).select_related("sender")

        # Group conversations by partner
        whisper_conversations = {}

        # Process sent messages
        for whisper in sent_whispers:
            partner = whisper.recipient
            if partner.id not in whisper_conversations:
                whisper_conversations[partner.id] = {
                    "partner": partner,
                    "last_message": whisper,
                    "unread_count": 0,
                    "messages": [],
                }
            whisper_conversations[partner.id]["messages"].append(whisper)

        for whisper in received_whispers:
            partner = whisper.sender
            if partner.id not in whisper_conversations:
                whisper_conversations[partner.id] = {
                    "partner": partner,
                    "last_message": whisper,
                    "unread_count": 0,
                    "messages": [],
                }
            else:
                # Update last message if this is more recent
                if whisper.created_at > whisper_conversations[partner.id]["last_message"].created_at:
                    whisper_conversations[partner.id]["last_message"] = whisper

            whisper_conversations[partner.id]["messages"].append(whisper)

            # Count unread messages
            if not whisper.is_read:
                whisper_conversations[partner.id]["unread_count"] += 1

        # Sort conversations by last message time
        sorted_conversations = sorted(
            whisper_conversations.values(),
            key=lambda x: x["last_message"].created_at,
            reverse=True,
        )

        return render(
            request,
            "messaging/partials/whisper_conversations_list.html",
            {
                "conversations": sorted_conversations,
                "total_conversations": len(sorted_conversations),
                "total_unread": sum(conv["unread_count"] for conv in sorted_conversations),
            },
        )

    except Exception as e:
        return render(
            request,
            "messaging/partials/whisper_conversations_list.html",
            {
                "error": f"Error loading conversations: {e!s}",
                "conversations": [],
                "total_conversations": 0,
                "total_unread": 0,
            },
        )


@login_required
@require_http_methods(["POST"])
def whisper_conversation_archive_htmx(request, user_id):
    """
    HTMX endpoint for archiving a whisper conversation.
    Marks all messages in the conversation as archived for the current user.
    """
    try:
        user = request.user
        partner_id = int(user_id)

        # Get the partner user
        try:
            partner = User.objects.get(id=partner_id, organization=user.organization)
        except User.DoesNotExist:
            return JsonResponse({"success": False, "error": "Partner not found"}, status=404)

        # Get all whisper messages between the users
        whisper_messages = WhisperMessage.objects.filter(
            Q(sender=user, recipient=partner) | Q(sender=partner, recipient=user)
        )

        # Archive the messages (this would typically involve adding an archived field)
        # For now, we'll use a placeholder implementation
        archived_count = whisper_messages.count()

        # In a real implementation, you might:
        # whisper_messages.update(archived_by_user=user, archived_at=timezone.now())

        logger.info(f"User {user.email} archived whisper conversation with {partner.email}")

        return render(
            request,
            "messaging/partials/whisper_conversation_archived.html",
            {
                "success": True,
                "partner": partner,
                "archived_count": archived_count,
                "message": f"Conversation with {partner.get_full_name()} has been archived.",
            },
        )

    except ValueError:
        return JsonResponse({"success": False, "error": "Invalid user ID"}, status=400)
    except Exception as e:
        logger.error(f"Error archiving whisper conversation: {e}")
        return JsonResponse(
            {"success": False, "error": f"Error archiving conversation: {e!s}"},
            status=500,
        )


@login_required
@require_http_methods(["GET"])
def whisper_contacts_htmx(request):
    """
    HTMX endpoint for getting contacts available for whisper messaging.
    Returns users in the same organization who can receive whispers.
    """
    try:
        user = request.user
        search_query = request.GET.get("search", "").strip()

        # Get all users in the same organization
        contacts_query = (
            User.objects.filter(organization=user.organization, is_active=True)
            .exclude(id=user.id)
            .select_related("organization")
        )

        # Apply search filter if provided
        if search_query:
            contacts_query = contacts_query.filter(
                Q(first_name__icontains=search_query)
                | Q(last_name__icontains=search_query)
                | Q(email__icontains=search_query)
            )

        # Order by name
        contacts = contacts_query.order_by("first_name", "last_name")[:50]  # Limit to 50 results

        # Get recent whisper conversations for each contact
        contacts_data = []
        for contact in contacts:
            # Get last whisper between users
            last_whisper = (
                WhisperMessage.objects.filter(Q(sender=user, recipient=contact) | Q(sender=contact, recipient=user))
                .order_by("-created_at")
                .first()
            )

            # Count unread whispers from this contact
            unread_count = WhisperMessage.objects.filter(sender=contact, recipient=user, is_read=False).count()

            contacts_data.append(
                {
                    "contact": contact,
                    "last_whisper": last_whisper,
                    "unread_count": unread_count,
                    "last_activity": last_whisper.created_at if last_whisper else None,
                }
            )

        # Sort by last activity (most recent first)
        contacts_data.sort(
            key=lambda x: (x["last_activity"] if x["last_activity"] else timezone.now().replace(year=1900)),
            reverse=True,
        )

        return render(
            request,
            "messaging/partials/whisper_contacts_list.html",
            {
                "contacts": contacts_data,
                "search_query": search_query,
                "total_contacts": len(contacts_data),
                "total_unread": sum(contact["unread_count"] for contact in contacts_data),
            },
        )

    except Exception as e:
        logger.error(f"Error loading whisper contacts: {e}")
        return render(
            request,
            "messaging/partials/whisper_contacts_list.html",
            {
                "error": f"Error loading contacts: {e!s}",
                "contacts": [],
                "search_query": search_query if "search_query" in locals() else "",
                "total_contacts": 0,
                "total_unread": 0,
            },
        )


@login_required
@require_http_methods(["GET"])
def unread_mentions_htmx(request):
    """
    HTMX endpoint for getting unread mentions for the current user.
    Returns messages where the user has been mentioned but hasn't read yet.
    """
    try:
        user = request.user

        # Get unread mentions for the user
        unread_mentions = (
            MessageMention.objects.filter(mentioned_user=user, is_read=False)
            .select_related("message", "message__sender", "message__conversation")
            .order_by("-message__created_at")[:20]
        )  # Limit to 20 most recent

        # Get count of total unread mentions
        total_unread = MessageMention.objects.filter(mentioned_user=user, is_read=False).count()

        # Prepare mention data with additional context
        mentions_data = []
        for mention in unread_mentions:
            message = mention.message
            mentions_data.append(
                {
                    "mention": mention,
                    "message": message,
                    "sender": message.sender,
                    "conversation": message.conversation,
                    "time_ago": timezone.now() - message.created_at,
                    "snippet": (message.content[:100] + "..." if len(message.content) > 100 else message.content),
                }
            )

        return render(
            request,
            "messaging/partials/unread_mentions_list.html",
            {
                "mentions": mentions_data,
                "total_unread": total_unread,
                "user": user,
            },
        )

    except Exception as e:
        logger.error(f"Error loading unread mentions: {e}")
        return render(
            request,
            "messaging/partials/unread_mentions_list.html",
            {
                "error": f"Error loading mentions: {e!s}",
                "mentions": [],
                "total_unread": 0,
                "user": user if "user" in locals() else None,
            },
        )


@login_required
@require_http_methods(["POST"])
def mention_mark_read_htmx(request, mention_id):
    """
    HTMX endpoint for marking a specific mention as read.
    Updates the mention's read status and returns updated mention data.
    """
    try:
        user = request.user

        # Get the mention and verify it belongs to the current user
        try:
            mention = MessageMention.objects.select_related("message", "message__sender", "message__conversation").get(
                id=mention_id, mentioned_user=user
            )
        except MessageMention.DoesNotExist:
            return JsonResponse({"success": False, "error": "Mention not found"}, status=404)

        # Mark the mention as read
        mention.is_read = True
        mention.read_at = timezone.now()
        mention.save()

        # Log the action
        logger.info(f"User {user.email} marked mention {mention_id} as read")

        # Return updated mention data
        return render(
            request,
            "messaging/partials/mention_read_success.html",
            {
                "success": True,
                "mention": mention,
                "message": mention.message,
                "sender": mention.message.sender,
                "conversation": mention.message.conversation,
                "read_at": mention.read_at,
            },
        )

    except Exception as e:
        logger.error(f"Error marking mention as read: {e}")
        return JsonResponse(
            {"success": False, "error": f"Error marking mention as read: {e!s}"},
            status=500,
        )


@login_required
@require_http_methods(["GET"])
def online_users_htmx(request):
    """
    HTMX endpoint for getting currently online users in the organization.
    Returns list of users who are currently active/online.
    """
    try:
        user = request.user

        # Get users from the same organization who are currently online
        # This would typically check a last_activity field or online status
        # For now, we'll use a placeholder implementation

        # Get all active users in the organization
        organization_users = (
            User.objects.filter(organization=user.organization, is_active=True)
            .exclude(id=user.id)
            .select_related("organization")
        )

        # In a real implementation, you might filter by:
        # - last_activity within last 5-10 minutes
        # - online_status field
        # - active sessions

        # For placeholder, we'll show recent users (simulate online status)
        online_users = organization_users.order_by("-last_login")[:20]

        # Prepare user data with additional context
        users_data = []
        for online_user in online_users:
            # Get last activity (placeholder)
            last_activity = online_user.last_login if online_user.last_login else timezone.now()

            # Calculate if user is "online" (within last 10 minutes)
            is_online = (timezone.now() - last_activity).total_seconds() < 600 if last_activity else False

            users_data.append(
                {
                    "user": online_user,
                    "is_online": is_online,
                    "last_activity": last_activity,
                    "status": "online" if is_online else "away",
                    "avatar_url": getattr(online_user, "avatar", None),
                }
            )

        # Sort by online status first, then by last activity
        users_data.sort(
            key=lambda x: (
                not x["is_online"],
                x["last_activity"] or timezone.now().replace(year=1900),
            ),
            reverse=True,
        )

        return render(
            request,
            "messaging/partials/online_users_list.html",
            {
                "online_users": users_data,
                "total_online": sum(1 for u in users_data if u["is_online"]),
                "total_users": len(users_data),
                "current_user": user,
            },
        )

    except Exception as e:
        logger.error(f"Error loading online users: {e}")
        return render(
            request,
            "messaging/partials/online_users_list.html",
            {
                "error": f"Error loading online users: {e!s}",
                "online_users": [],
                "total_online": 0,
                "total_users": 0,
                "current_user": user if "user" in locals() else None,
            },
        )


@login_required
@require_http_methods(["GET", "POST"])
def typing_indicator_htmx(request):
    """
    HTMX endpoint for handling typing indicators in conversations.
    GET: Returns current typing users in a conversation
    POST: Updates typing status for the current user
    """
    try:
        user = request.user
        conversation_id = request.GET.get("conversation_id") or request.POST.get("conversation_id")

        if not conversation_id:
            return JsonResponse({"success": False, "error": "Conversation ID is required"}, status=400)

        # Verify user has access to the conversation
        try:
            conversation = Conversation.objects.get(id=conversation_id, organization=user.organization)
        except Conversation.DoesNotExist:
            return JsonResponse({"success": False, "error": "Conversation not found"}, status=404)

        # Initialize cache service
        from apps.messaging.services.message_cache import MessageCacheService

        cache_service = MessageCacheService()

        if request.method == "POST":
            # Update typing status
            is_typing = request.POST.get("is_typing", "false").lower() == "true"

            # Update typing status in cache
            cache_service.set_typing_status(conversation_id, user.id, is_typing)

            # Broadcast typing event via SSE
            from asgiref.sync import async_to_sync
            from channels.layers import get_channel_layer

            channel_layer = get_channel_layer()
            if channel_layer:
                group_name = f"typing_{conversation_id}"
                async_to_sync(channel_layer.group_send)(
                    group_name,
                    {
                        "type": "typing_update",
                        "user_id": user.id,
                        "username": user.username,
                        "typing": is_typing,
                        "timestamp": timezone.now().isoformat(),
                    },
                )

            logger.info(f"User {user.email} typing status in conversation {conversation_id}: {is_typing}")

            return JsonResponse(
                {
                    "success": True,
                    "is_typing": is_typing,
                    "user_id": user.id,
                    "conversation_id": conversation_id,
                    "timestamp": timezone.now().isoformat(),
                }
            )

        # GET request - Return current typing users
        typing_user_data = cache_service.get_typing_users(conversation_id)

        # Get actual user objects for the template
        from apps.authentication.models import User

        typing_user_ids = [data["user_id"] for data in typing_user_data]
        typing_users = User.objects.filter(id__in=typing_user_ids).exclude(id=user.id)

        return render(
            request,
            "common/htmx_partials/realtime/typing_indicator.html",
            {
                "typing_users": typing_users,
                "conversation": conversation,
                "current_user": user,
                "typing_count": len(typing_users),
            },
        )

    except Exception as e:
        logger.error(f"Error handling typing indicator: {e}")
        if request.method == "POST":
            return JsonResponse(
                {"success": False, "error": f"Error updating typing status: {e!s}"},
                status=500,
            )
        return render(
            request,
            "messaging/partials/typing_indicator.html",
            {
                "error": f"Error loading typing indicator: {e!s}",
                "typing_users": [],
                "conversation": None,
                "current_user": user if "user" in locals() else None,
                "typing_count": 0,
            },
        )


@login_required
@require_http_methods(["POST"])
def update_user_presence_htmx(request):
    """
    HTMX endpoint for updating user presence status.
    Updates the user's online/away/busy status and last activity timestamp.
    """
    try:
        user = request.user
        presence_status = request.POST.get("presence_status", "online")

        # Validate presence status
        valid_statuses = ["online", "away", "busy", "offline"]
        if presence_status not in valid_statuses:
            return JsonResponse(
                {
                    "success": False,
                    "error": f"Invalid presence status. Must be one of: {', '.join(valid_statuses)}",
                },
                status=400,
            )

        # In a real implementation, you might:
        # - Update user model with presence status
        # - Store in cache with expiration
        # - Broadcast presence change via WebSocket
        # - Update last_activity timestamp

        # For placeholder implementation, we'll just log and return success
        logger.info(f"User {user.email} updated presence to: {presence_status}")

        # Simulate updating user's last activity
        # user.last_activity = timezone.now()
        # user.presence_status = presence_status
        # user.save(update_fields=['last_activity', 'presence_status'])

        # Return updated presence data
        return render(
            request,
            "messaging/partials/presence_updated.html",
            {
                "success": True,
                "user": user,
                "presence_status": presence_status,
                "status_display": presence_status.title(),
                "updated_at": timezone.now(),
                "status_color": {
                    "online": "success",
                    "away": "warning",
                    "busy": "danger",
                    "offline": "secondary",
                }.get(presence_status, "secondary"),
            },
        )

    except Exception as e:
        logger.error(f"Error updating user presence: {e}")
        return JsonResponse({"success": False, "error": f"Error updating presence: {e!s}"}, status=500)


@login_required
@require_http_methods(["GET"])
def conversation_files_htmx(request):
    """
    HTMX endpoint for getting files shared in conversations.
    Returns list of files/attachments from messages in conversations the user has access to.
    """
    try:
        user = request.user
        conversation_id = request.GET.get("conversation_id")
        file_type = request.GET.get("file_type", "all")  # all, images, documents, etc.

        # Build base query for messages with files
        messages_query = (
            ChatMessage.objects.filter(sender__organization=user.organization)
            .exclude(file_attachment__isnull=True)
            .exclude(file_attachment__exact="")
            .select_related("sender", "conversation")
            .order_by("-created_at")
        )

        # Filter by specific conversation if provided
        if conversation_id:
            try:
                conversation = Conversation.objects.get(id=conversation_id, organization=user.organization)
                messages_query = messages_query.filter(conversation=conversation)
            except Conversation.DoesNotExist:
                return JsonResponse({"success": False, "error": "Conversation not found"}, status=404)

        # Filter by file type if specified
        if file_type != "all":
            if file_type == "images":
                messages_query = messages_query.filter(file_attachment__iregex=r"\.(jpg|jpeg|png|gif|bmp|svg)$")
            elif file_type == "documents":
                messages_query = messages_query.filter(file_attachment__iregex=r"\.(pdf|doc|docx|txt|rtf)$")
            elif file_type == "spreadsheets":
                messages_query = messages_query.filter(file_attachment__iregex=r"\.(xls|xlsx|csv)$")

        # Limit results for performance
        messages_with_files = messages_query[:50]

        # Prepare file data
        files_data = []
        for message in messages_with_files:
            if message.file_attachment:
                # Extract file info
                file_name = message.file_attachment.split("/")[-1] if message.file_attachment else "Unknown"
                file_extension = file_name.split(".")[-1].lower() if "." in file_name else ""

                files_data.append(
                    {
                        "message": message,
                        "file_url": message.file_attachment,
                        "file_name": file_name,
                        "file_extension": file_extension,
                        "file_type": file_type,
                        "sender": message.sender,
                        "conversation": message.conversation,
                        "uploaded_at": message.created_at,
                        "file_size": getattr(message, "file_size", None),  # If available
                    }
                )

        return render(
            request,
            "messaging/partials/conversation_files_list.html",
            {
                "files": files_data,
                "total_files": len(files_data),
                "file_type_filter": file_type,
                "conversation_id": conversation_id,
                "current_user": user,
            },
        )

    except Exception as e:
        logger.error(f"Error loading conversation files: {e}")
        return render(
            request,
            "messaging/partials/conversation_files_list.html",
            {
                "error": f"Error loading files: {e!s}",
                "files": [],
                "total_files": 0,
                "file_type_filter": file_type if "file_type" in locals() else "all",
                "conversation_id": (conversation_id if "conversation_id" in locals() else None),
                "current_user": user if "user" in locals() else None,
            },
        )


@login_required
@require_http_methods(["GET"])
def file_download_htmx(request, file_id):
    """
    HTMX endpoint for downloading files from messages.
    Handles secure file downloads with proper authorization checks.
    """
    try:
        user = request.user

        # Find the message with the file attachment
        try:
            message = ChatMessage.objects.select_related("sender", "conversation").get(
                id=file_id, sender__organization=user.organization
            )
        except ChatMessage.DoesNotExist:
            return JsonResponse({"success": False, "error": "File not found"}, status=404)

        # Check if message has file attachment
        if not message.file_attachment:
            return JsonResponse({"success": False, "error": "No file attachment found"}, status=404)

        # Verify user has access to the conversation
        if message.conversation and message.conversation.organization != user.organization:
            return JsonResponse({"success": False, "error": "Access denied"}, status=403)

        # In a real implementation, you would:
        # 1. Validate file exists on storage
        # 2. Check user permissions for the conversation
        # 3. Return proper file response with correct headers
        # 4. Log download activity
        # 5. Handle different storage backends (S3, local, etc.)

        # For placeholder implementation, return file info
        file_path = message.file_attachment
        file_name = file_path.split("/")[-1] if file_path else "unknown_file"

        logger.info(f"User {user.email} downloading file: {file_name} from message {file_id}")

        # Return download info (in real implementation, this would be a file response)
        return render(
            request,
            "messaging/partials/file_download_info.html",
            {
                "success": True,
                "message": message,
                "file_path": file_path,
                "file_name": file_name,
                "file_url": file_path,  # In real implementation, this might be a signed URL
                "sender": message.sender,
                "conversation": message.conversation,
                "download_timestamp": timezone.now(),
            },
        )

    except Exception as e:
        logger.error(f"Error downloading file: {e}")
        return JsonResponse({"success": False, "error": f"Error downloading file: {e!s}"}, status=500)


@login_required
@require_http_methods(["GET"])
def file_preview_htmx(request, file_id):
    """
    HTMX endpoint for previewing files from messages.
    Returns file preview content for supported file types (images, PDFs, text files).
    """
    try:
        user = request.user

        # Find the message with the file attachment
        try:
            message = ChatMessage.objects.select_related("sender", "conversation").get(
                id=file_id, sender__organization=user.organization
            )
        except ChatMessage.DoesNotExist:
            return JsonResponse({"success": False, "error": "File not found"}, status=404)

        # Check if message has file attachment
        if not message.file_attachment:
            return JsonResponse({"success": False, "error": "No file attachment found"}, status=404)

        # Verify user has access to the conversation
        if message.conversation and message.conversation.organization != user.organization:
            return JsonResponse({"success": False, "error": "Access denied"}, status=403)

        # Extract file information
        file_path = message.file_attachment
        file_name = file_path.split("/")[-1] if file_path else "unknown_file"
        file_extension = file_name.split(".")[-1].lower() if "." in file_name else ""

        # Determine file type and preview capability
        image_extensions = ["jpg", "jpeg", "png", "gif", "bmp", "svg", "webp"]
        document_extensions = ["pdf", "txt", "md", "csv"]
        video_extensions = ["mp4", "webm", "ogg", "avi", "mov"]
        audio_extensions = ["mp3", "wav", "ogg", "aac", "m4a"]

        preview_type = "unknown"
        can_preview = False

        if file_extension in image_extensions:
            preview_type = "image"
            can_preview = True
        elif file_extension in document_extensions:
            preview_type = "document"
            can_preview = True
        elif file_extension in video_extensions:
            preview_type = "video"
            can_preview = True
        elif file_extension in audio_extensions:
            preview_type = "audio"
            can_preview = True

        logger.info(f"User {user.email} previewing file: {file_name} (type: {preview_type}) from message {file_id}")

        # Return preview data
        return render(
            request,
            "messaging/partials/file_preview.html",
            {
                "success": True,
                "message": message,
                "file_path": file_path,
                "file_name": file_name,
                "file_extension": file_extension,
                "file_url": file_path,
                "preview_type": preview_type,
                "can_preview": can_preview,
                "sender": message.sender,
                "conversation": message.conversation,
                "file_size": getattr(message, "file_size", None),
                "uploaded_at": message.created_at,
            },
        )

    except Exception as e:
        logger.error(f"Error previewing file: {e}")
        return JsonResponse({"success": False, "error": f"Error previewing file: {e!s}"}, status=500)


@login_required
@require_http_methods(["GET", "POST"])
def file_share_htmx(request, file_id):
    """
    HTMX endpoint for sharing files with users or channels.

    GET: Show file sharing form
    POST: Process file sharing
    """
    try:
        # Find the file/message containing the file
        # This could be a ChatMessage with a file attachment or a Document
        message = None
        document = None

        # Try to find as a message attachment first
        try:
            message = ChatMessage.objects.get(id=file_id, conversation__members=request.user, file__isnull=False)
        except ChatMessage.DoesNotExist:
            # Try to find as a document
            try:
                document = Document.objects.filter(id=file_id, organization=request.user.organization).first()
            except Document.DoesNotExist:
                pass

        if not message and not document:
            return JsonResponse({"success": False, "error": "File not found"}, status=404)

        # Check permissions
        if message and message.conversation.organization != request.user.organization:
            return JsonResponse({"success": False, "error": "Access denied"}, status=403)

        if document and document.organization != request.user.organization:
            return JsonResponse({"success": False, "error": "Access denied"}, status=403)

        if request.method == "GET":
            # Get available users and conversations for sharing
            organization_users = User.objects.filter(organization=request.user.organization).exclude(id=request.user.id)

            user_conversations = Conversation.objects.filter(
                members=request.user, organization=request.user.organization
            )

            context = {
                "file_id": file_id,
                "file_name": message.file.name if message else document.title,
                "file_type": (getattr(message, "file_type", None) if message else "document"),
                "organization_users": organization_users,
                "conversations": user_conversations,
            }

            return render(request, "messaging/partials/file_share_form.html", context)

        if request.method == "POST":
            # Process file sharing
            share_with_users = request.POST.getlist("share_with_users")
            share_with_conversations = request.POST.getlist("share_with_conversations")
            share_message = request.POST.get("share_message", "")

            shared_count = 0

            # Share with individual users (create whisper messages)
            for user_id in share_with_users:
                try:
                    target_user = User.objects.get(id=user_id, organization=request.user.organization)

                    # Create whisper message with file
                    WhisperMessage.objects.create(
                        sender=request.user,
                        recipient=target_user,
                        content=f"Shared file: {message.file.name if message else document.title}\n{share_message}",
                        organization=request.user.organization,
                    )

                    shared_count += 1
                    logger.info(f"File shared with user {target_user.email} via whisper")

                except User.DoesNotExist:
                    logger.warning(f"User {user_id} not found for file sharing")
                    continue

            # Share with conversations
            for conversation_id in share_with_conversations:
                try:
                    conversation = Conversation.objects.get(
                        id=conversation_id,
                        members=request.user,
                        organization=request.user.organization,
                    )

                    # Create message in conversation with file reference
                    ChatMessage.objects.create(
                        sender=request.user,
                        conversation=conversation,
                        content=f"Shared file: {message.file.name if message else document.title}\n{share_message}",
                        organization=request.user.organization,
                    )

                    shared_count += 1
                    logger.info(f"File shared with conversation {conversation.name}")

                except Conversation.DoesNotExist:
                    logger.warning(f"Conversation {conversation_id} not found for file sharing")
                    continue

            if shared_count > 0:
                return JsonResponse(
                    {
                        "success": True,
                        "message": f"File shared with {shared_count} recipient(s)",
                        "shared_count": shared_count,
                    }
                )
            return JsonResponse({"success": False, "error": "No valid recipients found"}, status=400)

    except Exception as e:
        logger.error(f"Error sharing file: {e}")
        return JsonResponse({"success": False, "error": f"Error sharing file: {e!s}"}, status=500)


@login_required
@require_http_methods(["GET"])
def image_gallery_htmx(request):
    """
    HTMX endpoint for displaying image gallery from messages and documents.

    Shows images shared in conversations and document attachments.
    """
    try:
        # Get filter parameters
        conversation_id = request.GET.get("conversation_id")
        search_query = request.GET.get("search", "").strip()
        page = int(request.GET.get("page", 1))
        per_page = 20

        # Base queryset for images from messages
        image_messages = (
            ChatMessage.objects.filter(
                conversation__members=request.user,
                conversation__organization=request.user.organization,
                file__isnull=False,
                file_type__in=[
                    "image/jpeg",
                    "image/jpg",
                    "image/png",
                    "image/gif",
                    "image/webp",
                ],
            )
            .select_related("sender", "conversation")
            .order_by("-created_at")
        )

        # Filter by conversation if specified
        if conversation_id:
            try:
                conversation = Conversation.objects.get(
                    id=conversation_id,
                    members=request.user,
                    organization=request.user.organization,
                )
                image_messages = image_messages.filter(conversation=conversation)
            except Conversation.DoesNotExist:
                return JsonResponse({"success": False, "error": "Conversation not found"}, status=404)

        # Filter by search query if provided
        if search_query:
            image_messages = image_messages.filter(
                Q(content__icontains=search_query)
                | Q(file__icontains=search_query)
                | Q(sender__first_name__icontains=search_query)
                | Q(sender__last_name__icontains=search_query)
            )

        # Get images from documents as well
        document_images = Document.objects.filter(
            organization=request.user.organization,
            file_type__in=[
                "image/jpeg",
                "image/jpg",
                "image/png",
                "image/gif",
                "image/webp",
            ],
        ).select_related("uploaded_by")

        if search_query:
            document_images = document_images.filter(
                Q(title__icontains=search_query)
                | Q(description__icontains=search_query)
                | Q(uploaded_by__first_name__icontains=search_query)
                | Q(uploaded_by__last_name__icontains=search_query)
            )

        # Combine and paginate results
        # For simplicity, we'll handle message images and document images separately
        # In a real implementation, you might want to create a unified structure

        # Paginate message images
        from django.core.paginator import Paginator

        message_paginator = Paginator(image_messages, per_page)
        message_page_obj = message_paginator.get_page(page)

        # Get a few document images for the gallery
        document_images_sample = document_images[:10]  # Limit to prevent performance issues

        # Get user's conversations for filter dropdown
        user_conversations = Conversation.objects.filter(
            members=request.user, organization=request.user.organization
        ).order_by("name")

        context = {
            "message_images": message_page_obj,
            "document_images": document_images_sample,
            "conversations": user_conversations,
            "current_conversation_id": conversation_id,
            "search_query": search_query,
            "page": page,
            "has_next": message_page_obj.has_next(),
            "has_previous": message_page_obj.has_previous(),
            "next_page_number": (message_page_obj.next_page_number() if message_page_obj.has_next() else None),
            "previous_page_number": (
                message_page_obj.previous_page_number() if message_page_obj.has_previous() else None
            ),
        }

        return render(request, "messaging/partials/image_gallery.html", context)

    except Exception as e:
        logger.error(f"Error loading image gallery: {e}")
        return JsonResponse(
            {"success": False, "error": f"Error loading image gallery: {e!s}"},
            status=500,
        )


@login_required
@require_http_methods(["GET"])
def video_gallery_htmx(request):
    """
    HTMX endpoint for displaying video gallery from messages and documents.

    Shows videos shared in conversations and document attachments.
    """
    try:
        # Get filter parameters
        conversation_id = request.GET.get("conversation_id")
        search_query = request.GET.get("search", "").strip()
        page = int(request.GET.get("page", 1))
        per_page = 20

        # Base queryset for videos from messages
        video_messages = (
            ChatMessage.objects.filter(
                conversation__members=request.user,
                conversation__organization=request.user.organization,
                file__isnull=False,
                file_type__in=[
                    "video/mp4",
                    "video/avi",
                    "video/mov",
                    "video/wmv",
                    "video/webm",
                    "video/mkv",
                ],
            )
            .select_related("sender", "conversation")
            .order_by("-created_at")
        )

        # Filter by conversation if specified
        if conversation_id:
            try:
                conversation = Conversation.objects.get(
                    id=conversation_id,
                    members=request.user,
                    organization=request.user.organization,
                )
                video_messages = video_messages.filter(conversation=conversation)
            except Conversation.DoesNotExist:
                return JsonResponse({"success": False, "error": "Conversation not found"}, status=404)

        # Filter by search query if provided
        if search_query:
            video_messages = video_messages.filter(
                Q(content__icontains=search_query)
                | Q(file__icontains=search_query)
                | Q(sender__first_name__icontains=search_query)
                | Q(sender__last_name__icontains=search_query)
            )

        # Get videos from documents as well
        document_videos = Document.objects.filter(
            organization=request.user.organization,
            file_type__in=[
                "video/mp4",
                "video/avi",
                "video/mov",
                "video/wmv",
                "video/webm",
                "video/mkv",
            ],
        ).select_related("uploaded_by")

        if search_query:
            document_videos = document_videos.filter(
                Q(title__icontains=search_query)
                | Q(description__icontains=search_query)
                | Q(uploaded_by__first_name__icontains=search_query)
                | Q(uploaded_by__last_name__icontains=search_query)
            )

        # Paginate message videos
        from django.core.paginator import Paginator

        message_paginator = Paginator(video_messages, per_page)
        message_page_obj = message_paginator.get_page(page)

        # Get a few document videos for the gallery
        document_videos_sample = document_videos[:10]  # Limit to prevent performance issues

        # Get user's conversations for filter dropdown
        user_conversations = Conversation.objects.filter(
            members=request.user, organization=request.user.organization
        ).order_by("name")

        context = {
            "message_videos": message_page_obj,
            "document_videos": document_videos_sample,
            "conversations": user_conversations,
            "current_conversation_id": conversation_id,
            "search_query": search_query,
            "page": page,
            "has_next": message_page_obj.has_next(),
            "has_previous": message_page_obj.has_previous(),
            "next_page_number": (message_page_obj.next_page_number() if message_page_obj.has_next() else None),
            "previous_page_number": (
                message_page_obj.previous_page_number() if message_page_obj.has_previous() else None
            ),
        }

        return render(request, "messaging/partials/video_gallery.html", context)

    except Exception as e:
        logger.error(f"Error loading video gallery: {e}")
        return JsonResponse(
            {"success": False, "error": f"Error loading video gallery: {e!s}"},
            status=500,
        )


@login_required
@require_http_methods(["GET"])
def document_gallery_htmx(request):
    """
    HTMX endpoint for displaying document gallery from messages and documents.

    Shows documents shared in conversations and document attachments.
    """
    try:
        # Get filter parameters
        conversation_id = request.GET.get("conversation_id")
        search_query = request.GET.get("search", "").strip()
        page = int(request.GET.get("page", 1))
        per_page = 20

        # Base queryset for documents from messages
        document_messages = (
            ChatMessage.objects.filter(
                conversation__members=request.user,
                conversation__organization=request.user.organization,
                file__isnull=False,
                file_type__in=[
                    "application/pdf",
                    "application/msword",
                    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                    "text/plain",
                    "application/vnd.ms-excel",
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                ],
            )
            .select_related("sender", "conversation")
            .order_by("-created_at")
        )

        # Filter by conversation if specified
        if conversation_id:
            try:
                conversation = Conversation.objects.get(
                    id=conversation_id,
                    members=request.user,
                    organization=request.user.organization,
                )
                document_messages = document_messages.filter(conversation=conversation)
            except Conversation.DoesNotExist:
                return JsonResponse({"success": False, "error": "Conversation not found"}, status=404)

        # Filter by search query if provided
        if search_query:
            document_messages = document_messages.filter(
                Q(content__icontains=search_query)
                | Q(file__icontains=search_query)
                | Q(sender__first_name__icontains=search_query)
                | Q(sender__last_name__icontains=search_query)
            )

        # Get documents from the Document model as well
        documents = Document.objects.filter(organization=request.user.organization).select_related("uploaded_by")

        if search_query:
            documents = documents.filter(
                Q(title__icontains=search_query)
                | Q(description__icontains=search_query)
                | Q(uploaded_by__first_name__icontains=search_query)
                | Q(uploaded_by__last_name__icontains=search_query)
            )

        # Paginate message documents
        from django.core.paginator import Paginator

        message_paginator = Paginator(document_messages, per_page)
        message_page_obj = message_paginator.get_page(page)

        # Get a sample of documents for the gallery
        documents_sample = documents[:10]  # Limit to prevent performance issues

        # Get user's conversations for filter dropdown
        user_conversations = Conversation.objects.filter(
            members=request.user, organization=request.user.organization
        ).order_by("name")

        context = {
            "message_documents": message_page_obj,
            "documents": documents_sample,
            "conversations": user_conversations,
            "current_conversation_id": conversation_id,
            "search_query": search_query,
            "page": page,
            "has_next": message_page_obj.has_next(),
            "has_previous": message_page_obj.has_previous(),
            "next_page_number": (message_page_obj.next_page_number() if message_page_obj.has_next() else None),
            "previous_page_number": (
                message_page_obj.previous_page_number() if message_page_obj.has_previous() else None
            ),
        }

        return render(request, "messaging/partials/document_gallery.html", context)

    except Exception as e:
        logger.error(f"Error loading document gallery: {e}")
        return JsonResponse(
            {"success": False, "error": f"Error loading document gallery: {e!s}"},
            status=500,
        )


@login_required
@require_http_methods(["GET", "POST"])
def conversation_export_htmx(request, conversation_id):
    """
    HTMX endpoint for exporting conversation data.

    GET: Show export options form
    POST: Process export and provide download
    """
    try:
        # Verify conversation access
        try:
            conversation = Conversation.objects.get(
                id=conversation_id,
                members=request.user,
                organization=request.user.organization,
            )
        except Conversation.DoesNotExist:
            return JsonResponse({"success": False, "error": "Conversation not found"}, status=404)

        if request.method == "GET":
            # Show export options form
            context = {
                "conversation": conversation,
                "export_formats": [
                    {"value": "json", "label": "JSON"},
                    {"value": "csv", "label": "CSV"},
                    {"value": "txt", "label": "Text"},
                    {"value": "pdf", "label": "PDF"},
                ],
                "date_ranges": [
                    {"value": "all", "label": "All messages"},
                    {"value": "last_week", "label": "Last week"},
                    {"value": "last_month", "label": "Last month"},
                    {"value": "last_year", "label": "Last year"},
                    {"value": "custom", "label": "Custom range"},
                ],
            }

            return render(request, "messaging/partials/conversation_export_form.html", context)

        if request.method == "POST":
            # Process export request
            export_format = request.POST.get("format", "json")
            date_range = request.POST.get("date_range", "all")
            start_date = request.POST.get("start_date")
            end_date = request.POST.get("end_date")
            request.POST.get("include_files") == "on"

            # Get messages based on date range
            messages_query = (
                ChatMessage.objects.filter(conversation=conversation).select_related("sender").order_by("created_at")
            )

            if date_range == "last_week":
                from datetime import datetime, timedelta

                start_date = timezone.now() - timedelta(weeks=1)
                messages_query = messages_query.filter(created_at__gte=start_date)
            elif date_range == "last_month":
                from datetime import datetime, timedelta

                start_date = timezone.now() - timedelta(days=30)
                messages_query = messages_query.filter(created_at__gte=start_date)
            elif date_range == "last_year":
                from datetime import datetime, timedelta

                start_date = timezone.now() - timedelta(days=365)
                messages_query = messages_query.filter(created_at__gte=start_date)
            elif date_range == "custom" and start_date and end_date:
                from datetime import datetime

                start_dt = datetime.strptime(start_date, "%Y-%m-%d")
                end_dt = datetime.strptime(end_date, "%Y-%m-%d")
                messages_query = messages_query.filter(
                    created_at__date__gte=start_dt.date(),
                    created_at__date__lte=end_dt.date(),
                )

            messages = list(messages_query)

            # Generate export data
            if export_format == "json":
                import json

                export_data = {
                    "conversation": {
                        "id": str(conversation.id),
                        "name": conversation.name,
                        "created_at": conversation.created_at.isoformat(),
                    },
                    "messages": [
                        {
                            "id": str(msg.id),
                            "sender": f"{msg.sender.first_name} {msg.sender.last_name}",
                            "content": msg.content,
                            "created_at": msg.created_at.isoformat(),
                            "file": msg.file.name if msg.file else None,
                        }
                        for msg in messages
                    ],
                }

                response = HttpResponse(json.dumps(export_data, indent=2), content_type="application/json")
                response["Content-Disposition"] = f'attachment; filename="conversation_{conversation.id}.json"'

            elif export_format == "csv":
                import csv
                from io import StringIO

                output = StringIO()
                writer = csv.writer(output)

                # Write header
                writer.writerow(["Date", "Sender", "Message", "File"])

                # Write messages
                for msg in messages:
                    writer.writerow(
                        [
                            msg.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                            f"{msg.sender.first_name} {msg.sender.last_name}",
                            msg.content,
                            msg.file.name if msg.file else "",
                        ]
                    )

                response = HttpResponse(output.getvalue(), content_type="text/csv")
                response["Content-Disposition"] = f'attachment; filename="conversation_{conversation.id}.csv"'

            elif export_format == "txt":
                output_lines = [
                    f"Conversation: {conversation.name}",
                    f"Exported: {timezone.now().strftime('%Y-%m-%d %H:%M:%S')}",
                    "=" * 50,
                    "",
                ]

                for msg in messages:
                    output_lines.extend(
                        [
                            f"[{msg.created_at.strftime('%Y-%m-%d %H:%M:%S')}] {msg.sender.first_name} {msg.sender.last_name}:",
                            msg.content,
                            "",
                        ]
                    )

                    if msg.file:
                        output_lines.append(f"  📎 File: {msg.file.name}")
                        output_lines.append("")

                response = HttpResponse("\n".join(output_lines), content_type="text/plain")
                response["Content-Disposition"] = f'attachment; filename="conversation_{conversation.id}.txt"'

            else:  # Default to JSON
                return JsonResponse({"success": False, "error": "Unsupported export format"}, status=400)

            logger.info(f"Conversation {conversation.id} exported by user {request.user.email}")
            return response

    except Exception as e:
        logger.error(f"Error exporting conversation: {e}")
        return JsonResponse(
            {"success": False, "error": f"Error exporting conversation: {e!s}"},
            status=500,
        )


@login_required
@require_http_methods(["GET", "POST"])
def messages_export_htmx(request):
    """
    HTMX endpoint for exporting messages across conversations.

    GET: Show export options form
    POST: Process export and provide download
    """
    try:
        if request.method == "GET":
            # Get user's conversations for selection
            user_conversations = Conversation.objects.filter(
                members=request.user, organization=request.user.organization
            ).order_by("name")

            context = {
                "conversations": user_conversations,
                "export_formats": [
                    {"value": "json", "label": "JSON"},
                    {"value": "csv", "label": "CSV"},
                    {"value": "txt", "label": "Text"},
                    {"value": "pdf", "label": "PDF"},
                ],
                "date_ranges": [
                    {"value": "all", "label": "All messages"},
                    {"value": "last_week", "label": "Last week"},
                    {"value": "last_month", "label": "Last month"},
                    {"value": "last_year", "label": "Last year"},
                    {"value": "custom", "label": "Custom range"},
                ],
                "message_types": [
                    {"value": "all", "label": "All messages"},
                    {"value": "text_only", "label": "Text messages only"},
                    {"value": "with_files", "label": "Messages with files"},
                    {"value": "mentions_only", "label": "Messages mentioning me"},
                ],
            }

            return render(request, "messaging/partials/messages_export_form.html", context)

        if request.method == "POST":
            # Process export request
            export_format = request.POST.get("format", "json")
            date_range = request.POST.get("date_range", "all")
            start_date = request.POST.get("start_date")
            end_date = request.POST.get("end_date")
            message_type = request.POST.get("message_type", "all")
            selected_conversations = request.POST.getlist("conversations")
            request.POST.get("include_files") == "on"

            # Build base query for user's messages
            messages_query = (
                ChatMessage.objects.filter(
                    conversation__members=request.user,
                    conversation__organization=request.user.organization,
                )
                .select_related("sender", "conversation")
                .order_by("created_at")
            )

            # Filter by selected conversations
            if selected_conversations:
                messages_query = messages_query.filter(conversation__id__in=selected_conversations)

            # Filter by date range
            if date_range == "last_week":
                from datetime import datetime, timedelta

                start_date = timezone.now() - timedelta(weeks=1)
                messages_query = messages_query.filter(created_at__gte=start_date)
            elif date_range == "last_month":
                from datetime import datetime, timedelta

                start_date = timezone.now() - timedelta(days=30)
                messages_query = messages_query.filter(created_at__gte=start_date)
            elif date_range == "last_year":
                from datetime import datetime, timedelta

                start_date = timezone.now() - timedelta(days=365)
                messages_query = messages_query.filter(created_at__gte=start_date)
            elif date_range == "custom" and start_date and end_date:
                from datetime import datetime

                start_dt = datetime.strptime(start_date, "%Y-%m-%d")
                end_dt = datetime.strptime(end_date, "%Y-%m-%d")
                messages_query = messages_query.filter(
                    created_at__date__gte=start_dt.date(),
                    created_at__date__lte=end_dt.date(),
                )

            # Filter by message type
            if message_type == "text_only":
                messages_query = messages_query.filter(file__isnull=True)
            elif message_type == "with_files":
                messages_query = messages_query.filter(file__isnull=False)
            elif message_type == "mentions_only":
                # Filter for messages that mention the current user
                messages_query = messages_query.filter(content__icontains=f"@{request.user.username}")

            messages = list(messages_query)

            # Generate export data based on format
            if export_format == "json":
                import json

                export_data = {
                    "export_info": {
                        "exported_by": f"{request.user.first_name} {request.user.last_name}",
                        "exported_at": timezone.now().isoformat(),
                        "total_messages": len(messages),
                        "date_range": date_range,
                        "message_type": message_type,
                    },
                    "messages": [
                        {
                            "id": str(msg.id),
                            "conversation": msg.conversation.name,
                            "sender": f"{msg.sender.first_name} {msg.sender.last_name}",
                            "content": msg.content,
                            "created_at": msg.created_at.isoformat(),
                            "file": msg.file.name if msg.file else None,
                        }
                        for msg in messages
                    ],
                }

                response = HttpResponse(json.dumps(export_data, indent=2), content_type="application/json")
                response["Content-Disposition"] = (
                    f'attachment; filename="messages_export_{timezone.now().strftime("%Y%m%d_%H%M%S")}.json"'
                )

            elif export_format == "csv":
                import csv
                from io import StringIO

                output = StringIO()
                writer = csv.writer(output)

                # Write header
                writer.writerow(["Date", "Conversation", "Sender", "Message", "File"])

                # Write messages
                for msg in messages:
                    writer.writerow(
                        [
                            msg.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                            msg.conversation.name,
                            f"{msg.sender.first_name} {msg.sender.last_name}",
                            msg.content,
                            msg.file.name if msg.file else "",
                        ]
                    )

                response = HttpResponse(output.getvalue(), content_type="text/csv")
                response["Content-Disposition"] = (
                    f'attachment; filename="messages_export_{timezone.now().strftime("%Y%m%d_%H%M%S")}.csv"'
                )

            elif export_format == "txt":
                output_lines = [
                    "Messages Export",
                    f"Exported by: {request.user.first_name} {request.user.last_name}",
                    f"Exported: {timezone.now().strftime('%Y-%m-%d %H:%M:%S')}",
                    f"Total messages: {len(messages)}",
                    f"Date range: {date_range}",
                    f"Message type: {message_type}",
                    "=" * 80,
                    "",
                ]

                current_conversation = None
                for msg in messages:
                    # Add conversation header if changed
                    if current_conversation != msg.conversation.name:
                        current_conversation = msg.conversation.name
                        output_lines.extend(["", f"=== {current_conversation} ===", ""])

                    output_lines.extend(
                        [
                            f"[{msg.created_at.strftime('%Y-%m-%d %H:%M:%S')}] {msg.sender.first_name} {msg.sender.last_name}:",
                            msg.content,
                            "",
                        ]
                    )

                    if msg.file:
                        output_lines.append(f"  📎 File: {msg.file.name}")
                        output_lines.append("")

                response = HttpResponse("\n".join(output_lines), content_type="text/plain")
                response["Content-Disposition"] = (
                    f'attachment; filename="messages_export_{timezone.now().strftime("%Y%m%d_%H%M%S")}.txt"'
                )

            else:  # Default to JSON
                return JsonResponse({"success": False, "error": "Unsupported export format"}, status=400)

            logger.info(f"Messages exported by user {request.user.email}: {len(messages)} messages")
            return response

    except Exception as e:
        logger.error(f"Error exporting messages: {e}")
        return JsonResponse({"success": False, "error": f"Error exporting messages: {e!s}"}, status=500)


@login_required
@require_http_methods(["GET", "POST"])
def whispers_export_htmx(request):
    """
    HTMX endpoint for exporting whisper messages.

    GET: Show export options form
    POST: Process export and provide download
    """
    try:
        if request.method == "GET":
            # Get users the current user has whisper conversations with
            whisper_contacts = (
                User.objects.filter(
                    Q(sent_whispers__recipient=request.user) | Q(received_whispers__sender=request.user),
                    organization=request.user.organization,
                )
                .exclude(id=request.user.id)
                .distinct()
                .order_by("first_name", "last_name")
            )

            context = {
                "whisper_contacts": whisper_contacts,
                "export_formats": [
                    {"value": "json", "label": "JSON"},
                    {"value": "csv", "label": "CSV"},
                    {"value": "txt", "label": "Text"},
                ],
                "date_ranges": [
                    {"value": "all", "label": "All whispers"},
                    {"value": "last_week", "label": "Last week"},
                    {"value": "last_month", "label": "Last month"},
                    {"value": "last_year", "label": "Last year"},
                    {"value": "custom", "label": "Custom range"},
                ],
                "message_directions": [
                    {"value": "all", "label": "Sent and received"},
                    {"value": "sent", "label": "Sent only"},
                    {"value": "received", "label": "Received only"},
                ],
            }

            return render(request, "messaging/partials/whispers_export_form.html", context)

        if request.method == "POST":
            # Process export request
            export_format = request.POST.get("format", "json")
            date_range = request.POST.get("date_range", "all")
            start_date = request.POST.get("start_date")
            end_date = request.POST.get("end_date")
            message_direction = request.POST.get("message_direction", "all")
            selected_contacts = request.POST.getlist("contacts")

            # Build base query for user's whisper messages
            whispers_query = (
                WhisperMessage.objects.filter(
                    Q(sender=request.user) | Q(recipient=request.user),
                    organization=request.user.organization,
                )
                .select_related("sender", "recipient")
                .order_by("created_at")
            )

            # Filter by message direction
            if message_direction == "sent":
                whispers_query = whispers_query.filter(sender=request.user)
            elif message_direction == "received":
                whispers_query = whispers_query.filter(recipient=request.user)

            # Filter by selected contacts
            if selected_contacts:
                whispers_query = whispers_query.filter(
                    Q(sender__id__in=selected_contacts) | Q(recipient__id__in=selected_contacts)
                )

            # Filter by date range
            if date_range == "last_week":
                from datetime import datetime, timedelta

                start_date = timezone.now() - timedelta(weeks=1)
                whispers_query = whispers_query.filter(created_at__gte=start_date)
            elif date_range == "last_month":
                from datetime import datetime, timedelta

                start_date = timezone.now() - timedelta(days=30)
                whispers_query = whispers_query.filter(created_at__gte=start_date)
            elif date_range == "last_year":
                from datetime import datetime, timedelta

                start_date = timezone.now() - timedelta(days=365)
                whispers_query = whispers_query.filter(created_at__gte=start_date)
            elif date_range == "custom" and start_date and end_date:
                from datetime import datetime

                start_dt = datetime.strptime(start_date, "%Y-%m-%d")
                end_dt = datetime.strptime(end_date, "%Y-%m-%d")
                whispers_query = whispers_query.filter(
                    created_at__date__gte=start_dt.date(),
                    created_at__date__lte=end_dt.date(),
                )

            whispers = list(whispers_query)

            # Generate export data based on format
            if export_format == "json":
                import json

                export_data = {
                    "export_info": {
                        "exported_by": f"{request.user.first_name} {request.user.last_name}",
                        "exported_at": timezone.now().isoformat(),
                        "total_whispers": len(whispers),
                        "date_range": date_range,
                        "message_direction": message_direction,
                    },
                    "whispers": [
                        {
                            "id": str(whisper.id),
                            "sender": f"{whisper.sender.first_name} {whisper.sender.last_name}",
                            "recipient": f"{whisper.recipient.first_name} {whisper.recipient.last_name}",
                            "content": whisper.content,
                            "created_at": whisper.created_at.isoformat(),
                            "is_read": whisper.is_read,
                        }
                        for whisper in whispers
                    ],
                }

                response = HttpResponse(json.dumps(export_data, indent=2), content_type="application/json")
                response["Content-Disposition"] = (
                    f'attachment; filename="whispers_export_{timezone.now().strftime("%Y%m%d_%H%M%S")}.json"'
                )

            elif export_format == "csv":
                import csv
                from io import StringIO

                output = StringIO()
                writer = csv.writer(output)

                # Write header
                writer.writerow(["Date", "Sender", "Recipient", "Message", "Read"])

                # Write whispers
                for whisper in whispers:
                    writer.writerow(
                        [
                            whisper.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                            f"{whisper.sender.first_name} {whisper.sender.last_name}",
                            f"{whisper.recipient.first_name} {whisper.recipient.last_name}",
                            whisper.content,
                            "Yes" if whisper.is_read else "No",
                        ]
                    )

                response = HttpResponse(output.getvalue(), content_type="text/csv")
                response["Content-Disposition"] = (
                    f'attachment; filename="whispers_export_{timezone.now().strftime("%Y%m%d_%H%M%S")}.csv"'
                )

            elif export_format == "txt":
                output_lines = [
                    "Whispers Export",
                    f"Exported by: {request.user.first_name} {request.user.last_name}",
                    f"Exported: {timezone.now().strftime('%Y-%m-%d %H:%M:%S')}",
                    f"Total whispers: {len(whispers)}",
                    f"Date range: {date_range}",
                    f"Message direction: {message_direction}",
                    "=" * 80,
                    "",
                ]

                current_contact = None
                for whisper in whispers:
                    # Determine the contact (other person in the conversation)
                    contact = whisper.recipient if whisper.sender == request.user else whisper.sender
                    contact_name = f"{contact.first_name} {contact.last_name}"

                    # Add contact header if changed
                    if current_contact != contact_name:
                        current_contact = contact_name
                        output_lines.extend(["", f"=== Conversation with {current_contact} ===", ""])

                    # Determine direction indicator
                    direction = "→" if whisper.sender == request.user else "←"
                    read_status = " (read)" if whisper.is_read else " (unread)"

                    output_lines.extend(
                        [
                            f"[{whisper.created_at.strftime('%Y-%m-%d %H:%M:%S')}] {direction} {whisper.sender.first_name} {whisper.sender.last_name}{read_status}:",
                            whisper.content,
                            "",
                        ]
                    )

                response = HttpResponse("\n".join(output_lines), content_type="text/plain")
                response["Content-Disposition"] = (
                    f'attachment; filename="whispers_export_{timezone.now().strftime("%Y%m%d_%H%M%S")}.txt"'
                )

            else:  # Default to JSON
                return JsonResponse({"success": False, "error": "Unsupported export format"}, status=400)

            logger.info(f"Whispers exported by user {request.user.email}: {len(whispers)} whispers")
            return response

    except Exception as e:
        logger.error(f"Error exporting whispers: {e}")
        return JsonResponse({"success": False, "error": f"Error exporting whispers: {e!s}"}, status=500)


@login_required
@require_http_methods(["GET", "POST"])
def analytics_export_htmx(request):
    """
    HTMX endpoint for exporting messaging analytics data.

    GET: Show analytics export options form
    POST: Process export and provide download
    """
    try:
        if request.method == "GET":
            # Get user's conversations for analytics selection
            user_conversations = Conversation.objects.filter(
                members=request.user, organization=request.user.organization
            ).order_by("name")

            context = {
                "conversations": user_conversations,
                "export_formats": [
                    {"value": "json", "label": "JSON"},
                    {"value": "csv", "label": "CSV"},
                    {"value": "xlsx", "label": "Excel"},
                ],
                "date_ranges": [
                    {"value": "last_week", "label": "Last week"},
                    {"value": "last_month", "label": "Last month"},
                    {"value": "last_quarter", "label": "Last quarter"},
                    {"value": "last_year", "label": "Last year"},
                    {"value": "custom", "label": "Custom range"},
                ],
                "analytics_types": [
                    {"value": "overview", "label": "Overview statistics"},
                    {"value": "user_activity", "label": "User activity metrics"},
                    {"value": "conversation_stats", "label": "Conversation statistics"},
                    {
                        "value": "message_frequency",
                        "label": "Message frequency analysis",
                    },
                    {"value": "response_times", "label": "Response time analysis"},
                ],
            }

            return render(request, "messaging/partials/analytics_export_form.html", context)

        if request.method == "POST":
            # Process analytics export request
            export_format = request.POST.get("format", "json")
            date_range = request.POST.get("date_range", "last_month")
            start_date = request.POST.get("start_date")
            end_date = request.POST.get("end_date")
            analytics_type = request.POST.get("analytics_type", "overview")
            selected_conversations = request.POST.getlist("conversations")

            # Determine date range
            from datetime import datetime, timedelta

            end_dt = timezone.now()

            if date_range == "last_week":
                start_dt = end_dt - timedelta(weeks=1)
            elif date_range == "last_month":
                start_dt = end_dt - timedelta(days=30)
            elif date_range == "last_quarter":
                start_dt = end_dt - timedelta(days=90)
            elif date_range == "last_year":
                start_dt = end_dt - timedelta(days=365)
            elif date_range == "custom" and start_date and end_date:
                start_dt = timezone.make_aware(datetime.strptime(start_date, "%Y-%m-%d"))
                end_dt = timezone.make_aware(datetime.strptime(end_date, "%Y-%m-%d"))
            else:
                start_dt = end_dt - timedelta(days=30)  # Default to last month

            # Base queries for analytics
            messages_query = ChatMessage.objects.filter(
                conversation__members=request.user,
                conversation__organization=request.user.organization,
                created_at__gte=start_dt,
                created_at__lte=end_dt,
            )

            conversations_query = Conversation.objects.filter(
                members=request.user, organization=request.user.organization
            )

            if selected_conversations:
                messages_query = messages_query.filter(conversation__id__in=selected_conversations)
                conversations_query = conversations_query.filter(id__in=selected_conversations)

            # Generate analytics data based on type
            analytics_data = {}

            if analytics_type == "overview":
                total_messages = messages_query.count()
                total_conversations = conversations_query.count()
                active_users = messages_query.values("sender").distinct().count()
                messages_with_files = messages_query.filter(file__isnull=False).count()

                analytics_data = {
                    "overview": {
                        "total_messages": total_messages,
                        "total_conversations": total_conversations,
                        "active_users": active_users,
                        "messages_with_files": messages_with_files,
                        "average_messages_per_conversation": (
                            round(total_messages / total_conversations, 2) if total_conversations > 0 else 0
                        ),
                        "file_attachment_rate": (
                            round((messages_with_files / total_messages) * 100, 2) if total_messages > 0 else 0
                        ),
                    }
                }

            elif analytics_type == "user_activity":
                from django.db.models import Count

                user_activity = (
                    messages_query.values("sender__first_name", "sender__last_name", "sender__email")
                    .annotate(message_count=Count("id"))
                    .order_by("-message_count")
                )

                analytics_data = {"user_activity": list(user_activity)}

            elif analytics_type == "conversation_stats":
                from django.db.models import Count

                conversation_stats = (
                    conversations_query.annotate(
                        message_count=Count(
                            "messages",
                            filter=Q(
                                messages__created_at__gte=start_dt,
                                messages__created_at__lte=end_dt,
                            ),
                        ),
                        participant_count=Count("members"),
                    )
                    .values("name", "message_count", "participant_count", "created_at")
                    .order_by("-message_count")
                )

                analytics_data = {
                    "conversation_stats": [
                        {
                            "name": conv["name"],
                            "message_count": conv["message_count"],
                            "participant_count": conv["participant_count"],
                            "created_at": (conv["created_at"].isoformat() if conv["created_at"] else None),
                        }
                        for conv in conversation_stats
                    ]
                }

            elif analytics_type == "message_frequency":
                from django.db.models import Count
                from django.db.models.functions import TruncDate

                daily_frequency = (
                    messages_query.annotate(date=TruncDate("created_at"))
                    .values("date")
                    .annotate(message_count=Count("id"))
                    .order_by("date")
                )

                analytics_data = {
                    "message_frequency": [
                        {
                            "date": freq["date"].isoformat(),
                            "message_count": freq["message_count"],
                        }
                        for freq in daily_frequency
                    ]
                }

            elif analytics_type == "response_times":
                # Calculate average response times (simplified)
                conversations_with_messages = conversations_query.prefetch_related("messages")
                response_times = []

                for conv in conversations_with_messages:
                    conv_messages = conv.messages.filter(created_at__gte=start_dt, created_at__lte=end_dt).order_by(
                        "created_at"
                    )

                    if conv_messages.count() > 1:
                        total_response_time = 0
                        response_count = 0

                        prev_message = None
                        for message in conv_messages:
                            if prev_message and prev_message.sender != message.sender:
                                response_time = (
                                    message.created_at - prev_message.created_at
                                ).total_seconds() / 60  # minutes
                                total_response_time += response_time
                                response_count += 1
                            prev_message = message

                        if response_count > 0:
                            avg_response_time = total_response_time / response_count
                            response_times.append(
                                {
                                    "conversation": conv.name,
                                    "average_response_time_minutes": round(avg_response_time, 2),
                                    "total_responses": response_count,
                                }
                            )

                analytics_data = {"response_times": response_times}

            # Add metadata
            analytics_data["metadata"] = {
                "exported_by": f"{request.user.first_name} {request.user.last_name}",
                "exported_at": timezone.now().isoformat(),
                "date_range": {
                    "start": start_dt.isoformat(),
                    "end": end_dt.isoformat(),
                    "type": date_range,
                },
                "analytics_type": analytics_type,
                "organization": (request.user.organization.name if hasattr(request.user, "organization") else None),
            }

            # Generate export based on format
            if export_format == "json":
                import json

                response = HttpResponse(
                    json.dumps(analytics_data, indent=2, default=str),
                    content_type="application/json",
                )
                response["Content-Disposition"] = (
                    f'attachment; filename="messaging_analytics_{analytics_type}_{timezone.now().strftime("%Y%m%d_%H%M%S")}.json"'
                )

            elif export_format == "csv":
                import csv
                from io import StringIO

                output = StringIO()
                writer = csv.writer(output)

                # Write different CSV structure based on analytics type
                if analytics_type == "overview":
                    writer.writerow(["Metric", "Value"])
                    for key, value in analytics_data["overview"].items():
                        writer.writerow([key.replace("_", " ").title(), value])

                elif analytics_type == "user_activity":
                    writer.writerow(["User Name", "Email", "Message Count"])
                    for user in analytics_data["user_activity"]:
                        writer.writerow(
                            [
                                f"{user['sender__first_name']} {user['sender__last_name']}",
                                user["sender__email"],
                                user["message_count"],
                            ]
                        )

                elif analytics_type == "conversation_stats":
                    writer.writerow(["Conversation", "Messages", "Participants", "Created"])
                    for conv in analytics_data["conversation_stats"]:
                        writer.writerow(
                            [
                                conv["name"],
                                conv["message_count"],
                                conv["participant_count"],
                                conv["created_at"],
                            ]
                        )

                elif analytics_type == "message_frequency":
                    writer.writerow(["Date", "Message Count"])
                    for freq in analytics_data["message_frequency"]:
                        writer.writerow([freq["date"], freq["message_count"]])

                elif analytics_type == "response_times":
                    writer.writerow(["Conversation", "Avg Response Time (min)", "Total Responses"])
                    for rt in analytics_data["response_times"]:
                        writer.writerow(
                            [
                                rt["conversation"],
                                rt["average_response_time_minutes"],
                                rt["total_responses"],
                            ]
                        )

                response = HttpResponse(output.getvalue(), content_type="text/csv")
                response["Content-Disposition"] = (
                    f'attachment; filename="messaging_analytics_{analytics_type}_{timezone.now().strftime("%Y%m%d_%H%M%S")}.csv"'
                )

            else:  # Default to JSON
                return JsonResponse({"success": False, "error": "Unsupported export format"}, status=400)

            logger.info(f"Analytics exported by user {request.user.email}: {analytics_type} for {date_range}")
            return response

    except Exception as e:
        logger.error(f"Error exporting analytics: {e}")
        return JsonResponse({"success": False, "error": f"Error exporting analytics: {e!s}"}, status=500)


@login_required
@require_http_methods(["POST"])
def create_backup_htmx(request):
    """
    HTMX endpoint for creating backups of user's messaging data.

    Creates a backup of conversations, messages, and whispers for the current user.
    """
    try:
        # Ensure user has organization access
        if not hasattr(request.user, "organization") or not request.user.organization:
            return JsonResponse({"success": False, "error": "Organization access required"}, status=400)

        # Get user's conversations for backup
        conversations = Conversation.objects.filter(
            participants=request.user, organization=request.user.organization
        ).prefetch_related("messages__user", "messages__reactions", "participants")

        # Get user's whisper messages
        whispers = WhisperMessage.objects.filter(Q(sender=request.user) | Q(recipient=request.user)).select_related(
            "sender", "recipient"
        )

        # Get user's comments
        comments = Comment.objects.filter(user=request.user).select_related("user")

        # Create backup data structure
        backup_data = {
            "user_id": str(request.user.id),
            "organization_id": str(request.user.organization.id),
            "created_at": timezone.now().isoformat(),
            "conversations": [],
            "whispers": [],
            "comments": [],
            "stats": {
                "total_conversations": conversations.count(),
                "total_messages": 0,
                "total_whispers": whispers.count(),
                "total_comments": comments.count(),
            },
        }

        # Process conversations and messages
        for conversation in conversations:
            conv_data = {
                "id": str(conversation.id),
                "title": conversation.title,
                "conversation_type": conversation.conversation_type,
                "created_at": conversation.created_at.isoformat(),
                "is_active": conversation.is_active,
                "messages": [],
            }

            for message in conversation.messages.all():
                msg_data = {
                    "id": str(message.id),
                    "content": message.content,
                    "message_type": message.message_type,
                    "created_at": message.created_at.isoformat(),
                    "user": {
                        "id": str(message.user.id),
                        "username": message.user.username,
                        "email": message.user.email,
                    },
                    "reactions": [
                        {"emoji": reaction.emoji, "user": reaction.user.username}
                        for reaction in message.reactions.all()
                    ],
                }
                conv_data["messages"].append(msg_data)
                backup_data["stats"]["total_messages"] += 1

            backup_data["conversations"].append(conv_data)

        # Process whisper messages
        for whisper in whispers:
            whisper_data = {
                "id": str(whisper.id),
                "message": whisper.message,
                "created_at": whisper.created_at.isoformat(),
                "is_read": whisper.is_read,
                "sender": {
                    "id": str(whisper.sender.id),
                    "username": whisper.sender.username,
                    "email": whisper.sender.email,
                },
                "recipient": {
                    "id": str(whisper.recipient.id),
                    "username": whisper.recipient.username,
                    "email": whisper.recipient.email,
                },
            }
            backup_data["whispers"].append(whisper_data)

        # Process comments
        for comment in comments:
            comment_data = {
                "id": str(comment.id),
                "content": comment.content,
                "created_at": comment.created_at.isoformat(),
                "content_type": comment.content_type.model,
                "object_id": comment.object_id,
            }
            backup_data["comments"].append(comment_data)

        # Store backup data (in real implementation, this would be saved to file/database)
        # For now, we'll store in session for demo purposes
        request.session["backup_data"] = backup_data

        logger.info(
            f"Backup created for user {request.user.username} with {backup_data['stats']['total_conversations']} conversations"
        )

        return JsonResponse(
            {
                "success": True,
                "backup_created": True,
                "backup_stats": backup_data["stats"],
                "backup_timestamp": timezone.now().isoformat(),
            }
        )

    except Exception as e:
        logger.error(f"Error creating backup for user {request.user.username}: {e}")
        return JsonResponse({"success": False, "error": f"Error creating backup: {str(e)}"}, status=500)


@login_required
@require_http_methods(["POST"])
def restore_backup_htmx(request):
    """
    HTMX endpoint for restoring messaging data from backup.

    Restores conversations, messages, and whispers from a previously created backup.
    """
    try:
        # Ensure user has organization access
        if not hasattr(request.user, "organization") or not request.user.organization:
            return JsonResponse({"success": False, "error": "Organization access required"}, status=400)

        # Get backup data from session (in real implementation, this would come from file/database)
        backup_data = request.session.get("backup_data")
        if not backup_data:
            return JsonResponse({"success": False, "error": "No backup data found"}, status=400)

        # Validate backup data belongs to current user
        if backup_data.get("user_id") != str(request.user.id):
            return JsonResponse(
                {
                    "success": False,
                    "error": "Backup data does not belong to current user",
                },
                status=400,
            )

        # Start restoration process
        with transaction.atomic():
            restored_stats = {
                "conversations": 0,
                "messages": 0,
                "whispers": 0,
                "comments": 0,
            }

            # Restore conversations (if they don't already exist)
            for conv_data in backup_data.get("conversations", []):
                conversation, created = Conversation.objects.get_or_create(
                    id=conv_data["id"],
                    defaults={
                        "title": conv_data["title"],
                        "conversation_type": conv_data["conversation_type"],
                        "is_active": conv_data["is_active"],
                        "organization": request.user.organization,
                    },
                )

                if created:
                    # Add user as participant
                    conversation.add_participant(request.user)
                    restored_stats["conversations"] += 1

                    # Restore messages for this conversation
                    for msg_data in conv_data.get("messages", []):
                        message, msg_created = ChatMessage.objects.get_or_create(
                            id=msg_data["id"],
                            defaults={
                                "content": msg_data["content"],
                                "message_type": msg_data["message_type"],
                                "user": request.user,
                                "conversation": conversation,
                                "organization": request.user.organization,
                            },
                        )

                        if msg_created:
                            restored_stats["messages"] += 1

            # Restore whisper messages (if they don't already exist)
            for whisper_data in backup_data.get("whispers", []):
                whisper, created = WhisperMessage.objects.get_or_create(
                    id=whisper_data["id"],
                    defaults={
                        "message": whisper_data["message"],
                        "sender": request.user,
                        "recipient": whisper_data["recipient"],  # Restore original recipient
                        "is_read": whisper_data["is_read"],
                    },
                )

                if created:
                    restored_stats["whispers"] += 1

            # Restore comments (if they don't already exist)
            for comment_data in backup_data.get("comments", []):
                comment, created = Comment.objects.get_or_create(
                    id=comment_data["id"],
                    defaults={
                        "content": comment_data["content"],
                        "user": request.user,
                        "object_id": comment_data["object_id"],
                    },
                )

                if created:
                    restored_stats["comments"] += 1

        logger.info(f"Backup restored for user {request.user.username}: {restored_stats}")

        return JsonResponse(
            {
                "success": True,
                "restore_completed": True,
                "restored_stats": restored_stats,
                "restore_timestamp": timezone.now().isoformat(),
            }
        )

    except Exception as e:
        logger.error(f"Error restoring backup for user {request.user.username}: {e}")
        return JsonResponse({"success": False, "error": f"Error restoring backup: {str(e)}"}, status=500)


@login_required
@require_http_methods(["POST"])
def archive_conversation_htmx(request):
    """
    HTMX endpoint for archiving/unarchiving a conversation.

    Toggles the archive status of a conversation for the current user.
    """
    try:
        conversation_id = request.POST.get("conversation_id")
        if not conversation_id:
            return JsonResponse({"success": False, "error": "Conversation ID required"}, status=400)

        # Get conversation with organization access check
        conversation = get_object_or_404(
            Conversation,
            id=conversation_id,
            participants=request.user,
            organization=request.user.organization,
        )

        # Check if user has permission to archive this conversation
        user_member = conversation.members.filter(user=request.user).first()
        if not user_member:
            return JsonResponse(
                {
                    "success": False,
                    "error": "You don't have permission to archive this conversation",
                },
                status=403,
            )

        # Toggle archive status
        conversation.is_active = not conversation.is_active
        conversation.save()

        action = "archived" if not conversation.is_active else "unarchived"

        logger.info(f"Conversation {conversation.id} {action} by user {request.user.username}")

        return JsonResponse(
            {
                "success": True,
                "conversation_id": str(conversation.id),
                "action": action,
                "is_active": conversation.is_active,
            }
        )

    except Exception as e:
        logger.error(f"Error archiving conversation: {e}")
        return JsonResponse(
            {"success": False, "error": f"Error archiving conversation: {str(e)}"},
            status=500,
        )


@login_required
@require_http_methods(["GET", "POST"])
def notification_settings_htmx(request):
    """
    HTMX endpoint for managing user notification settings.
    """
    try:
        # Get or create user notification settings
        from ..models import NotificationSettings

        settings, created = NotificationSettings.objects.get_or_create(user=request.user)

        if request.method == "POST":
            # Update notification settings
            settings.email_notifications = request.POST.get("email_notifications") == "on"
            settings.push_notifications = request.POST.get("push_notifications") == "on"
            settings.notify_messages = request.POST.get("notify_messages") == "on"
            settings.notify_mentions = request.POST.get("notify_mentions") == "on"
            settings.notify_comments = request.POST.get("notify_comments") == "on"
            settings.notify_tasks = request.POST.get("notify_tasks") == "on"
            settings.notify_documents = request.POST.get("notify_documents") == "on"
            settings.notify_projects = request.POST.get("notify_projects") == "on"
            settings.email_digest_frequency = request.POST.get("email_digest_frequency", "daily")
            settings.save()

            return JsonResponse(
                {
                    "success": True,
                    "message": "Notification settings updated successfully",
                }
            )

        # GET request - return settings form
        return JsonResponse(
            {
                "success": True,
                "settings": {
                    "email_notifications": settings.email_notifications,
                    "push_notifications": settings.push_notifications,
                    "notify_messages": settings.notify_messages,
                    "notify_mentions": settings.notify_mentions,
                    "notify_comments": settings.notify_comments,
                    "notify_tasks": settings.notify_tasks,
                    "notify_documents": settings.notify_documents,
                    "notify_projects": settings.notify_projects,
                    "email_digest_frequency": settings.email_digest_frequency,
                },
            }
        )

    except Exception as e:
        logger.error(f"Error handling notification settings: {e}")
        return JsonResponse(
            {
                "success": False,
                "error": f"Error updating notification settings: {str(e)}",
            },
            status=500,
        )


@login_required
@require_http_methods(["GET", "POST"])
def privacy_settings_htmx(request):
    """
    HTMX endpoint for managing user privacy settings.
    """
    try:
        # Get or create user collaboration settings
        from ..models import CollaborationSettings

        settings, created = CollaborationSettings.objects.get_or_create(user=request.user)

        if request.method == "POST":
            # Update privacy settings
            settings.allow_direct_messages = request.POST.get("allow_direct_messages") == "on"
            settings.show_read_receipts = request.POST.get("show_read_receipts") == "on"
            settings.show_user_status = request.POST.get("show_user_status") == "on"
            settings.save()

            return JsonResponse({"success": True, "message": "Privacy settings updated successfully"})

        # GET request - return settings
        return JsonResponse(
            {
                "success": True,
                "settings": {
                    "allow_direct_messages": settings.allow_direct_messages,
                    "show_read_receipts": settings.show_read_receipts,
                    "show_user_status": settings.show_user_status,
                },
            }
        )

    except Exception as e:
        logger.error(f"Error handling privacy settings: {e}")
        return JsonResponse(
            {"success": False, "error": f"Error updating privacy settings: {str(e)}"},
            status=500,
        )


@login_required
@require_http_methods(["GET", "POST"])
def appearance_settings_htmx(request):
    """
    HTMX endpoint for managing user appearance settings.
    """
    try:
        # Get or create user collaboration settings
        from ..models import CollaborationSettings

        settings, created = CollaborationSettings.objects.get_or_create(user=request.user)

        if request.method == "POST":
            # Update appearance settings
            settings.compact_message_view = request.POST.get("compact_message_view") == "on"
            settings.show_message_timestamps = request.POST.get("show_message_timestamps") == "on"
            settings.show_reaction_tooltips = request.POST.get("show_reaction_tooltips") == "on"
            settings.custom_emoji_set = request.POST.get("custom_emoji_set", "default")
            settings.save()

            return JsonResponse({"success": True, "message": "Appearance settings updated successfully"})

        # GET request - return settings
        return JsonResponse(
            {
                "success": True,
                "settings": {
                    "compact_message_view": settings.compact_message_view,
                    "show_message_timestamps": settings.show_message_timestamps,
                    "show_reaction_tooltips": settings.show_reaction_tooltips,
                    "custom_emoji_set": settings.custom_emoji_set,
                },
            }
        )

    except Exception as e:
        logger.error(f"Error handling appearance settings: {e}")
        return JsonResponse(
            {
                "success": False,
                "error": f"Error updating appearance settings: {str(e)}",
            },
            status=500,
        )


@login_required
@require_http_methods(["GET", "POST"])
def admin_messaging_settings_htmx(request):
    """
    HTMX endpoint for managing admin messaging settings.
    """
    try:
        # Check if user has admin permissions
        if not request.user.is_staff:
            return JsonResponse({"success": False, "error": "Admin access required"}, status=403)

        if request.method == "POST":
            # Update admin settings (this would normally be stored in a settings model)
            # For now, just return success
            return JsonResponse({"success": True, "message": "Admin settings updated successfully"})

        # GET request - return admin settings
        return JsonResponse(
            {
                "success": True,
                "settings": {
                    "max_message_length": 5000,
                    "allow_file_uploads": True,
                    "max_file_size": 10,  # MB
                    "allowed_file_types": ["pdf", "doc", "docx", "jpg", "png", "gif"],
                    "message_moderation_enabled": False,
                    "auto_archive_days": 365,
                },
            }
        )

    except Exception as e:
        logger.error(f"Error handling admin messaging settings: {e}")
        return JsonResponse(
            {"success": False, "error": f"Error updating admin settings: {str(e)}"},
            status=500,
        )


@login_required
@require_http_methods(["GET", "POST"])
def message_moderation_htmx(request):
    """
    HTMX endpoint for message moderation functionality.
    """
    try:
        # Check if user has admin permissions
        if not request.user.is_staff:
            return JsonResponse({"success": False, "error": "Admin access required"}, status=403)

        if request.method == "POST":
            action = request.POST.get("action")
            request.POST.get("message_id")

            if action == "approve":
                # Approve message
                return JsonResponse({"success": True, "message": "Message approved successfully"})
            elif action == "reject":
                # Reject message
                return JsonResponse({"success": True, "message": "Message rejected successfully"})
            elif action == "delete":
                # Delete message
                return JsonResponse({"success": True, "message": "Message deleted successfully"})

        # GET request - return pending messages for moderation
        return JsonResponse(
            {
                "success": True,
                "pending_messages": [],
                "moderation_stats": {
                    "total_pending": 0,
                    "approved_today": 0,
                    "rejected_today": 0,
                },
            }
        )

    except Exception as e:
        logger.error(f"Error handling message moderation: {e}")
        return JsonResponse(
            {"success": False, "error": f"Error with message moderation: {str(e)}"},
            status=500,
        )


@login_required
@require_http_methods(["GET"])
def admin_analytics_htmx(request):
    """
    HTMX endpoint for admin analytics dashboard.
    """
    try:
        # Check if user has admin permissions
        if not request.user.is_staff:
            return JsonResponse({"success": False, "error": "Admin access required"}, status=403)

        # Get analytics data
        from datetime import timedelta

        # Calculate date ranges
        today = timezone.now().date()
        week_ago = today - timedelta(days=7)
        month_ago = today - timedelta(days=30)

        # Get message statistics
        total_messages = ChatMessage.objects.count()
        messages_this_week = ChatMessage.objects.filter(created_at__date__gte=week_ago).count()
        messages_this_month = ChatMessage.objects.filter(created_at__date__gte=month_ago).count()

        # Get user statistics
        total_users = User.objects.count()
        active_users_week = User.objects.filter(chat_messages__created_at__date__gte=week_ago).distinct().count()

        # Get conversation statistics
        total_conversations = Conversation.objects.count()
        active_conversations = Conversation.objects.filter(is_active=True).count()

        return JsonResponse(
            {
                "success": True,
                "analytics": {
                    "messages": {
                        "total": total_messages,
                        "this_week": messages_this_week,
                        "this_month": messages_this_month,
                    },
                    "users": {
                        "total": total_users,
                        "active_this_week": active_users_week,
                    },
                    "conversations": {
                        "total": total_conversations,
                        "active": active_conversations,
                    },
                },
            }
        )

    except Exception as e:
        logger.error(f"Error retrieving admin analytics: {e}")
        return JsonResponse(
            {"success": False, "error": f"Error retrieving analytics: {str(e)}"},
            status=500,
        )


@login_required
@require_http_methods(["GET"])
def unread_notifications_htmx(request):
    """
    HTMX endpoint for getting unread notifications.
    """
    try:
        # Get unread notifications for the current user
        notifications = (
            Notification.objects.filter(recipient=request.user, is_read=False)
            .select_related("sender")
            .order_by("-created_at")[:10]
        )

        # Format notifications for response
        notifications_data = []
        for notification in notifications:
            notifications_data.append(
                {
                    "id": str(notification.id),
                    "title": notification.title,
                    "message": notification.message,
                    "notification_type": notification.notification_type,
                    "priority": notification.priority,
                    "created_at": notification.created_at.isoformat(),
                    "sender": (
                        {
                            "id": str(notification.sender.id),
                            "username": notification.sender.username,
                            "email": notification.sender.email,
                        }
                        if notification.sender
                        else None
                    ),
                    "action_url": notification.action_url,
                    "action_label": notification.action_label,
                }
            )

        return JsonResponse(
            {
                "success": True,
                "notifications": notifications_data,
                "total_unread": notifications.count(),
            }
        )

    except Exception as e:
        logger.error(f"Error retrieving unread notifications: {e}")
        return JsonResponse(
            {"success": False, "error": f"Error retrieving notifications: {str(e)}"},
            status=500,
        )


@login_required
@require_http_methods(["POST"])
def mark_notifications_read_htmx(request):
    """
    HTMX endpoint for marking notifications as read.
    """
    try:
        notification_ids = request.POST.getlist("notification_ids")

        if not notification_ids:
            # If no specific IDs provided, mark all notifications as read
            updated_count = Notification.objects.filter(recipient=request.user, is_read=False).update(
                is_read=True, read_at=timezone.now()
            )
        else:
            # Mark specific notifications as read
            updated_count = Notification.objects.filter(
                id__in=notification_ids, recipient=request.user, is_read=False
            ).update(is_read=True, read_at=timezone.now())

        logger.info(f"Marked {updated_count} notifications as read for user {request.user.username}")

        return JsonResponse(
            {
                "success": True,
                "marked_count": updated_count,
                "message": f"Marked {updated_count} notifications as read",
            }
        )

    except Exception as e:
        logger.error(f"Error marking notifications as read: {e}")
        return JsonResponse(
            {
                "success": False,
                "error": f"Error marking notifications as read: {str(e)}",
            },
            status=500,
        )


@login_required
@require_http_methods(["POST"])
def mark_all_notifications_read_htmx(request):
    """
    HTMX endpoint for marking all notifications as read.
    """
    try:
        # Mark all unread notifications as read for the current user
        updated_count = Notification.objects.filter(recipient=request.user, is_read=False).update(
            is_read=True, read_at=timezone.now()
        )

        logger.info(f"Marked all {updated_count} notifications as read for user {request.user.username}")

        return JsonResponse(
            {
                "success": True,
                "marked_count": updated_count,
                "message": f"Marked all {updated_count} notifications as read",
            }
        )

    except Exception as e:
        logger.error(f"Error marking all notifications as read: {e}")
        return JsonResponse(
            {
                "success": False,
                "error": f"Error marking all notifications as read: {str(e)}",
            },
            status=500,
        )


@login_required
@require_http_methods(["POST"])
def mark_notification_read_htmx(request):
    """
    HTMX endpoint for marking a single notification as read.
    """
    try:
        notification_id = request.POST.get("notification_id")

        if not notification_id:
            return JsonResponse({"success": False, "error": "Notification ID is required"}, status=400)

        # Mark the specific notification as read
        notification = get_object_or_404(Notification, id=notification_id, recipient=request.user)

        if not notification.is_read:
            notification.is_read = True
            notification.read_at = timezone.now()
            notification.save()

            logger.info(f"Marked notification {notification_id} as read for user {request.user.username}")

            return JsonResponse(
                {
                    "success": True,
                    "notification_id": str(notification.id),
                    "message": "Notification marked as read",
                }
            )
        else:
            return JsonResponse(
                {
                    "success": True,
                    "notification_id": str(notification.id),
                    "message": "Notification was already read",
                }
            )

    except Exception as e:
        logger.error(f"Error marking notification as read: {e}")
        return JsonResponse(
            {
                "success": False,
                "error": f"Error marking notification as read: {str(e)}",
            },
            status=500,
        )


@login_required
@require_http_methods(["DELETE", "POST"])
def delete_notification_htmx(request):
    """
    HTMX endpoint for deleting a notification.
    """
    try:
        notification_id = request.POST.get("notification_id") or request.GET.get("notification_id")

        if not notification_id:
            return JsonResponse({"success": False, "error": "Notification ID is required"}, status=400)

        # Delete the specific notification
        notification = get_object_or_404(Notification, id=notification_id, recipient=request.user)

        notification.delete()

        logger.info(f"Deleted notification {notification_id} for user {request.user.username}")

        return JsonResponse(
            {
                "success": True,
                "notification_id": str(notification_id),
                "message": "Notification deleted successfully",
            }
        )

    except Exception as e:
        logger.error(f"Error deleting notification: {e}")
        return JsonResponse(
            {"success": False, "error": f"Error deleting notification: {str(e)}"},
            status=500,
        )


@login_required
@require_http_methods(["GET", "POST"])
def notification_preferences_htmx(request):
    """
    HTMX endpoint for managing notification preferences.
    """
    try:
        # Get or create user notification settings
        from ..models import NotificationSettings

        settings, created = NotificationSettings.objects.get_or_create(user=request.user)

        if request.method == "POST":
            # Update notification preferences
            settings.email_notifications = request.POST.get("email_notifications") == "on"
            settings.push_notifications = request.POST.get("push_notifications") == "on"
            settings.push_sound = request.POST.get("push_sound") == "on"
            settings.notify_messages = request.POST.get("notify_messages") == "on"
            settings.notify_mentions = request.POST.get("notify_mentions") == "on"
            settings.notify_comments = request.POST.get("notify_comments") == "on"
            settings.notify_tasks = request.POST.get("notify_tasks") == "on"
            settings.notify_documents = request.POST.get("notify_documents") == "on"
            settings.notify_projects = request.POST.get("notify_projects") == "on"
            settings.notify_conflicts = request.POST.get("notify_conflicts") == "on"
            settings.email_digest_frequency = request.POST.get("email_digest_frequency", "daily")

            # Quiet hours settings
            settings.quiet_hours_enabled = request.POST.get("quiet_hours_enabled") == "on"
            if settings.quiet_hours_enabled:
                quiet_start = request.POST.get("quiet_hours_start")
                quiet_end = request.POST.get("quiet_hours_end")
                if quiet_start:
                    from datetime import time

                    settings.quiet_hours_start = time.fromisoformat(quiet_start)
                if quiet_end:
                    from datetime import time

                    settings.quiet_hours_end = time.fromisoformat(quiet_end)

            settings.save()

            return JsonResponse(
                {
                    "success": True,
                    "message": "Notification preferences updated successfully",
                }
            )

        # GET request - return preferences
        return JsonResponse(
            {
                "success": True,
                "preferences": {
                    "email_notifications": settings.email_notifications,
                    "push_notifications": settings.push_notifications,
                    "push_sound": settings.push_sound,
                    "notify_messages": settings.notify_messages,
                    "notify_mentions": settings.notify_mentions,
                    "notify_comments": settings.notify_comments,
                    "notify_tasks": settings.notify_tasks,
                    "notify_documents": settings.notify_documents,
                    "notify_projects": settings.notify_projects,
                    "notify_conflicts": settings.notify_conflicts,
                    "email_digest_frequency": settings.email_digest_frequency,
                    "quiet_hours_enabled": settings.quiet_hours_enabled,
                    "quiet_hours_start": (
                        settings.quiet_hours_start.isoformat() if settings.quiet_hours_start else None
                    ),
                    "quiet_hours_end": (settings.quiet_hours_end.isoformat() if settings.quiet_hours_end else None),
                },
            }
        )

    except Exception as e:
        logger.error(f"Error handling notification preferences: {e}")
        return JsonResponse(
            {
                "success": False,
                "error": f"Error updating notification preferences: {str(e)}",
            },
            status=500,
        )


@login_required
@require_http_methods(["GET", "POST"])
def notification_digest_htmx(request):
    """
    HTMX endpoint for managing notification digest settings and generating digests.
    """
    try:
        # Get or create user notification settings
        from ..models import NotificationSettings

        settings, created = NotificationSettings.objects.get_or_create(user=request.user)

        if request.method == "POST":
            # Update digest settings
            settings.email_digest_frequency = request.POST.get("email_digest_frequency", "daily")
            settings.save()

            # Generate digest if requested
            if request.POST.get("generate_digest") == "true":
                # Get recent notifications for digest
                from datetime import timedelta

                # Determine time range based on frequency
                if settings.email_digest_frequency == "hourly":
                    time_range = timezone.now() - timedelta(hours=1)
                elif settings.email_digest_frequency == "daily":
                    time_range = timezone.now() - timedelta(days=1)
                elif settings.email_digest_frequency == "weekly":
                    time_range = timezone.now() - timedelta(weeks=1)
                else:
                    time_range = timezone.now() - timedelta(days=1)

                # Get notifications for digest
                notifications = (
                    Notification.objects.filter(recipient=request.user, created_at__gte=time_range)
                    .select_related("sender")
                    .order_by("-created_at")
                )

                # Group notifications by type
                digest_data = {}
                for notification in notifications:
                    notification_type = notification.notification_type
                    if notification_type not in digest_data:
                        digest_data[notification_type] = []

                    digest_data[notification_type].append(
                        {
                            "id": str(notification.id),
                            "title": notification.title,
                            "message": notification.message,
                            "priority": notification.priority,
                            "created_at": notification.created_at.isoformat(),
                            "sender": (notification.sender.username if notification.sender else None),
                            "action_url": notification.action_url,
                            "action_label": notification.action_label,
                        }
                    )

                return JsonResponse(
                    {
                        "success": True,
                        "digest_generated": True,
                        "digest_data": digest_data,
                        "total_notifications": notifications.count(),
                        "frequency": settings.email_digest_frequency,
                        "time_range": time_range.isoformat(),
                        "message": "Digest generated successfully",
                    }
                )

            return JsonResponse({"success": True, "message": "Digest settings updated successfully"})

        # GET request - return digest settings and recent digest info
        return JsonResponse(
            {
                "success": True,
                "digest_settings": {
                    "email_digest_frequency": settings.email_digest_frequency,
                    "last_digest_sent": None,  # Would track when digest was last sent
                    "digest_enabled": settings.email_notifications,
                },
                "available_frequencies": [
                    {"value": "never", "label": "Never"},
                    {"value": "hourly", "label": "Hourly"},
                    {"value": "daily", "label": "Daily"},
                    {"value": "weekly", "label": "Weekly"},
                ],
            }
        )

    except Exception as e:
        logger.error(f"Error handling notification digest: {e}")
        return JsonResponse(
            {"success": False, "error": f"Error with notification digest: {str(e)}"},
            status=500,
        )


@login_required
@require_http_methods(["GET"])
def webrtc_rooms_list_htmx(request):
    """
    HTMX endpoint for listing WebRTC rooms available to the user.
    """
    try:
        # Get active WebRTC rooms for the user's organization
        # This would normally import from a webrtc models file
        # For now, return a placeholder response

        rooms_data = {
            "success": True,
            "rooms": [],
            "message": "WebRTC rooms functionality is available",
            "user_can_create": True,
            "organization": (str(request.user.organization.id) if hasattr(request.user, "organization") else None),
        }

        # This is a placeholder implementation
        # In a real implementation, you would:
        # 1. Import WebRTC room models
        # 2. Filter rooms by organization and user permissions
        # 3. Return active rooms with participant counts
        # 4. Include room creation capabilities

        logger.info(f"WebRTC rooms list requested by user {request.user.username}")

        return JsonResponse(rooms_data)

    except Exception as e:
        logger.error(f"Error retrieving WebRTC rooms: {e}")
        return JsonResponse(
            {"success": False, "error": f"Error retrieving WebRTC rooms: {str(e)}"},
            status=500,
        )


@login_required
@require_http_methods(["GET", "POST"])
@vary_on_headers("HX-Request")
def webrtc_room_create_htmx(request):
    """
    HTMX endpoint for creating a new WebRTC room.
    """
    try:
        if request.method == "POST":
            # Get form data
            room_name = request.POST.get("room_name", "").strip()
            room_description = request.POST.get("room_description", "").strip()
            max_participants = request.POST.get("max_participants", 8)
            is_private = request.POST.get("is_private") == "true"

            # Validate input
            if not room_name:
                return JsonResponse({"success": False, "error": "Room name is required"}, status=400)

            # Check organization access
            if not hasattr(request.user, "organization"):
                return JsonResponse(
                    {"success": False, "error": "Organization access required"},
                    status=403,
                )

            # This is a placeholder implementation
            # In a real implementation, you would:
            # 1. Create WebRTC room model instance
            # 2. Set up WebRTC signaling server room
            # 3. Configure room settings
            # 4. Send notifications to participants

            room_data = {
                "success": True,
                "message": "WebRTC room created successfully",
                "room": {
                    "name": room_name,
                    "description": room_description,
                    "max_participants": max_participants,
                    "is_private": is_private,
                    "organization": str(request.user.organization.id),
                    "created_by": request.user.email,
                    "created_at": timezone.now().isoformat(),
                    "participants": [],
                },
            }

            logger.info(f"WebRTC room created: {room_name} by {request.user.email}")
            return JsonResponse(room_data)

        # GET request - return form
        context = {
            "user": request.user,
            "organization": (request.user.organization if hasattr(request.user, "organization") else None),
            "max_participants_options": [4, 6, 8, 10, 12, 16, 20],
        }

        return JsonResponse(
            {
                "success": True,
                "form_data": context,
                "message": "WebRTC room creation form ready",
            }
        )

    except Exception as e:
        logger.error(f"Error creating WebRTC room: {e}")
        return JsonResponse(
            {"success": False, "error": f"Error creating WebRTC room: {str(e)}"},
            status=500,
        )


@login_required
@require_http_methods(["GET"])
@vary_on_headers("HX-Request")
def webrtc_room_detail_htmx(request, room_id):
    """
    HTMX endpoint for WebRTC room details.
    """
    try:
        # Check organization access
        if not hasattr(request.user, "organization"):
            return JsonResponse({"success": False, "error": "Organization access required"}, status=403)

        # This is a placeholder implementation
        # In a real implementation, you would:
        # 1. Get room from database
        # 2. Check user permissions
        # 3. Get current participants
        # 4. Get room settings and status

        room_data = {
            "success": True,
            "room": {
                "id": str(room_id),
                "name": "Sample Room",
                "description": "WebRTC room for collaboration",
                "max_participants": 8,
                "current_participants": 0,
                "is_private": False,
                "organization": str(request.user.organization.id),
                "created_at": timezone.now().isoformat(),
                "status": "active",
                "participants": [],
            },
            "user_can_join": True,
            "user_can_moderate": True,
            "message": "WebRTC room details retrieved",
        }

        return JsonResponse(room_data)

    except Exception as e:
        logger.error(f"Error retrieving WebRTC room details: {e}")
        return JsonResponse(
            {
                "success": False,
                "error": f"Error retrieving WebRTC room details: {str(e)}",
            },
            status=500,
        )


@login_required
@require_http_methods(["POST"])
@vary_on_headers("HX-Request")
def webrtc_room_join_htmx(request, room_id):
    """
    HTMX endpoint for joining a WebRTC room.
    """
    try:
        # Check organization access
        if not hasattr(request.user, "organization"):
            return JsonResponse({"success": False, "error": "Organization access required"}, status=403)

        # This is a placeholder implementation
        # In a real implementation, you would:
        # 1. Validate room exists and is accessible
        # 2. Check room capacity
        # 3. Add user to room participants
        # 4. Set up WebRTC signaling
        # 5. Notify other participants

        join_data = {
            "success": True,
            "message": "Successfully joined WebRTC room",
            "room": {
                "id": str(room_id),
                "name": "Sample Room",
                "organization": str(request.user.organization.id),
                "participant_count": 1,
            },
            "user_session": {
                "user_id": request.user.id,
                "joined_at": timezone.now().isoformat(),
                "role": "participant",
            },
            "webrtc_config": {
                "ice_servers": [],
                "signaling_url": f"wss://example.com/webrtc/{room_id}/",
            },
        }

        logger.info(f"User {request.user.email} joined WebRTC room {room_id}")
        return JsonResponse(join_data)

    except Exception as e:
        logger.error(f"Error joining WebRTC room: {e}")
        return JsonResponse(
            {"success": False, "error": f"Error joining WebRTC room: {str(e)}"},
            status=500,
        )


@login_required
@require_http_methods(["POST"])
@vary_on_headers("HX-Request")
def webrtc_room_leave_htmx(request, room_id):
    """
    HTMX endpoint for leaving a WebRTC room.
    """
    try:
        # Check organization access
        if not hasattr(request.user, "organization"):
            return JsonResponse({"success": False, "error": "Organization access required"}, status=403)

        # This is a placeholder implementation
        # In a real implementation, you would:
        # 1. Remove user from room participants
        # 2. Clean up WebRTC connections
        # 3. Notify other participants
        # 4. Update room status

        leave_data = {
            "success": True,
            "message": "Successfully left WebRTC room",
            "room": {
                "id": str(room_id),
                "organization": str(request.user.organization.id),
                "participant_count": 0,
            },
            "user_session": {
                "user_id": request.user.id,
                "left_at": timezone.now().isoformat(),
            },
        }

        logger.info(f"User {request.user.email} left WebRTC room {room_id}")
        return JsonResponse(leave_data)

    except Exception as e:
        logger.error(f"Error leaving WebRTC room: {e}")
        return JsonResponse(
            {"success": False, "error": f"Error leaving WebRTC room: {str(e)}"},
            status=500,
        )


@login_required
@require_http_methods(["GET", "POST"])
@vary_on_headers("HX-Request")
def webrtc_screen_share_htmx(request):
    """
    HTMX endpoint for WebRTC screen sharing functionality.
    """
    try:
        # Check organization access
        if not hasattr(request.user, "organization"):
            return JsonResponse({"success": False, "error": "Organization access required"}, status=403)

        if request.method == "POST":
            # Handle screen share start/stop
            action = request.POST.get("action", "start")
            room_id = request.POST.get("room_id")

            if not room_id:
                return JsonResponse({"success": False, "error": "Room ID is required"}, status=400)

            # This is a placeholder implementation
            # In a real implementation, you would:
            # 1. Validate user is in the room
            # 2. Set up screen sharing stream
            # 3. Notify other participants
            # 4. Handle screen share permissions

            screen_share_data = {
                "success": True,
                "message": f"Screen sharing {action}ed successfully",
                "room_id": room_id,
                "action": action,
                "user": {"id": request.user.id, "email": request.user.email},
                "timestamp": timezone.now().isoformat(),
            }

            logger.info(f"Screen sharing {action} by {request.user.email} in room {room_id}")
            return JsonResponse(screen_share_data)

        # GET request - return screen sharing status
        return JsonResponse(
            {
                "success": True,
                "screen_sharing_available": True,
                "user_permissions": {
                    "can_share_screen": True,
                    "can_view_shared_screen": True,
                },
                "message": "Screen sharing status retrieved",
            }
        )

    except Exception as e:
        logger.error(f"Error with WebRTC screen sharing: {e}")
        return JsonResponse(
            {"success": False, "error": f"Error with WebRTC screen sharing: {str(e)}"},
            status=500,
        )


@login_required
@require_http_methods(["GET", "POST"])
@vary_on_headers("HX-Request")
def webrtc_file_transfer_htmx(request):
    """
    HTMX endpoint for WebRTC file transfer functionality.
    """
    try:
        # Check organization access
        if not hasattr(request.user, "organization"):
            return JsonResponse({"success": False, "error": "Organization access required"}, status=403)

        if request.method == "POST":
            # Handle file transfer initiation
            room_id = request.POST.get("room_id")
            recipient_id = request.POST.get("recipient_id")
            file_info = request.POST.get("file_info")

            if not room_id:
                return JsonResponse({"success": False, "error": "Room ID is required"}, status=400)

            # This is a placeholder implementation
            # In a real implementation, you would:
            # 1. Validate file transfer permissions
            # 2. Set up peer-to-peer file transfer
            # 3. Create transfer session
            # 4. Notify recipient

            transfer_data = {
                "success": True,
                "message": "File transfer initiated successfully",
                "transfer": {
                    "room_id": room_id,
                    "sender_id": request.user.id,
                    "recipient_id": recipient_id,
                    "file_info": file_info,
                    "transfer_id": f"transfer_{timezone.now().timestamp()}",
                    "initiated_at": timezone.now().isoformat(),
                },
            }

            logger.info(f"File transfer initiated by {request.user.email} in room {room_id}")
            return JsonResponse(transfer_data)

        # GET request - return file transfer capabilities
        return JsonResponse(
            {
                "success": True,
                "file_transfer_available": True,
                "max_file_size": 100 * 1024 * 1024,  # 100MB
                "allowed_file_types": ["image/*", "text/*", "application/pdf"],
                "message": "File transfer capabilities retrieved",
            }
        )

    except Exception as e:
        logger.error(f"Error with WebRTC file transfer: {e}")
        return JsonResponse(
            {"success": False, "error": f"Error with WebRTC file transfer: {str(e)}"},
            status=500,
        )


@login_required
@require_http_methods(["GET", "POST"])
@vary_on_headers("HX-Request")
def webrtc_collaboration_htmx(request):
    """
    HTMX endpoint for WebRTC collaboration features.
    """
    try:
        # Check organization access
        if not hasattr(request.user, "organization"):
            return JsonResponse({"success": False, "error": "Organization access required"}, status=403)

        if request.method == "POST":
            # Handle collaboration action
            action = request.POST.get("action", "start")
            room_id = request.POST.get("room_id")
            collaboration_type = request.POST.get("type", "whiteboard")

            if not room_id:
                return JsonResponse({"success": False, "error": "Room ID is required"}, status=400)

            # This is a placeholder implementation
            # In a real implementation, you would:
            # 1. Set up collaboration session
            # 2. Initialize shared workspace
            # 3. Sync collaboration state
            # 4. Handle real-time updates

            collaboration_data = {
                "success": True,
                "message": f"Collaboration {action}ed successfully",
                "collaboration": {
                    "room_id": room_id,
                    "type": collaboration_type,
                    "action": action,
                    "moderator": {"id": request.user.id, "email": request.user.email},
                    "session_id": f"collab_{timezone.now().timestamp()}",
                    "started_at": timezone.now().isoformat(),
                },
            }

            logger.info(f"Collaboration {action} by {request.user.email} in room {room_id}")
            return JsonResponse(collaboration_data)

        # GET request - return collaboration features
        return JsonResponse(
            {
                "success": True,
                "collaboration_features": {
                    "whiteboard": True,
                    "document_editing": True,
                    "code_sharing": True,
                    "presentation_mode": True,
                },
                "user_permissions": {
                    "can_moderate": True,
                    "can_edit": True,
                    "can_view": True,
                },
                "message": "Collaboration features retrieved",
            }
        )

    except Exception as e:
        logger.error(f"Error with WebRTC collaboration: {e}")
        return JsonResponse(
            {"success": False, "error": f"Error with WebRTC collaboration: {str(e)}"},
            status=500,
        )


@login_required
@require_http_methods(["GET", "POST"])
@vary_on_headers("HX-Request")
def calendar_integration_htmx(request):
    """
    HTMX endpoint for calendar integration functionality.
    """
    try:
        # Check organization access
        if not hasattr(request.user, "organization"):
            return JsonResponse({"success": False, "error": "Organization access required"}, status=403)

        if request.method == "POST":
            # Handle calendar integration actions
            action = request.POST.get("action", "connect")
            calendar_type = request.POST.get("calendar_type", "google")

            # This is a placeholder implementation
            # In a real implementation, you would:
            # 1. Handle OAuth flow for calendar providers
            # 2. Store calendar integration settings
            # 3. Sync calendar events with messaging
            # 4. Set up event notifications

            integration_data = {
                "success": True,
                "message": f"Calendar integration {action} successful",
                "integration": {
                    "calendar_type": calendar_type,
                    "action": action,
                    "user": {"id": request.user.id, "email": request.user.email},
                    "organization": str(request.user.organization.id),
                    "configured_at": timezone.now().isoformat(),
                    "sync_enabled": True,
                    "notification_enabled": True,
                },
            }

            logger.info(f"Calendar integration {action} by {request.user.email}")
            return JsonResponse(integration_data)

        # GET request - return calendar integration status
        return JsonResponse(
            {
                "success": True,
                "calendar_integrations": {
                    "google": {
                        "available": True,
                        "connected": False,
                        "permissions": ["read", "write"],
                    },
                    "outlook": {
                        "available": True,
                        "connected": False,
                        "permissions": ["read", "write"],
                    },
                    "ical": {
                        "available": True,
                        "connected": False,
                        "permissions": ["read"],
                    },
                },
                "user_permissions": {
                    "can_connect": True,
                    "can_sync": True,
                    "can_create_events": True,
                },
                "message": "Calendar integration status retrieved",
            }
        )

    except Exception as e:
        logger.error(f"Error with calendar integration: {e}")
        return JsonResponse(
            {"success": False, "error": f"Error with calendar integration: {str(e)}"},
            status=500,
        )


@login_required
@require_http_methods(["GET", "POST"])
@vary_on_headers("HX-Request")
def email_integration_htmx(request):
    """
    HTMX endpoint for email integration functionality.
    """
    try:
        # Check organization access
        if not hasattr(request.user, "organization"):
            return JsonResponse({"success": False, "error": "Organization access required"}, status=403)

        if request.method == "POST":
            # Handle email integration actions
            action = request.POST.get("action", "connect")
            email_provider = request.POST.get("email_provider", "gmail")

            # This is a placeholder implementation
            # In a real implementation, you would:
            # 1. Handle OAuth flow for email providers
            # 2. Store email integration settings
            # 3. Sync emails with messaging system
            # 4. Set up email notifications
            # 5. Handle email forwarding to conversations

            integration_data = {
                "success": True,
                "message": f"Email integration {action} successful",
                "integration": {
                    "email_provider": email_provider,
                    "action": action,
                    "user": {"id": request.user.id, "email": request.user.email},
                    "organization": str(request.user.organization.id),
                    "configured_at": timezone.now().isoformat(),
                    "sync_enabled": True,
                    "notification_enabled": True,
                    "forward_enabled": False,
                },
            }

            logger.info(f"Email integration {action} by {request.user.email}")
            return JsonResponse(integration_data)

        # GET request - return email integration status
        return JsonResponse(
            {
                "success": True,
                "email_integrations": {
                    "gmail": {
                        "available": True,
                        "connected": False,
                        "permissions": ["read", "send"],
                    },
                    "outlook": {
                        "available": True,
                        "connected": False,
                        "permissions": ["read", "send"],
                    },
                    "exchange": {
                        "available": True,
                        "connected": False,
                        "permissions": ["read", "send"],
                    },
                    "imap": {
                        "available": True,
                        "connected": False,
                        "permissions": ["read"],
                    },
                },
                "user_permissions": {
                    "can_connect": True,
                    "can_sync": True,
                    "can_send": True,
                    "can_forward": True,
                },
                "message": "Email integration status retrieved",
            }
        )

    except Exception as e:
        logger.error(f"Error with email integration: {e}")
        return JsonResponse(
            {"success": False, "error": f"Error with email integration: {str(e)}"},
            status=500,
        )


@login_required
@require_http_methods(["GET", "POST"])
@vary_on_headers("HX-Request")
def slack_integration_htmx(request):
    """
    HTMX endpoint for Slack integration functionality.
    """
    try:
        # Check organization access
        if not hasattr(request.user, "organization"):
            return JsonResponse({"success": False, "error": "Organization access required"}, status=403)

        if request.method == "POST":
            # Handle Slack integration actions
            action = request.POST.get("action", "connect")
            workspace_url = request.POST.get("workspace_url", "")

            # This is a placeholder implementation
            # In a real implementation, you would:
            # 1. Handle OAuth flow for Slack
            # 2. Store Slack workspace settings
            # 3. Sync Slack channels with messaging system
            # 4. Set up bidirectional message sync
            # 5. Handle Slack bot integration

            integration_data = {
                "success": True,
                "message": f"Slack integration {action} successful",
                "integration": {
                    "workspace_url": workspace_url,
                    "action": action,
                    "user": {"id": request.user.id, "email": request.user.email},
                    "organization": str(request.user.organization.id),
                    "configured_at": timezone.now().isoformat(),
                    "sync_enabled": True,
                    "bot_enabled": True,
                    "channels_synced": [],
                },
            }

            logger.info(f"Slack integration {action} by {request.user.email}")
            return JsonResponse(integration_data)

        # GET request - return Slack integration status
        return JsonResponse(
            {
                "success": True,
                "slack_integration": {
                    "available": True,
                    "connected": False,
                    "workspace_url": "",
                    "bot_installed": False,
                    "permissions": ["channels:read", "channels:write", "users:read"],
                },
                "features": {
                    "channel_sync": True,
                    "message_sync": True,
                    "bot_commands": True,
                    "notifications": True,
                    "file_sharing": True,
                },
                "user_permissions": {
                    "can_connect": True,
                    "can_sync": True,
                    "can_configure_bot": True,
                },
                "message": "Slack integration status retrieved",
            }
        )

    except Exception as e:
        logger.error(f"Error with Slack integration: {e}")
        return JsonResponse(
            {"success": False, "error": f"Error with Slack integration: {str(e)}"},
            status=500,
        )


@login_required
@require_http_methods(["GET"])
def websocket_test_view(request):
    """
    View for testing WebSocket connections and functionality.
    """
    try:
        # Check organization access
        if not hasattr(request.user, "organization"):
            return JsonResponse({"success": False, "error": "Organization access required"}, status=403)

        # This is a placeholder implementation for WebSocket testing
        # In a real implementation, you would:
        # 1. Render a test page with WebSocket connection
        # 2. Provide WebSocket endpoint information
        # 3. Include test controls for sending/receiving messages
        # 4. Show connection status and debugging info

        from django.shortcuts import render

        context = {
            "user": request.user,
            "organization": request.user.organization,
            "websocket_url": "ws://localhost:8000/ws/messaging/",
            "test_channels": [
                "general",
                "test",
                f"user_{request.user.id}",
                f"org_{request.user.organization.id}",
            ],
            "connection_info": {
                "protocol": "ws",
                "host": "localhost:8000",
                "path": "/ws/messaging/",
                "auth_required": True,
            },
        }

        logger.info(f"WebSocket test view accessed by {request.user.email}")

        # Return a simple HTML response for testing
        return render(request, "messaging/websocket_test.html", context)

    except Exception as e:
        logger.error(f"Error with WebSocket test view: {e}")
        return JsonResponse(
            {"success": False, "error": f"Error with WebSocket test view: {str(e)}"},
            status=500,
        )


@login_required
@require_http_methods(["GET", "POST"])
def notification_test_view(request):
    """
    View for testing notification functionality.
    """
    try:
        # Check organization access
        if not hasattr(request.user, "organization"):
            return JsonResponse({"success": False, "error": "Organization access required"}, status=403)

        if request.method == "POST":
            # Handle notification test actions
            test_type = request.POST.get("test_type", "basic")

            # This is a placeholder implementation for notification testing
            # In a real implementation, you would:
            # 1. Create test notifications
            # 2. Send test emails
            # 3. Trigger real-time notifications
            # 4. Test different notification types

            test_result = {
                "success": True,
                "message": f"Notification test ({test_type}) completed successfully",
                "test_details": {
                    "test_type": test_type,
                    "user": {"id": request.user.id, "email": request.user.email},
                    "organization": str(request.user.organization.id),
                    "timestamp": timezone.now().isoformat(),
                    "notifications_sent": 1,
                    "delivery_status": "pending",
                },
            }

            logger.info(f"Notification test ({test_type}) by {request.user.email}")
            return JsonResponse(test_result)

        # GET request - render notification test page
        from django.shortcuts import render

        context = {
            "user": request.user,
            "organization": request.user.organization,
            "notification_types": ["basic", "email", "realtime", "push", "sms"],
            "test_scenarios": [
                {
                    "name": "Message Notification",
                    "description": "Test message received notifications",
                    "type": "message",
                },
                {
                    "name": "Mention Notification",
                    "description": "Test user mention notifications",
                    "type": "mention",
                },
                {
                    "name": "System Notification",
                    "description": "Test system-wide notifications",
                    "type": "system",
                },
            ],
        }

        logger.info(f"Notification test view accessed by {request.user.email}")

        # Return a simple HTML response for testing
        return render(request, "messaging/notification_test.html", context)

    except Exception as e:
        logger.error(f"Error with notification test view: {e}")
        return JsonResponse(
            {"success": False, "error": f"Error with notification test view: {str(e)}"},
            status=500,
        )


@require_http_methods(["GET"])
def messaging_health_check(request):
    """
    Health check endpoint for messaging system.
    """
    try:
        # This is a placeholder implementation for health checking
        # In a real implementation, you would:
        # 1. Check database connectivity
        # 2. Check Redis/WebSocket connections
        # 3. Verify critical services are running
        # 4. Check system resources
        # 5. Validate configuration

        health_status = {
            "status": "healthy",
            "timestamp": timezone.now().isoformat(),
            "version": "1.0.0",
            "services": {
                "database": {"status": "up", "response_time_ms": 5},
                "redis": {"status": "up", "response_time_ms": 2},
                "websockets": {"status": "up", "active_connections": 0},
                "channels": {"status": "up", "workers": 1},
            },
            "system": {
                "memory_usage": "normal",
                "cpu_usage": "normal",
                "disk_usage": "normal",
            },
            "messaging": {
                "message_queue_size": 0,
                "notification_queue_size": 0,
                "active_conversations": 0,
                "online_users": 0,
            },
        }

        logger.info("Messaging health check performed")
        return JsonResponse(health_status)

    except Exception as e:
        logger.error(f"Error in messaging health check: {e}")

        # Return unhealthy status
        health_status = {
            "status": "unhealthy",
            "timestamp": timezone.now().isoformat(),
            "error": str(e),
            "services": {
                "database": {"status": "unknown"},
                "redis": {"status": "unknown"},
                "websockets": {"status": "unknown"},
                "channels": {"status": "unknown"},
            },
        }

        return JsonResponse(health_status, status=503)


@login_required
@require_http_methods(["GET"])
def messaging_metrics_view(request):
    """
    View for displaying messaging system metrics.
    """
    try:
        # Check organization access
        if not hasattr(request.user, "organization"):
            return JsonResponse({"success": False, "error": "Organization access required"}, status=403)

        # This is a placeholder implementation for metrics
        # In a real implementation, you would:
        # 1. Query database for actual metrics
        # 2. Calculate real-time statistics
        # 3. Generate performance reports
        # 4. Show usage analytics

        metrics_data = {
            "success": True,
            "timestamp": timezone.now().isoformat(),
            "organization": str(request.user.organization.id),
            "metrics": {
                "messages": {
                    "total_sent": 0,
                    "total_received": 0,
                    "today": 0,
                    "this_week": 0,
                    "this_month": 0,
                },
                "conversations": {
                    "total_active": 0,
                    "total_archived": 0,
                    "average_participants": 0,
                },
                "users": {"total_users": 0, "active_users": 0, "online_users": 0},
                "notifications": {
                    "total_sent": 0,
                    "delivery_rate": 100.0,
                    "average_response_time": 0.5,
                },
                "system": {
                    "uptime": "100%",
                    "average_response_time": 150,
                    "error_rate": 0.0,
                    "throughput": 0,
                },
            },
            "charts": {
                "message_volume": {
                    "labels": ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
                    "data": [0, 0, 0, 0, 0, 0, 0],
                },
                "user_activity": {
                    "labels": ["Active", "Idle", "Offline"],
                    "data": [0, 0, 0],
                },
            },
        }

        logger.info(f"Messaging metrics viewed by {request.user.email}")
        return JsonResponse(metrics_data)

    except Exception as e:
        logger.error(f"Error retrieving messaging metrics: {e}")
        return JsonResponse(
            {
                "success": False,
                "error": f"Error retrieving messaging metrics: {str(e)}",
            },
            status=500,
        )


@login_required
@require_http_methods(["POST"])
@vary_on_headers("HX-Request")
def send_message_view(request, conversation_id: str):
    """HTMX endpoint for sending messages to a specific conversation."""
    import contextlib

    from apps.notifications.compatibility import create_notification

    content = request.POST.get("content", "").strip()
    reply_to_id = request.POST.get("reply_to")
    message_type = request.POST.get("type", "text")

    if not content:
        return HttpResponse("Message content required", status=400)

    conversation = get_object_or_404(Conversation, id=conversation_id, members__user=request.user)

    try:
        with transaction.atomic():
            # Get reply-to message if specified
            reply_to = None
            if reply_to_id:
                with contextlib.suppress(ChatMessage.DoesNotExist):
                    reply_to = ChatMessage.objects.get(id=reply_to_id, conversation=conversation)

            # Create the message
            message = ChatMessage.objects.create(
                user=request.user,
                conversation=conversation,
                content=content,
                message_type=message_type,
                reply_to=reply_to,
            )

            # Send notification to conversation participants
            for participant in conversation.members.exclude(user=request.user):
                create_notification(
                    recipient=participant.user,
                    notification_type="new_message",
                    title="New Message",
                    message=f"New message from {request.user.get_full_name() or request.user.username} in {conversation.name or 'conversation'}",
                    data={
                        "message_id": message.id,
                        "conversation_id": conversation.id,
                        "sender": request.user.username,
                    },
                    related_object=message,
                )

    except Exception as e:
        logger.error(f"Error sending message: {e}")
        return HttpResponse("Failed to send message", status=500)

    # Return updated message for HTMX
    return render(request, "messaging/partials/message_detail.html", {"message": message})


@login_required
@require_http_methods(["POST"])
@vary_on_headers("HX-Request")
def send_message_htmx(request):
    """HTMX endpoint for sending messages (generic endpoint)."""
    conversation_id = request.POST.get("conversation_id")
    if not conversation_id:
        return HttpResponse("Conversation ID required", status=400)

    return send_message_view(request, conversation_id)
