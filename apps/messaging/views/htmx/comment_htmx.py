"""Comment HTMX Views for CLEAR Application

This module contains HTMX view functions that handle comments, replies,
and feedback functionality across the application.
"""

from rest_framework import status
from requests.exceptions import HTT<PERSON><PERSON>rror, ConnectionError, TimeoutError
from typing import ClassVar
from celery.decorators import task
from django.contrib import admin
from django.contrib.auth.decorators import login_required
from django.db import models
from django.http import HttpResponse
from django.shortcuts import render
from django.utils import timezone
from django.views.decorators.http import require_http_methods
from rest_framework import permissions
from rest_framework.response import Response
from typing import List
import logging
logger = logging.getLogger(__name__)

from apps.analytics.models import Activity
from apps.messaging.models import Comment
from apps.projects.models import Project


# ========== COMMENT COUNTS ==========


def comment_count_htmx(request, commentable_type, commentable_id):
    """Get comment count for an entity via HTMX"""
    commentable_type = commentable_type.lower()
    count = Comment.objects.count_for_entity(commentable_type, commentable_id)

    return render(
        request,
        "shared/components/comments/comment_count.html",
        {
            "count": count,
            "commentable_type": commentable_type,
            "commentable_id": commentable_id,
        },
    )


# ========== COMMENT CREATION ==========


@login_required
@require_http_methods(["POST"])
def comment_create_htmx(request):
    """Create a new comment via HTMX"""
    content = request.POST.get("content", "").strip()
    commentable_type = request.POST.get("commentable_type", "").lower()
    commentable_id = request.POST.get("commentable_id", "")
    parent_id = request.POST.get("parent_id")  # For replies

    if not content or not commentable_type or not commentable_id:
        return HttpResponse('<div class="alert alert-danger">Missing required fields</div>', status=400)

    # Validate commentable_type
    valid_types = ["project", "task", "utility", "conflict", "document", "note"]
    if commentable_type not in valid_types:
        return HttpResponse('<div class="alert alert-danger">Invalid entity type</div>', status=400)

    # Create the comment
    comment_data = {
        "commentable_type": commentable_type,
        "commentable_id": commentable_id,
        "user": request.user,
        "content": content,
    }

    if parent_id:
        try:
            parent_comment = Comment.objects.get(id=parent_id, deleted_at__isnull=True)
            comment_data["parent"] = parent_comment
        except Comment.DoesNotExist:
            return HttpResponse(
                '<div class="alert alert-danger">Parent comment not found</div>',
                status=400,
            )

    comment = Comment.objects.create(**comment_data)

    # Create activity log if possible
    try:
        if commentable_type == "project":
            Project.objects.get(id=commentable_id)
            # Create activity using the existing Activity model
            Activity.objects.create(
                user=request.user,
                action_type="comment_added",
                description=(
                    f"Added a comment: {content[:100]}..." if len(content) > 100 else f"Added a comment: {content}"
                ),
                target_id=str(comment.id),
            )
    except (ConnectionError, TimeoutError, HTTPError):
        pass  # Don't fail comment creation if activity logging fails

    # Return the new comment HTML
    return render(
        request,
        "shared/components/comments/comment_item.html",
        {
            "comment": comment,
            "can_edit": True,
            "can_delete": True,
        },
    )


# ========== COMMENT DELETION ==========


@login_required
@require_http_methods(["POST"])
def comment_delete_htmx(request, comment_id):
    """Soft delete a comment via HTMX"""
    try:
        comment = Comment.objects.get(id=comment_id, deleted_at__isnull=True)
    except Comment.DoesNotExist:
        return HttpResponse('<div class="alert alert-danger">Comment not found</div>', status=404)

    # Check permissions - user can delete their own comments or admin can delete any
    if comment.user != request.user and not request.user.is_staff:
        return HttpResponse('<div class="alert alert-danger">Permission denied</div>', status=403)

    # Soft delete the comment
    comment.deleted_at = timezone.now()
    comment.save()

    # Create activity log
    try:
        Activity.objects.create(
            user=request.user,
            action_type="comment_deleted",
            description=(
                f"Deleted a comment: {comment.content[:100]}..."
                if len(comment.content) > 100
                else f"Deleted a comment: {comment.content}"
            ),
            target_id=str(comment.id),
        )
    except (ConnectionError, TimeoutError, HTTPError):
        pass  # Don't fail deletion if activity logging fails

    # Return success message
    return render(
        request,
        "shared/components/comments/comment_deleted.html",
        {"comment_id": comment_id},
    )


# ========== COMMENT LISTING ==========


@login_required
def comment_list_htmx(request, commentable_type, commentable_id):
    """Get comments list for an entity via HTMX"""
    commentable_type = commentable_type.lower()

    # Validate commentable_type
    valid_types = ["project", "task", "utility", "conflict", "document", "note"]
    if commentable_type not in valid_types:
        return HttpResponse('<div class="alert alert-danger">Invalid entity type</div>', status=400)

    # Get comments for the entity (only top-level comments, replies are loaded
    # separately)
    comments = Comment.objects.filter(
        commentable_type=commentable_type,
        commentable_id=commentable_id,
        parent__isnull=True,  # Only top-level comments
        deleted_at__isnull=True,
    ).order_by("-created_at")

    return render(
        request,
        "shared/components/comments/comment_list.html",
        {
            "comments": comments,
            "commentable_type": commentable_type,
            "commentable_id": commentable_id,
            "user": request.user,
        },
    )


# ========== COMMENT REPLIES ==========


@login_required
def comment_replies_htmx(request, comment_id):
    """Get replies for a comment via HTMX"""
    try:
        parent_comment = Comment.objects.get(id=comment_id, deleted_at__isnull=True)
    except Comment.DoesNotExist:
        return HttpResponse('<div class="alert alert-danger">Comment not found</div>', status=404)

    # Get replies for this comment
    replies = Comment.objects.filter(parent=parent_comment, deleted_at__isnull=True).order_by("created_at")

    return render(
        request,
        "shared/components/comments/comment_replies.html",
        {"replies": replies, "parent_comment": parent_comment, "user": request.user},
    )


# ========== COMMENT UPDATES ==========


@login_required
@require_http_methods(["POST"])
def comment_update_htmx(request, comment_id):
    """Update a comment via HTMX"""
    try:
        comment = Comment.objects.get(id=comment_id, deleted_at__isnull=True)
    except Comment.DoesNotExist:
        return HttpResponse('<div class="alert alert-danger">Comment not found</div>', status=404)

    # Check permissions - user can only edit their own comments
    if comment.user != request.user:
        return HttpResponse('<div class="alert alert-danger">Permission denied</div>', status=403)

    # Get new content
    new_content = request.POST.get("content", "").strip()
    if not new_content:
        return HttpResponse('<div class="alert alert-danger">Content cannot be empty</div>', status=400)

    # Store original content for activity log
    original_content = comment.content

    # Update the comment
    comment.content = new_content
    comment.updated_at = timezone.now()
    comment.save()

    # Create activity log
    try:
        Activity.objects.create(
            user=request.user,
            action_type="comment_updated",
            description=f'Updated comment from "{original_content[:50]}..." to "{new_content[:50]}..."',
            target_id=str(comment.id),
        )
    except (ConnectionError, TimeoutError, HTTPError):
        pass  # Don't fail update if activity logging fails

    # Return updated comment HTML
    return render(
        request,
        "shared/components/comments/comment_item.html",
        {"comment": comment, "can_edit": True, "can_delete": True, "updated": True},
    )


@login_required
@require_http_methods(["GET", "POST"])
def comment_form_htmx(request, content_type, object_id):
    """
    HTMX endpoint for comment form display and submission.
    """
    try:
        if request.method == "GET":
            # Show the comment form
            return render(
                request,
                "messaging/partials/comment_form.html",
                {
                    "content_type": content_type,
                    "object_id": object_id,
                    "user": request.user,
                },
            )

        elif request.method == "POST":
            # Process the comment form submission
            content = request.POST.get("content", "").strip()
            parent_id = request.POST.get("parent_id")

            if not content:
                return render(
                    request,
                    "messaging/partials/comment_form_error.html",
                    {
                        "error": "Comment content is required",
                        "content_type": content_type,
                        "object_id": object_id,
                    },
                )

            # Create the comment
            comment = Comment.objects.create(
                content=content,
                author=request.user,
                content_type=content_type,
                object_id=object_id,
                parent_id=parent_id,
                organization=request.user.organization,
            )

            return render(
                request,
                "messaging/partials/comment_form_success.html",
                {"comment": comment, "message": "Comment added successfully"},
            )

    except Exception as e:
        logger.error(f"Error handling comment form: {e}")
        return render(
            request,
            "messaging/partials/comment_form_error.html",
            {"error": str(e), "content_type": content_type, "object_id": object_id},
        )


@login_required
@require_http_methods(["POST"])
def add_comment_htmx(request, content_type, object_id):
    """
    HTMX endpoint for adding a comment.
    """
    try:
        content = request.POST.get("content", "").strip()
        parent_id = request.POST.get("parent_id")

        if not content:
            return render(
                request,
                "messaging/partials/add_comment_error.html",
                {
                    "error": "Comment content is required",
                    "content_type": content_type,
                    "object_id": object_id,
                },
            )

        # Create the comment
        comment = Comment.objects.create(
            content=content,
            author=request.user,
            content_type=content_type,
            object_id=object_id,
            parent_id=parent_id,
            organization=request.user.organization,
        )

        return render(
            request,
            "messaging/partials/add_comment_success.html",
            {"comment": comment, "message": "Comment added successfully"},
        )

    except Exception as e:
        logger.error(f"Error adding comment: {e}")
        return render(
            request,
            "messaging/partials/add_comment_error.html",
            {"error": str(e), "content_type": content_type, "object_id": object_id},
        )


@login_required
@require_http_methods(["GET"])
def comment_detail_htmx(request, comment_id):
    """
    HTMX endpoint for getting comment details.
    """
    try:
        comment = Comment.objects.select_related("author", "author__organization").get(
            id=comment_id, organization=request.user.organization
        )

        return render(
            request,
            "messaging/partials/comment_detail.html",
            {
                "comment": comment,
                "can_edit": comment.author == request.user,
                "can_delete": comment.author == request.user or request.user.is_staff,
            },
        )

    except Comment.DoesNotExist:
        return render(
            request,
            "messaging/partials/comment_detail_error.html",
            {
                "comment_id": comment_id,
                "error": "Comment not found or you don't have access to it",
            },
        )
    except Exception as e:
        logger.error(f"Error getting comment detail: {e}")
        return render(
            request,
            "messaging/partials/comment_detail_error.html",
            {"comment_id": comment_id, "error": str(e)},
        )


@login_required
@require_http_methods(["GET", "POST"])
def edit_comment_htmx(request, comment_id):
    """
    HTMX endpoint for editing a comment.
    """
    try:
        comment = Comment.objects.get(id=comment_id, author=request.user, organization=request.user.organization)

        if request.method == "GET":
            # Show the edit form
            return render(
                request,
                "messaging/partials/edit_comment_form.html",
                {"comment": comment},
            )

        elif request.method == "POST":
            # Process the edit
            content = request.POST.get("content", "").strip()

            if not content:
                return render(
                    request,
                    "messaging/partials/edit_comment_error.html",
                    {"comment": comment, "error": "Comment content is required"},
                )

            comment.content = content
            comment.updated_at = timezone.now()
            comment.save(update_fields=["content", "updated_at"])

            return render(
                request,
                "messaging/partials/edit_comment_success.html",
                {"comment": comment, "message": "Comment updated successfully"},
            )

    except Comment.DoesNotExist:
        return render(
            request,
            "messaging/partials/edit_comment_error.html",
            {
                "comment_id": comment_id,
                "error": "Comment not found or you don't have access to edit it",
            },
        )
    except Exception as e:
        logger.error(f"Error editing comment: {e}")
        return render(
            request,
            "messaging/partials/edit_comment_error.html",
            {"comment_id": comment_id, "error": str(e)},
        )


@login_required
@require_http_methods(["POST", "DELETE"])
def delete_comment_htmx(request, comment_id):
    """
    HTMX endpoint for deleting a comment.
    """
    try:
        comment = Comment.objects.get(id=comment_id, organization=request.user.organization)

        # Check permissions
        if comment.author != request.user and not request.user.is_staff:
            return render(
                request,
                "messaging/partials/delete_comment_error.html",
                {
                    "comment_id": comment_id,
                    "error": "You don't have permission to delete this comment",
                },
            )

        comment.delete()

        return render(
            request,
            "messaging/partials/delete_comment_success.html",
            {"comment_id": comment_id, "message": "Comment deleted successfully"},
        )

    except Comment.DoesNotExist:
        return render(
            request,
            "messaging/partials/delete_comment_error.html",
            {
                "comment_id": comment_id,
                "error": "Comment not found or you don't have access to it",
            },
        )
    except Exception as e:
        logger.error(f"Error deleting comment: {e}")
        return render(
            request,
            "messaging/partials/delete_comment_error.html",
            {"comment_id": comment_id, "error": str(e)},
        )


@login_required
@require_http_methods(["GET", "POST"])
def reply_comment_htmx(request, comment_id):
    """
    HTMX endpoint for replying to a comment.
    """
    try:
        parent_comment = Comment.objects.get(id=comment_id, organization=request.user.organization)

        if request.method == "GET":
            # Show the reply form
            return render(
                request,
                "messaging/partials/reply_comment_form.html",
                {"parent_comment": parent_comment, "user": request.user},
            )

        elif request.method == "POST":
            # Process the reply
            content = request.POST.get("content", "").strip()

            if not content:
                return render(
                    request,
                    "messaging/partials/reply_comment_error.html",
                    {
                        "parent_comment": parent_comment,
                        "error": "Reply content is required",
                    },
                )

            # Create the reply
            reply = Comment.objects.create(
                content=content,
                author=request.user,
                content_type=parent_comment.content_type,
                object_id=parent_comment.object_id,
                parent=parent_comment,
                organization=request.user.organization,
            )

            return render(
                request,
                "messaging/partials/reply_comment_success.html",
                {
                    "reply": reply,
                    "parent_comment": parent_comment,
                    "message": "Reply added successfully",
                },
            )

    except Comment.DoesNotExist:
        return render(
            request,
            "messaging/partials/reply_comment_error.html",
            {
                "comment_id": comment_id,
                "error": "Comment not found or you don't have access to it",
            },
        )
    except Exception as e:
        logger.error(f"Error replying to comment: {e}")
        return render(
            request,
            "messaging/partials/reply_comment_error.html",
            {"comment_id": comment_id, "error": str(e)},
        )


@login_required
@require_http_methods(["GET"])
def comment_moderation_htmx(request):
    """
    HTMX endpoint for comment moderation.
    """
    try:
        # Only allow staff users to moderate
        if not request.user.is_staff:
            return render(
                request,
                "messaging/partials/comment_moderation_error.html",
                {"error": "You don't have permission to moderate comments"},
            )

        # Get comments that need moderation
        comments_to_moderate = (
            Comment.objects.filter(organization=request.user.organization, is_active=True)
            .select_related("author", "author__organization")
            .order_by("-created_at")[:20]
        )

        return render(
            request,
            "messaging/partials/comment_moderation.html",
            {
                "comments": comments_to_moderate,
                "comment_count": comments_to_moderate.count(),
                "has_comments": comments_to_moderate.exists(),
            },
        )

    except Exception as e:
        logger.error(f"Error getting comment moderation: {e}")
        return render(
            request,
            "messaging/partials/comment_moderation_error.html",
            {"error": str(e)},
        )


@login_required
@require_http_methods(["GET"])
def reported_comments_htmx(request):
    """
    HTMX endpoint for getting reported comments.
    """
    try:
        # Only allow staff users to view reported comments
        if not request.user.is_staff:
            return render(
                request,
                "messaging/partials/reported_comments_error.html",
                {"error": "You don't have permission to view reported comments"},
            )

        # Get reported comments
        reported_comments = (
            Comment.objects.filter(organization=request.user.organization, is_reported=True)
            .select_related("author", "author__organization")
            .order_by("-created_at")[:20]
        )

        return render(
            request,
            "messaging/partials/reported_comments.html",
            {
                "comments": reported_comments,
                "comment_count": reported_comments.count(),
                "has_comments": reported_comments.exists(),
            },
        )

    except Exception as e:
        logger.error(f"Error getting reported comments: {e}")
        return render(
            request,
            "messaging/partials/reported_comments_error.html",
            {"error": str(e)},
        )


@login_required
@require_http_methods(["POST"])
def flag_comment_htmx(request, comment_id):
    """
    HTMX endpoint for flagging a comment.
    """
    try:
        comment = Comment.objects.get(id=comment_id)

        reason = request.POST.get("reason", "").strip()
        description = request.POST.get("description", "").strip()

        if not reason:
            return render(
                request,
                "messaging/partials/flag_comment_error.html",
                {"comment": comment, "error": "Flag reason is required"},
            )

        # Create the flag using the new model
        from ..models import CommentFlag

        flag, created = CommentFlag.objects.get_or_create(
            comment=comment,
            user=request.user,
            defaults={
                "reason": reason,
                "description": description,
            },
        )

        if created:
            # Update flag count and check for auto-hiding
            comment.update_flag_count()

            return render(
                request,
                "messaging/partials/flag_comment_success.html",
                {
                    "comment": comment,
                    "flag": flag,
                    "message": "Comment flagged successfully",
                },
            )
        else:
            return render(
                request,
                "messaging/partials/flag_comment_error.html",
                {"comment": comment, "error": "You have already flagged this comment"},
            )

    except Comment.DoesNotExist:
        return render(
            request,
            "messaging/partials/flag_comment_error.html",
            {
                "comment_id": comment_id,
                "error": "Comment not found or you don't have access to it",
            },
        )
    except Exception as e:
        logger.error(f"Error flagging comment: {e}")
        return render(
            request,
            "messaging/partials/flag_comment_error.html",
            {"comment_id": comment_id, "error": str(e)},
        )


@login_required
@require_http_methods(["GET"])
def moderation_queue_htmx(request):
    """
    HTMX endpoint for moderation queue.
    """
    try:
        # Only allow staff users to access moderation queue
        if not request.user.is_staff:
            return render(
                request,
                "messaging/partials/moderation_queue_error.html",
                {"error": "You don't have permission to access the moderation queue"},
            )

        # Get flagged comments that need moderation
        flagged_comments = (
            Comment.objects.filter(flags__is_resolved=False)
            .select_related("user")
            .prefetch_related("flags__user")
            .distinct()
            .order_by("-flag_count", "-created_at")[:50]
        )

        # Get flag summary for each comment
        for comment in flagged_comments:
            comment.flag_summary = comment.get_flag_summary()

        return render(
            request,
            "messaging/partials/moderation_queue.html",
            {
                "comments": flagged_comments,
                "comment_count": len(flagged_comments),
                "has_comments": len(flagged_comments) > 0,
            },
        )

    except Exception as e:
        logger.error(f"Error getting moderation queue: {e}")
        return render(request, "messaging/partials/moderation_queue_error.html", {"error": str(e)})


@login_required
@require_http_methods(["POST"])
def moderate_comment_htmx(request, comment_id):
    """
    HTMX endpoint for taking moderation action on a comment.
    """
    try:
        # Only allow staff users to moderate
        if not request.user.is_staff:
            return render(
                request,
                "messaging/partials/moderate_comment_error.html",
                {"error": "You don't have permission to moderate comments"},
            )

        comment = Comment.objects.get(id=comment_id)
        action_type = request.POST.get("action_type", "").strip()
        reason = request.POST.get("reason", "").strip()
        notes = request.POST.get("notes", "").strip()

        if not action_type or not reason:
            return render(
                request,
                "messaging/partials/moderate_comment_error.html",
                {"comment": comment, "error": "Action type and reason are required"},
            )

        from ..models import CommentFlag, ModerationAction

        # Create moderation action
        moderation_action = ModerationAction.objects.create(
            comment=comment,
            moderator=request.user,
            action_type=action_type,
            reason=reason,
            notes=notes,
        )

        # Get unresolved flags for this comment
        unresolved_flags = CommentFlag.objects.filter(comment=comment, is_resolved=False)

        # Apply the moderation action
        if action_type == "hide":
            comment.hide_comment()
        elif action_type == "delete":
            comment.soft_delete()
        elif action_type == "approve":
            comment.unhide_comment()

        # Resolve all flags for this comment
        for flag in unresolved_flags:
            flag.resolve(request.user, f"Resolved by moderation action: {action_type}")
            moderation_action.resolved_flags.add(flag)

        return render(
            request,
            "messaging/partials/moderate_comment_success.html",
            {
                "comment": comment,
                "moderation_action": moderation_action,
                "message": f"Moderation action '{action_type}' applied successfully",
            },
        )

    except Comment.DoesNotExist:
        return render(
            request,
            "messaging/partials/moderate_comment_error.html",
            {"comment_id": comment_id, "error": "Comment not found"},
        )
    except Exception as e:
        logger.error(f"Error moderating comment: {e}")
        return render(
            request,
            "messaging/partials/moderate_comment_error.html",
            {"comment_id": comment_id, "error": str(e)},
        )


@login_required
@require_http_methods(["POST"])
def bulk_moderate_htmx(request):
    """
    HTMX endpoint for bulk moderation actions.
    """
    try:
        # Only allow staff users to moderate
        if not request.user.is_staff:
            return render(
                request,
                "messaging/partials/bulk_moderate_error.html",
                {"error": "You don't have permission to moderate comments"},
            )

        comment_ids = request.POST.getlist("comment_ids")
        action_type = request.POST.get("action_type", "").strip()
        reason = request.POST.get("reason", "").strip()

        if not comment_ids or not action_type or not reason:
            return render(
                request,
                "messaging/partials/bulk_moderate_error.html",
                {"error": "Comment IDs, action type, and reason are required"},
            )

        from ..models import CommentFlag, ModerationAction

        comments = Comment.objects.filter(id__in=comment_ids)
        processed_count = 0

        for comment in comments:
            # Create moderation action
            moderation_action = ModerationAction.objects.create(
                comment=comment,
                moderator=request.user,
                action_type=action_type,
                reason=reason,
                notes=f"Bulk action applied to {len(comment_ids)} comments",
            )

            # Apply the moderation action
            if action_type == "hide":
                comment.hide_comment()
            elif action_type == "delete":
                comment.soft_delete()
            elif action_type == "approve":
                comment.unhide_comment()

            # Resolve all flags for this comment
            unresolved_flags = CommentFlag.objects.filter(comment=comment, is_resolved=False)
            for flag in unresolved_flags:
                flag.resolve(request.user, f"Resolved by bulk moderation action: {action_type}")
                moderation_action.resolved_flags.add(flag)

            processed_count += 1

        return render(
            request,
            "messaging/partials/bulk_moderate_success.html",
            {
                "processed_count": processed_count,
                "action_type": action_type,
                "message": f"Successfully applied '{action_type}' to {processed_count} comment(s)",
            },
        )

    except Exception as e:
        logger.error(f"Error bulk moderating comments: {e}")
        return render(request, "messaging/partials/bulk_moderate_error.html", {"error": str(e)})


@login_required
@require_http_methods(["POST"])
def report_comment_htmx(request, comment_id):
    """
    HTMX endpoint for reporting a comment (alias for flag_comment_htmx).
    """
    # This is essentially the same as flagging a comment
    return flag_comment_htmx(request, comment_id)
