"""Version Serializers

from requests.exceptions import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ConnectionError, TimeoutError
from datetime import date
from datetime import datetime
from django.core.exceptions import ValidationError
from django.db import models
from rest_framework import serializers
from typing import Any
from typing import List
import json
import logging
import logging
logger = logging.getLogger(__name__)
import re
import time
import uuid

Handle model state snapshots for the versioning system with comprehensive
support for Django field types, relationships, and proper error handling.
"""

import json
import logging
import uuid
from decimal import Decimal
from typing import TYPE_CHECKING, Any

from django.contrib.gis.db import models as gis_models
from django.core.exceptions import ObjectDoesNotExist
from django.core.serializers.json import DjangoJSONEncoder
from django.db import models
from django.utils.dateparse import parse_date, parse_datetime, parse_time

if TYPE_CHECKING:
    from .models import UniversalVersion

logger = logging.getLogger(__name__)


class VersionSerializer:
    """Serialize Django model instances for version control.

    Similar to DRF serializers but focused on versioning needs with
    comprehensive field type support and relationship handling.
    """

    def __init__(
        self,
        instance: models.Model,
        fields: list[str] | None = None,
        exclude: list[str] | None = None,
        include_relationships: bool = True,
        depth: int = 1,
    ):
        """Initialize serializer.

        Args:
        ----
            instance: Django model instance to serialize
            fields: List of fields to include (if None, includes all)
            exclude: List of fields to exclude
            include_relationships: Whether to serialize related objects
            depth: Depth of relationship serialization

        """
        self.instance = instance
        self.fields = fields
        self.exclude = exclude or []
        self.include_relationships = include_relationships
        self.depth = depth

        # Default fields to exclude from versioning
        self.default_exclude = [
            "id",
            "pk",
            "created_at",
            "updated_at",
            "last_modified",
            "version_number",
            "last_version_created",
            "last_accessed",
            "access_count",
            "cache_hits",
            "password",
            "last_login",
        ]

        # Combine exclude lists
        self.exclude = list(set(self.exclude + self.default_exclude))

    @property
    def data(self) -> dict[str, Any]:
        """Serialize the instance to a dictionary"""
        return self.to_representation(self.instance)

    def to_representation(self, instance: models.Model) -> dict[str, Any]:
        """Convert model instance to dictionary representation.

        Args:
        ----
            instance: Django model instance

        Returns:
        -------
            Dictionary representation of the instance

        """
        # Get model fields
        model_fields = self._get_model_fields(instance)

        # Build serialized data
        data = {}

        for field in model_fields:
            field_name = field.name

            # Skip excluded fields
            if field_name in self.exclude:
                continue

            # Skip if fields specified and field not in list
            if self.fields and field_name not in self.fields:
                continue

            # Get field value
            try:
                value = getattr(instance, field_name, None)
                serialized_value = self._serialize_field_value(field, value)
                data[field_name] = serialized_value
            except AttributeError:
                # Field doesn't exist on instance
                logger.debug(f"Field {field_name} not found on {instance}")
                continue
            except (AttributeError, KeyError, ValueError, TypeError) as e:
                # Handle serialization errors gracefully
                logger.warning(f"Error serializing field {field_name}: {e}")
                data[field_name] = f"<Error serializing: {e!s}>"

        # Add metadata
        data["_meta"] = {
            "model": f"{instance._meta.app_label}.{instance._meta.model_name}",
            "pk": str(instance.pk) if instance.pk else None,
            "serialized_at": None,  # Set by the calling code
            "version": "1.0",
        }

        return data

    def _get_model_fields(self, instance: models.Model) -> list[models.Field]:
        """Get all fields for the model.

        Args:
        ----
            instance: Model instance

        Returns:
        -------
            List of model fields

        """
        fields = list(instance._meta.fields)

        # Include many-to-many fields if relationships are enabled
        if self.include_relationships and self.depth > 0:
            fields.extend(instance._meta.many_to_many)

        return fields

    def _serialize_field_value(self, field: models.Field, value: Any) -> Any:
        """Serialize individual field values based on field type.

        Args:
        ----
            field: Django model field
            value: Field value

        Returns:
        -------
            Serialized value

        """
        if value is None:
            return None

        try:
            # Handle datetime fields
            if isinstance(field, models.DateTimeField | models.DateField | models.TimeField):
                return value.isoformat() if value else None

            # Handle UUID fields
            if isinstance(field, models.UUIDField):
                return str(value)

            # Handle file fields
            if isinstance(field, models.FileField | models.ImageField):
                if value:
                    return {
                        "name": value.name,
                        "url": value.url if hasattr(value, "url") else None,
                        "size": value.size if hasattr(value, "size") else None,
                    }
                return None

            # Handle JSON fields
            if isinstance(field, models.JSONField):
                return value  # Already JSON serializable

            # Handle spatial fields (PostGIS)
            if hasattr(gis_models, "GeometryField") and isinstance(field, gis_models.GeometryField):
                return self._serialize_geometry_field(value)

            # Handle foreign key relationships
            if isinstance(field, models.ForeignKey):
                return self._serialize_foreign_key(field, value)

            # Handle many-to-many relationships
            if isinstance(field, models.ManyToManyField):
                return self._serialize_many_to_many(field, value)

            # Handle decimal fields
            if isinstance(
                field,
                models.DecimalField | models.CharField | models.TextField | models.SlugField,
            ):
                return str(value) if value is not None else None

            # Handle integer fields
            if isinstance(
                field,
                models.IntegerField
                | models.BigIntegerField
                | models.SmallIntegerField
                | models.PositiveIntegerField
                | models.PositiveSmallIntegerField
                | models.BigAutoField
                | models.AutoField,
            ):
                return int(value) if value is not None else None

            # Handle float fields
            if isinstance(field, models.FloatField):
                return float(value) if value is not None else None

            # Handle boolean fields
            if isinstance(field, models.BooleanField):
                return bool(value)

            # Handle binary fields
            if isinstance(field, models.BinaryField):
                return value.hex() if value else None

            # Handle choice fields
            if hasattr(field, "choices") and field.choices:
                return {
                    "value": value,
                    "display": getattr(self.instance, f"get_{field.name}_display", lambda: str(value))(),
                }

            # For other field types, try to convert to string
            return str(value) if value is not None else None

        except (AttributeError, KeyError, ValueError, TypeError) as e:
            logger.warning(f"Error serializing field {field.name}: {e}")
            return f"<Unserializable: {type(value).__name__}>"

    def _serialize_geometry_field(self, value: Any) -> dict[str, Any] | None:
        """Serialize PostGIS geometry fields.

        Args:
        ----
            value: Geometry value

        Returns:
        -------
            Serialized geometry data

        """
        if not value:
            return None

        try:
            return {
                "type": value.geom_type,
                "srid": value.srid,
                "wkt": value.wkt,
                "geojson": (json.loads(value.geojson) if hasattr(value, "geojson") else None),
            }
        except (json.JSONDecodeError, ValueError) as e:
            logger.warning(f"Error serializing geometry field: {e}")
            return {"error": f"Could not serialize geometry: {e!s}"}

    def _serialize_foreign_key(self, field: models.ForeignKey, value: Any) -> dict[str, Any] | None:
        """Serialize foreign key relationships.

        Args:
        ----
            field: ForeignKey field
            value: Related object

        Returns:
        -------
            Serialized relationship data

        """
        if value is None:
            return None

        try:
            serialized = {
                "pk": str(value.pk),
                "model": f"{value._meta.app_label}.{value._meta.model_name}",
                "str": str(value),
                "display_field": self._get_display_field_value(value),
            }

            # Include nested serialization if depth allows
            if self.depth > 1:
                nested_serializer = VersionSerializer(
                    value,
                    depth=self.depth - 1,
                    include_relationships=False,  # Prevent infinite recursion
                )
                serialized["data"] = nested_serializer.data

            return serialized

        except (AttributeError, KeyError, ValueError, TypeError) as e:
            logger.warning(f"Error serializing foreign key {field.name}: {e}")
            return {
                "pk": str(value.pk) if hasattr(value, "pk") else None,
                "error": f"Serialization error: {e!s}",
            }

    def _serialize_many_to_many(self, field: models.ManyToManyField, value: Any) -> list[dict[str, Any]]:
        """Serialize many-to-many relationships.

        Args:
        ----
            field: ManyToManyField
            value: Related manager

        Returns:
        -------
            List of serialized related objects

        """
        if not value:
            return []

        try:
            serialized = []
            for item in value.all():
                item_data = {
                    "pk": str(item.pk),
                    "model": f"{item._meta.app_label}.{item._meta.model_name}",
                    "str": str(item),
                    "display_field": self._get_display_field_value(item),
                }

                # Include nested serialization if depth allows
                if self.depth > 1:
                    nested_serializer = VersionSerializer(item, depth=self.depth - 1, include_relationships=False)
                    item_data["data"] = nested_serializer.data

                serialized.append(item_data)

            return serialized

        except (AttributeError, KeyError, ValueError, TypeError) as e:
            logger.warning(f"Error serializing many-to-many field {field.name}: {e}")
            return [{"error": f"Serialization error: {e!s}"}]

    def _get_display_field_value(self, instance: models.Model) -> Any:
        """Get a display-friendly field value for related objects.

        Args:
        ----
            instance: Related model instance

        Returns:
        -------
            Display value

        """
        # Try common display fields
        display_fields = [
            "name",
            "title",
            "username",
            "email",
            "label",
            "slug",
            "code",
            "identifier",
            "description",
        ]

        for field_name in display_fields:
            if hasattr(instance, field_name):
                value = getattr(instance, field_name, None)
                if value:
                    return str(value)

        # Fall back to string representation
        return str(instance)

    def to_json(self, indent: int | None = 2) -> str:
        """Convert serialized data to JSON string.

        Args:
        ----
            indent: JSON indentation

        Returns:
        -------
            JSON string

        """
        return json.dumps(self.data, cls=DjangoJSONEncoder, indent=indent)

    @classmethod
    def from_data(cls, data: dict[str, Any], model_class: type[models.Model], save: bool = False) -> models.Model:
        """Create model instance from serialized data.
        Used for version restoration.

        Args:
        ----
            data: Serialized data dictionary
            model_class: Django model class
            save: Whether to save the instance

        Returns:
        -------
            Model instance

        """
        # Remove metadata
        instance_data = {k: v for k, v in data.items() if not k.startswith("_")}

        instance = model_class()

        # Track fields that couldn't be restored
        failed_fields = []

        for field_name, value in instance_data.items():
            if hasattr(instance, field_name):
                try:
                    # Get field from model
                    field = model_class._meta.get_field(field_name)
                    deserialized_value = cls._deserialize_field_value(field, value)
                    setattr(instance, field_name, deserialized_value)
                except (ConnectionError, TimeoutError, HTTPError) as e:
                    logger.warning(f"Failed to restore field {field_name}: {e}")
                    failed_fields.append(field_name)

        # Save if requested
        if save:
            try:
                instance.save()
            except Exception as e:
                logger.error(f"Failed to save restored instance: {e}")
                raise

        # Log restoration summary
        if failed_fields:
            logger.info(f"Restored {model_class.__name__} with {len(failed_fields)} failed fields: {failed_fields}")

        return instance

    @classmethod
    def _deserialize_field_value(cls, field: models.Field, value: Any) -> Any:
        """Deserialize field values back to Python objects.

        Args:
        ----
            field: Django model field
            value: Serialized value

        Returns:
        -------
            Deserialized value

        """
        if value is None:
            return None

        try:
            # Handle datetime fields
            if isinstance(field, models.DateTimeField):
                return parse_datetime(value) if isinstance(value, str) else value

            if isinstance(field, models.DateField):
                return parse_date(value) if isinstance(value, str) else value

            if isinstance(field, models.TimeField):
                return parse_time(value) if isinstance(value, str) else value

            # Handle UUID fields
            if isinstance(field, models.UUIDField):
                return uuid.UUID(value) if isinstance(value, str) else value

            # Handle decimal fields
            if isinstance(field, models.DecimalField):
                return Decimal(str(value))

            # Handle file fields
            if isinstance(field, models.FileField | models.ImageField):
                if isinstance(value, dict):
                    return value.get("name")  # Just restore the file name
                return value

            # Handle geometry fields
            if hasattr(gis_models, "GeometryField") and isinstance(field, gis_models.GeometryField):
                return cls._deserialize_geometry_field(value)

            # Handle foreign key relationships
            if isinstance(field, models.ForeignKey):
                return cls._deserialize_foreign_key(field, value)

            # Handle many-to-many relationships (special handling needed)
            if isinstance(field, models.ManyToManyField):
                return value  # Return as-is, handle during restoration

            # Handle choice fields
            if hasattr(field, "choices") and field.choices and isinstance(value, dict):
                return value.get("value", value)

            # Handle binary fields
            if isinstance(field, models.BinaryField):
                return bytes.fromhex(value) if isinstance(value, str) else value

            # For other field types, return as-is
            return value

        except (AttributeError, KeyError, ValueError, TypeError) as e:
            logger.warning(f"Error deserializing field {field.name}: {e}")
            return None

    @classmethod
    def _deserialize_geometry_field(cls, value: Any) -> Any:
        """Deserialize PostGIS geometry fields.

        Args:
        ----
            value: Serialized geometry data

        Returns:
        -------
            Geometry object or None

        """
        if not value or not isinstance(value, dict):
            return None

        try:
            from django.contrib.gis.geos import GEOSGeometry

            if "wkt" in value:
                return GEOSGeometry(value["wkt"], srid=value.get("srid"))
            if "geojson" in value:
                return GEOSGeometry(json.dumps(value["geojson"]))

        except (ImportError, ModuleNotFoundError) as e:
            logger.warning(f"Error deserializing geometry: {e}")

        return None

    @classmethod
    def _deserialize_foreign_key(cls, field: models.ForeignKey, value: Any) -> Any:
        """Deserialize foreign key relationships.

        Args:
        ----
            field: ForeignKey field
            value: Serialized relationship data

        Returns:
        -------
            Related object or None

        """
        if not value:
            return None

        try:
            if isinstance(value, dict) and "pk" in value:
                related_model = field.related_model
                return related_model.objects.get(pk=value["pk"])
            if not isinstance(value, dict):
                # Direct PK value
                related_model = field.related_model
                return related_model.objects.get(pk=value)
        except ObjectDoesNotExist:
            logger.warning(f"Related object not found for field {field.name}")
        except (AttributeError, KeyError, ValueError, TypeError) as e:
            logger.warning(f"Error deserializing foreign key {field.name}: {e}")

        return None


class VersionDiffSerializer:
    """Serialize differences between two versions.

    Provides comprehensive diff analysis with change categorization
    and human-readable summaries.
    """

    def __init__(self, from_version: "UniversalVersion", to_version: "UniversalVersion"):
        """Initialize diff serializer.

        Args:
        ----
            from_version: Starting version
            to_version: Ending version

        """
        self.from_version = from_version
        self.to_version = to_version

    def get_diff_data(self) -> dict[str, Any]:
        """Generate comprehensive diff data between versions.

        Returns
        -------
            Dictionary containing detailed change information

        """
        from_data = self.from_version.serialized_data or {}
        to_data = self.to_version.serialized_data or {}

        # Remove metadata from comparison
        from_data = {k: v for k, v in from_data.items() if not k.startswith("_")}
        to_data = {k: v for k, v in to_data.items() if not k.startswith("_")}

        diff_data = {
            "from_version": self.from_version.version_number,
            "to_version": self.to_version.version_number,
            "from_created_at": self.from_version.created_at.isoformat(),
            "to_created_at": self.to_version.created_at.isoformat(),
            "changes": {},
            "added_fields": [],
            "removed_fields": [],
            "modified_fields": [],
            "unchanged_fields": [],
            "statistics": {},
        }

        # Find all fields that exist in either version
        all_fields = set(from_data.keys()) | set(to_data.keys())

        for field in all_fields:
            from_value = from_data.get(field)
            to_value = to_data.get(field)

            if field not in from_data:
                # Field was added
                diff_data["added_fields"].append(field)
                diff_data["changes"][field] = {
                    "action": "added",
                    "old_value": None,
                    "new_value": to_value,
                    "change_type": self._get_change_type(None, to_value),
                }

            elif field not in to_data:
                # Field was removed
                diff_data["removed_fields"].append(field)
                diff_data["changes"][field] = {
                    "action": "removed",
                    "old_value": from_value,
                    "new_value": None,
                    "change_type": self._get_change_type(from_value, None),
                }

            elif from_value != to_value:
                # Field was modified
                diff_data["modified_fields"].append(field)
                diff_data["changes"][field] = {
                    "action": "modified",
                    "old_value": from_value,
                    "new_value": to_value,
                    "change_type": self._get_change_type(from_value, to_value),
                }

            else:
                # Field unchanged
                diff_data["unchanged_fields"].append(field)

        # Generate statistics
        diff_data["statistics"] = {
            "total_fields": len(all_fields),
            "added_count": len(diff_data["added_fields"]),
            "removed_count": len(diff_data["removed_fields"]),
            "modified_count": len(diff_data["modified_fields"]),
            "unchanged_count": len(diff_data["unchanged_fields"]),
            "change_percentage": (
                (
                    (
                        len(diff_data["added_fields"])
                        + len(diff_data["removed_fields"])
                        + len(diff_data["modified_fields"])
                    )
                    / len(all_fields)
                    * 100
                )
                if all_fields
                else 0
            ),
        }

        return diff_data

    def _get_change_type(self, old_value: Any, new_value: Any) -> str:
        """Determine the type of change between two values.

        Args:
        ----
            old_value: Original value
            new_value: New value

        Returns:
        -------
            Change type description

        """
        if old_value is None and new_value is not None:
            return "addition"
        if old_value is not None and new_value is None:
            return "removal"
        if type(old_value) != type(new_value):
            return "type_change"
        if isinstance(old_value, str | int | float | bool):
            return "value_change"
        if isinstance(old_value, list | dict):
            return "structure_change"
        return "complex_change"

    def get_summary(self) -> str:
        """Generate human-readable summary of changes.

        Returns
        -------
            Summary string

        """
        diff_data = self.get_diff_data()
        stats = diff_data["statistics"]

        changes = []

        if stats["added_count"]:
            changes.append(f"Added {stats['added_count']} field{'s' if stats['added_count'] > 1 else ''}")

        if stats["removed_count"]:
            changes.append(f"Removed {stats['removed_count']} field{'s' if stats['removed_count'] > 1 else ''}")

        if stats["modified_count"]:
            changes.append(f"Modified {stats['modified_count']} field{'s' if stats['modified_count'] > 1 else ''}")

        if not changes:
            return "No changes detected"

        summary = "; ".join(changes)
        summary += f" ({stats['change_percentage']:.1f}% of fields changed)"

        return summary

    def get_field_diff(self, field_name: str) -> dict[str, Any] | None:
        """Get detailed diff for a specific field.

        Args:
        ----
            field_name: Name of the field

        Returns:
        -------
            Field diff data or None if field not changed

        """
        diff_data = self.get_diff_data()
        return diff_data["changes"].get(field_name)

    def to_json(self, indent: int | None = 2) -> str:
        """Convert diff data to JSON string.

        Args:
        ----
            indent: JSON indentation

        Returns:
        -------
            JSON string

        """
        return json.dumps(self.get_diff_data(), cls=DjangoJSONEncoder, indent=indent)


class BulkVersionSerializer:
    """Serialize multiple model instances for bulk versioning operations.
    Optimized for performance with large datasets.
    """

    def __init__(
        self,
        instances: list[models.Model],
        fields: list[str] | None = None,
        exclude: list[str] | None = None,
        chunk_size: int = 100,
    ):
        """Initialize bulk serializer.

        Args:
        ----
            instances: List of model instances
            fields: Fields to include
            exclude: Fields to exclude
            chunk_size: Number of instances to process in each chunk

        """
        self.instances = instances
        self.fields = fields
        self.exclude = exclude or []
        self.chunk_size = chunk_size

    def serialize_all(self) -> list[dict[str, Any]]:
        """Serialize all instances.

        Returns
        -------
            List of serialized data dictionaries

        """
        serialized_data = []

        # Process in chunks for memory efficiency
        for i in range(0, len(self.instances), self.chunk_size):
            chunk = self.instances[i : i + self.chunk_size]

            for instance in chunk:
                try:
                    serializer = VersionSerializer(
                        instance,
                        fields=self.fields,
                        exclude=self.exclude,
                        include_relationships=False,  # Optimize for bulk operations
                        depth=1,
                    )
                    serialized_data.append(serializer.data)
                except (ValidationError, ValueError) as e:
                    logger.error(f"Failed to serialize {instance}: {e}")
                    # Include error information
                    serialized_data.append(
                        {
                            "_meta": {
                                "model": f"{instance._meta.app_label}.{instance._meta.model_name}",
                                "pk": str(instance.pk) if instance.pk else None,
                                "error": str(e),
                            },
                        },
                    )

        return serialized_data

    def to_json(self) -> str:
        """Convert all serialized data to JSON.

        Returns
        -------
            JSON string

        """
        return json.dumps(self.serialize_all(), cls=DjangoJSONEncoder, indent=2)
