"""Django app configuration for versioning system."""

import logging

from django.apps import AppConfig
from django.conf import settings
from django.db import DatabaseError, IntegrityError, OperationalError
from django.utils.translation import gettext_lazy as _

logger = logging.getLogger(__name__)


class VersioningConfig(AppConfig):
    """Configuration for the versioning app.

    Provides universal version control functionality with git-like features:
    - Automatic versioning for any Django model
    - Branch and merge capabilities
    - Compressed storage and retention policies
    - Comprehensive audit trails
    - Performance optimization for large datasets
    """

    default_auto_field = "django.db.models.BigAutoField"
    name = "apps.versioning"
    verbose_name = _("Universal Version Control")

    def ready(self):
        """Initialize versioning signals and hooks"""
        try:
            # Import signals to register them
            from . import signals

            # Initialize versioning system settings
            self._initialize_versioning_settings()

            # Register model hooks for auto-versioning
            self._register_model_hooks()

        except ImportError as e:
            # Log import errors but don't fail app startup
            import logging

            logger = logging.getLogger(__name__)
            logger.warning(f"Failed to initialize versioning signals: {e}")

    def _initialize_versioning_settings(self):
        """Initialize default versioning settings"""

        # Set default versioning settings if not configured
        if not hasattr(settings, "VERSIONING_SETTINGS"):
            settings.VERSIONING_SETTINGS = {
                "AUTO_VERSION": True,
                "COMPRESS_DATA": True,
                "DEFAULT_RETENTION_DAYS": 365,
                "MAX_VERSIONS_PER_OBJECT": 100,
                "SIGNIFICANT_CHANGE_THRESHOLD": 0.1,
                "ENABLE_BRANCH_MERGE": True,
                "ENABLE_CONFLICT_DETECTION": True,
                "ASYNC_PROCESSING": False,
            }

    def _register_model_hooks(self):
        """Register post-save hooks for auto-versioning"""
        try:
            from django.db.models.signals import post_delete, post_save

            from .signals import create_version_on_save, log_deletion

            # Connect signals for all models that support versioning
            post_save.connect(create_version_on_save, dispatch_uid="versioning_auto_version")
            post_delete.connect(log_deletion, dispatch_uid="versioning_log_deletion")

        except (DatabaseError, IntegrityError, OperationalError) as e:
            import logging

            logger = logging.getLogger(__name__)
            logger.error(f"Failed to register versioning hooks: {e}")
