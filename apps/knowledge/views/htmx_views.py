"""HTMX views for knowledge management.

This module provides HTMX endpoint views for dynamic content updates,
search functionality, and interactive components.
"""

from django.contrib.auth.decorators import login_required
from django.core.paginator import Paginator
from django.db.models import Q
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, render
from django.utils import timezone
from django.views.decorators.http import require_http_methods

from apps.knowledge.models import Article, ArticleCategory, ArticleComment, ArticleVote

# ============================================================================
# Search HTMX Views
# ============================================================================


@login_required
@require_http_methods(["GET"])
def search_htmx(request):
    """HTMX search interface."""
    query = request.GET.get("q", "").strip()
    organization = request.user.organization

    if not query:
        return render(request, "knowledge/htmx/search_empty.html")

    # Search articles
    articles = (
        Article.objects.filter(organization=organization, status="published", is_published=True)
        .filter(Q(title__icontains=query) | Q(content__icontains=query) | Q(summary__icontains=query))
        .select_related("author", "category")
        .order_by("-updated_at")[:10]
    )

    context = {"articles": articles, "query": query, "total_results": articles.count()}
    return render(request, "knowledge/htmx/search_results.html", context)


@login_required
@require_http_methods(["GET"])
def search_results_htmx(request):
    """HTMX search results with pagination."""
    query = request.GET.get("q", "").strip()
    page = request.GET.get("page", 1)
    organization = request.user.organization

    if not query:
        return HttpResponse("")

    articles = (
        Article.objects.filter(organization=organization, status="published", is_published=True)
        .filter(Q(title__icontains=query) | Q(content__icontains=query) | Q(summary__icontains=query))
        .select_related("author", "category")
        .order_by("-updated_at")
    )

    paginator = Paginator(articles, 10)
    page_obj = paginator.get_page(page)

    context = {"page_obj": page_obj, "query": query}
    return render(request, "knowledge/htmx/search_results_page.html", context)


@login_required
@require_http_methods(["GET"])
def search_suggestions_htmx(request):
    """HTMX search suggestions."""
    query = request.GET.get("q", "").strip()
    organization = request.user.organization

    if len(query) < 2:
        return HttpResponse("")

    # Get title suggestions
    suggestions = (
        Article.objects.filter(
            organization=organization,
            status="published",
            is_published=True,
            title__icontains=query,
        )
        .values_list("title", flat=True)
        .distinct()[:5]
    )

    context = {"suggestions": suggestions, "query": query}
    return render(request, "knowledge/htmx/search_suggestions.html", context)


@login_required
@require_http_methods(["GET"])
def search_filters_htmx(request):
    """HTMX search filters."""
    category_id = request.GET.get("category")
    organization = request.user.organization

    categories = ArticleCategory.objects.filter(organization=organization).order_by("name")

    context = {"categories": categories, "selected_category": category_id}
    return render(request, "knowledge/htmx/search_filters.html", context)


# ============================================================================
# Article HTMX Views
# ============================================================================


@login_required
@require_http_methods(["GET"])
def article_list_htmx(request):
    """HTMX article list."""
    category_id = request.GET.get("category")
    page = request.GET.get("page", 1)
    organization = request.user.organization

    articles = (
        Article.objects.filter(organization=organization, status="published", is_published=True)
        .select_related("author", "category")
        .order_by("-updated_at")
    )

    if category_id:
        articles = articles.filter(category_id=category_id)

    paginator = Paginator(articles, 10)
    page_obj = paginator.get_page(page)

    context = {"page_obj": page_obj, "selected_category": category_id}
    return render(request, "knowledge/htmx/article_list.html", context)


@login_required
@require_http_methods(["GET"])
def article_filter_htmx(request):
    """HTMX article filtering."""
    category_id = request.GET.get("category")
    status = request.GET.get("status", "published")
    organization = request.user.organization

    articles = (
        Article.objects.filter(organization=organization, status=status)
        .select_related("author", "category")
        .order_by("-updated_at")
    )

    if category_id:
        articles = articles.filter(category_id=category_id)

    context = {
        "articles": articles[:10],
        "selected_category": category_id,
        "selected_status": status,
    }
    return render(request, "knowledge/htmx/article_filter.html", context)


@login_required
@require_http_methods(["GET"])
def recent_articles_htmx(request):
    """HTMX recent articles."""
    organization = request.user.organization

    articles = (
        Article.objects.filter(organization=organization, status="published", is_published=True)
        .select_related("author", "category")
        .order_by("-updated_at")[:5]
    )

    context = {"articles": articles}
    return render(request, "knowledge/htmx/recent_articles.html", context)


@login_required
@require_http_methods(["POST"])
def article_vote_htmx(request, pk):
    """HTMX article voting."""
    article = get_object_or_404(Article, pk=pk, organization=request.user.organization)

    vote_type = request.POST.get("vote_type")  # 'up' or 'down'

    if vote_type not in ["up", "down"]:
        return HttpResponse("Invalid vote type", status=400)

    # Get or create vote
    vote, created = ArticleVote.objects.get_or_create(
        article=article, user=request.user, defaults={"vote_type": vote_type}
    )

    if not created:
        if vote.vote_type == vote_type:
            # Remove vote if clicking same button
            vote.delete()
        else:
            # Change vote type
            vote.vote_type = vote_type
            vote.save()

    # Calculate vote counts
    up_votes = ArticleVote.objects.filter(article=article, vote_type="up").count()
    down_votes = ArticleVote.objects.filter(article=article, vote_type="down").count()

    context = {
        "article": article,
        "up_votes": up_votes,
        "down_votes": down_votes,
        "user_vote": ArticleVote.objects.filter(article=article, user=request.user).first(),
    }
    return render(request, "knowledge/htmx/article_votes.html", context)


@login_required
@require_http_methods(["POST"])
def article_comment_htmx(request, pk):
    """HTMX article commenting."""
    article = get_object_or_404(Article, pk=pk, organization=request.user.organization)

    content = request.POST.get("content", "").strip()

    if not content:
        return HttpResponse("Comment cannot be empty", status=400)

    # Create comment
    comment = ArticleComment.objects.create(article=article, user=request.user, content=content)

    # Get updated comments
    comments = ArticleComment.objects.filter(article=article).select_related("user").order_by("-created_at")

    context = {"article": article, "comments": comments, "new_comment": comment}
    return render(request, "knowledge/htmx/article_comments.html", context)


@login_required
@require_http_methods(["GET"])
def article_preview_htmx(request, pk):
    """HTMX article preview."""
    article = get_object_or_404(Article, pk=pk, organization=request.user.organization)

    context = {"article": article}
    return render(request, "knowledge/htmx/article_preview.html", context)


@login_required
@require_http_methods(["GET"])
def article_create_form_htmx(request):
    """HTMX article creation form."""
    organization = request.user.organization

    categories = ArticleCategory.objects.filter(organization=organization).order_by("name")

    context = {"categories": categories, "organization": organization}
    return render(request, "knowledge/htmx/article_create_form.html", context)


@login_required
@require_http_methods(["GET"])
def article_edit_form_htmx(request, pk):
    """HTMX article edit form."""
    article = get_object_or_404(Article, pk=pk, organization=request.user.organization)

    categories = ArticleCategory.objects.filter(organization=request.user.organization).order_by("name")

    context = {"article": article, "categories": categories}
    return render(request, "knowledge/htmx/article_edit_form.html", context)


# ============================================================================
# Category HTMX Views
# ============================================================================


@login_required
@require_http_methods(["GET"])
def category_list_htmx(request):
    """HTMX category list."""
    organization = request.user.organization

    categories = ArticleCategory.objects.filter(organization=organization).order_by("name")

    context = {"categories": categories}
    return render(request, "knowledge/htmx/category_list.html", context)


@login_required
@require_http_methods(["GET"])
def category_tree_htmx(request):
    """HTMX category tree."""
    organization = request.user.organization

    categories = ArticleCategory.objects.filter(organization=organization).order_by("name")

    context = {"categories": categories}
    return render(request, "knowledge/htmx/category_tree.html", context)


@login_required
@require_http_methods(["GET"])
def category_filter_htmx(request):
    """HTMX category filtering."""
    organization = request.user.organization
    search = request.GET.get("search", "").strip()

    categories = ArticleCategory.objects.filter(organization=organization)

    if search:
        categories = categories.filter(Q(name__icontains=search) | Q(description__icontains=search))

    categories = categories.order_by("name")

    context = {"categories": categories, "search": search}
    return render(request, "knowledge/htmx/category_filter.html", context)


# ============================================================================
# Dashboard HTMX Views
# ============================================================================


@login_required
@require_http_methods(["GET"])
def dashboard_stats_htmx(request):
    """HTMX dashboard statistics."""
    organization = request.user.organization

    stats = {
        "total_articles": Article.objects.filter(organization=organization).count(),
        "published_articles": Article.objects.filter(organization=organization, status="published").count(),
        "total_categories": ArticleCategory.objects.filter(organization=organization).count(),
        "recent_articles": Article.objects.filter(
            organization=organization,
            created_at__gte=timezone.now() - timezone.timedelta(days=7),
        ).count(),
    }

    context = {"stats": stats}
    return render(request, "knowledge/htmx/dashboard_stats.html", context)


@login_required
@require_http_methods(["GET"])
def dashboard_recent_htmx(request):
    """HTMX dashboard recent activity."""
    organization = request.user.organization

    recent_articles = (
        Article.objects.filter(organization=organization)
        .select_related("author", "category")
        .order_by("-updated_at")[:5]
    )

    context = {"recent_articles": recent_articles}
    return render(request, "knowledge/htmx/dashboard_recent.html", context)


@login_required
@require_http_methods(["GET"])
def dashboard_popular_htmx(request):
    """HTMX dashboard popular articles."""
    organization = request.user.organization

    popular_articles = (
        Article.objects.filter(organization=organization, status="published", is_published=True)
        .select_related("author", "category")
        .order_by("-view_count")[:5]
    )

    context = {"popular_articles": popular_articles}
    return render(request, "knowledge/htmx/dashboard_popular.html", context)


@login_required
@require_http_methods(["GET"])
def analytics_charts_htmx(request):
    """HTMX analytics charts."""

    # Mock chart data - in real implementation, this would come from analytics
    chart_data = {
        "article_views": [10, 20, 30, 25, 40, 35, 50],
        "article_creation": [2, 3, 1, 4, 2, 5, 3],
        "user_engagement": [15, 25, 20, 30, 28, 35, 40],
    }

    context = {"chart_data": chart_data}
    return render(request, "knowledge/htmx/analytics_charts.html", context)


@login_required
@require_http_methods(["GET"])
def analytics_metrics_htmx(request):
    """HTMX analytics metrics."""

    metrics = {
        "total_views": 1250,
        "unique_visitors": 340,
        "avg_session_duration": "5m 30s",
        "bounce_rate": "35%",
    }

    context = {"metrics": metrics}
    return render(request, "knowledge/htmx/analytics_metrics.html", context)


@login_required
@require_http_methods(["GET"])
def ai_status_htmx(request):
    """HTMX AI status indicator."""
    # Mock AI status - in real implementation, this would check actual AI services
    ai_status = {
        "status": "operational",
        "last_check": timezone.now(),
        "response_time": "150ms",
        "accuracy": "94%",
    }

    context = {"ai_status": ai_status}
    return render(request, "knowledge/htmx/ai_status.html", context)


@login_required
@require_http_methods(["GET"])
def ai_discovery_htmx(request):
    """HTMX AI service discovery."""
    # Mock AI services - in real implementation, this would discover actual services
    services = [
        {"name": "OpenAI GPT", "status": "active", "response_time": "120ms"},
        {"name": "Claude", "status": "active", "response_time": "150ms"},
        {"name": "Local LLM", "status": "inactive", "response_time": "N/A"},
    ]

    context = {"services": services}
    return render(request, "knowledge/htmx/ai_discovery.html", context)


@login_required
@require_http_methods(["GET"])
def ai_documentation_htmx(request):
    """HTMX AI documentation helper."""
    context = {
        "suggestions": [
            "How to integrate AI services",
            "Best practices for AI content generation",
            "Troubleshooting AI responses",
        ]
    }
    return render(request, "knowledge/htmx/ai_documentation.html", context)


@login_required
@require_http_methods(["GET"])
def team_list_htmx(request):
    """HTMX team list."""
    # Mock team data - in real implementation, this would come from team models
    teams = [
        {"id": 1, "name": "Engineering", "members": 5, "active": True},
        {"id": 2, "name": "Design", "members": 3, "active": True},
        {"id": 3, "name": "Marketing", "members": 4, "active": False},
    ]

    context = {"teams": teams}
    return render(request, "knowledge/htmx/team_list.html", context)


@login_required
@require_http_methods(["GET"])
def team_members_htmx(request, pk):
    """HTMX team members."""
    # Mock team members - in real implementation, this would come from team models
    members = [
        {"name": "John Doe", "role": "Lead", "active": True},
        {"name": "Jane Smith", "role": "Member", "active": True},
        {"name": "Bob Johnson", "role": "Member", "active": False},
    ]

    context = {"members": members, "team_id": pk}
    return render(request, "knowledge/htmx/team_members.html", context)


@login_required
@require_http_methods(["GET"])
def active_collaborations_htmx(request):
    """HTMX active collaborations."""
    # Mock collaboration data
    collaborations = [
        {"id": 1, "title": "API Documentation", "participants": 3, "status": "active"},
        {"id": 2, "title": "User Guide", "participants": 2, "status": "paused"},
    ]

    context = {"collaborations": collaborations}
    return render(request, "knowledge/htmx/active_collaborations.html", context)


@login_required
@require_http_methods(["GET"])
def entity_autocomplete_htmx(request):
    """HTMX entity autocomplete for knowledge graph."""
    query = request.GET.get("q", "").strip()

    if len(query) < 2:
        return HttpResponse("")

    # Mock entity suggestions
    entities = [
        {"id": 1, "name": f"Entity {query} 1", "type": "concept"},
        {"id": 2, "name": f"Entity {query} 2", "type": "person"},
        {"id": 3, "name": f"Entity {query} 3", "type": "organization"},
    ]

    context = {"entities": entities, "query": query}
    return render(request, "knowledge/htmx/entity_autocomplete.html", context)


@login_required
@require_http_methods(["GET"])
def entity_chain_htmx(request):
    """HTMX entity chain for knowledge graph."""
    entity_id = request.GET.get("entity_id")

    # Mock entity chain
    chain = [
        {"id": 1, "name": "Root Entity", "type": "concept"},
        {"id": 2, "name": "Child Entity", "type": "subconcept"},
        {"id": 3, "name": "Leaf Entity", "type": "instance"},
    ]

    context = {"chain": chain, "entity_id": entity_id}
    return render(request, "knowledge/htmx/entity_chain.html", context)


@login_required
@require_http_methods(["GET"])
def knowledge_graph_htmx(request):
    """HTMX knowledge graph visualization."""
    # Mock graph data
    graph_data = {
        "nodes": [
            {"id": 1, "name": "Article A", "type": "article"},
            {"id": 2, "name": "Concept B", "type": "concept"},
            {"id": 3, "name": "Person C", "type": "person"},
        ],
        "edges": [
            {"source": 1, "target": 2, "type": "mentions"},
            {"source": 2, "target": 3, "type": "created_by"},
        ],
    }

    context = {"graph_data": graph_data}
    return render(request, "knowledge/htmx/knowledge_graph.html", context)


@login_required
@require_http_methods(["GET"])
def knowledge_graph_data_htmx(request):
    """HTMX knowledge graph data API."""
    # Mock graph data for visualization
    data = {
        "nodes": [
            {"id": "article1", "label": "Getting Started", "group": "article"},
            {"id": "concept1", "label": "Authentication", "group": "concept"},
            {"id": "person1", "label": "John Doe", "group": "person"},
        ],
        "edges": [
            {"from": "article1", "to": "concept1", "label": "discusses"},
            {"from": "concept1", "to": "person1", "label": "authored_by"},
        ],
    }

    return JsonResponse(data)


@login_required
@require_http_methods(["POST"])
def comment_moderate_htmx(request, pk):
    """HTMX comment moderation."""
    # Mock comment moderation
    action = request.POST.get("action")  # 'approve', 'reject', 'flag'

    if action not in ["approve", "reject", "flag"]:
        return HttpResponse("Invalid action", status=400)

    # Mock moderation result
    result = {
        "comment_id": pk,
        "action": action,
        "status": "success",
        "message": f"Comment {action}d successfully",
    }

    context = {"result": result}
    return render(request, "knowledge/htmx/comment_moderate.html", context)


@login_required
@require_http_methods(["POST"])
def comment_reply_htmx(request, pk):
    """HTMX comment reply."""
    reply_content = request.POST.get("content", "").strip()

    if not reply_content:
        return HttpResponse("Reply cannot be empty", status=400)

    # Mock reply creation
    reply = {
        "id": "new_reply_id",
        "content": reply_content,
        "author": request.user.get_full_name(),
        "created_at": timezone.now(),
        "parent_id": pk,
    }

    context = {"reply": reply}
    return render(request, "knowledge/htmx/comment_reply.html", context)


@login_required
@require_http_methods(["POST"])
def form_validate_htmx(request):
    """HTMX form validation."""
    field_name = request.POST.get("field_name")
    field_value = request.POST.get("field_value", "").strip()

    # Mock validation logic
    errors = []

    if field_name == "title" and len(field_value) < 3:
        errors.append("Title must be at least 3 characters long")

    if field_name == "email" and "@" not in field_value:
        errors.append("Please enter a valid email address")

    context = {
        "field_name": field_name,
        "field_value": field_value,
        "errors": errors,
        "is_valid": len(errors) == 0,
    }

    return render(request, "knowledge/htmx/form_validate.html", context)


@login_required
@require_http_methods(["GET"])
def tag_autocomplete_htmx(request):
    """HTMX tag autocomplete."""
    query = request.GET.get("q", "").strip()

    if len(query) < 2:
        return HttpResponse("")

    # Mock tag suggestions
    tags = [f"tag-{query}-1", f"tag-{query}-2", f"tag-{query}-3"]

    context = {"tags": tags, "query": query}
    return render(request, "knowledge/htmx/tag_autocomplete.html", context)


@login_required
@require_http_methods(["GET"])
def notification_list_htmx(request):
    """HTMX notification list."""
    # Mock notifications
    notifications = [
        {
            "id": 1,
            "message": "New comment on your article",
            "read": False,
            "timestamp": timezone.now(),
        },
        {
            "id": 2,
            "message": "Article approved",
            "read": True,
            "timestamp": timezone.now(),
        },
        {
            "id": 3,
            "message": "New team member added",
            "read": False,
            "timestamp": timezone.now(),
        },
    ]

    context = {"notifications": notifications}
    return render(request, "knowledge/htmx/notification_list.html", context)


@login_required
@require_http_methods(["POST"])
def validate_article_form_htmx(request):
    """HTMX article form validation."""
    title = request.POST.get("title", "").strip()
    content = request.POST.get("content", "").strip()

    errors = {}

    if not title:
        errors["title"] = "Title is required"
    elif len(title) < 3:
        errors["title"] = "Title must be at least 3 characters long"

    if not content:
        errors["content"] = "Content is required"
    elif len(content) < 10:
        errors["content"] = "Content must be at least 10 characters long"

    context = {
        "errors": errors,
        "is_valid": len(errors) == 0,
        "title": title,
        "content": content,
    }

    return render(request, "knowledge/htmx/validate_article.html", context)


@login_required
@require_http_methods(["POST"])
def validate_category_form_htmx(request):
    """HTMX category form validation."""
    name = request.POST.get("name", "").strip()
    description = request.POST.get("description", "").strip()

    errors = {}

    if not name:
        errors["name"] = "Category name is required"
    elif len(name) < 2:
        errors["name"] = "Category name must be at least 2 characters long"

    if description and len(description) < 10:
        errors["description"] = "Description must be at least 10 characters long"

    context = {
        "errors": errors,
        "is_valid": len(errors) == 0,
        "name": name,
        "description": description,
    }

    return render(request, "knowledge/htmx/validate_category.html", context)


@login_required
@require_http_methods(["GET"])
def category_select_htmx(request):
    """HTMX category selection form."""
    organization = request.user.organization
    selected_category = request.GET.get("selected")

    categories = ArticleCategory.objects.filter(organization=organization).order_by("name")

    context = {"categories": categories, "selected_category": selected_category}

    return render(request, "knowledge/htmx/category_select.html", context)


@login_required
@require_http_methods(["GET"])
def tag_input_htmx(request):
    """HTMX tag input form."""
    current_tags = request.GET.get("tags", "").split(",")
    current_tags = [tag.strip() for tag in current_tags if tag.strip()]

    context = {"current_tags": current_tags}

    return render(request, "knowledge/htmx/tag_input.html", context)
