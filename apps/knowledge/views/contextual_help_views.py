"""Contextual Help HTMX Views for CLEAR Platform

Provides HTMX endpoints for displaying contextual help content dynamically
within the CLEAR platform interface.
"""

import logging
from typing import Any, Dict

from django.contrib.auth.decorators import login_required
from django.db.models import Count
from django.http import HttpRequest, HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, render
from django.template import TemplateDoesNotExist
from django.utils.decorators import method_decorator
from django.utils.translation import gettext_lazy as _
from django.views.decorators.http import require_http_methods
from django.views.generic import TemplateView

from apps.common.mixins import HTMXResponseMixin, OrganizationAccessMixin
from apps.knowledge.models import HelpTopic, HelpTopicView
from apps.knowledge.services.contextual_help_service import get_contextual_help_service

logger = logging.getLogger(__name__)


class ContextualHelpMixin(OrganizationAccessMixin, HTMXResponseMixin):
    """Mixin for contextual help views with organization access and HTMX support."""

    def get_help_service(self):
        """Get contextual help service instance."""
        return get_contextual_help_service(organization=self.request.user.organization, user=self.request.user)


@method_decorator(login_required, name="dispatch")
class HelpTopicContentView(ContextualHelpMixin, TemplateView):
    """HTMX view for displaying help topic content."""

    template_name = "knowledge/help/partials/help_content.html"

    def get_context_data(self, **kwargs) -> Dict[str, Any]:
        context = super().get_context_data(**kwargs)

        topic_id = self.kwargs.get("topic_id")
        help_service = self.get_help_service()

        # Get help topic content
        content_data = help_service.get_help_topic_content(topic_id=topic_id, track_view=True)

        if not content_data:
            context["error"] = _("Help topic not found or not accessible")
            return context

        context.update(
            {
                "content_data": content_data,
                "topic_id": topic_id,
                "show_feedback": True,
                "feedback_endpoint": "knowledge:help_feedback",
            }
        )

        return context


@method_decorator(login_required, name="dispatch")
class ContextualHelpWidgetView(ContextualHelpMixin, TemplateView):
    """HTMX view for displaying contextual help widget."""

    template_name = "knowledge/help/partials/help_widget.html"

    def get_context_data(self, **kwargs) -> Dict[str, Any]:
        context = super().get_context_data(**kwargs)

        # Get context parameters from request
        page_context = self.request.GET.get("context", "")
        selector = self.request.GET.get("selector", "")
        view_name = self.request.GET.get("view_name", "")
        form_class = self.request.GET.get("form_class", "")
        field_name = self.request.GET.get("field_name", "")

        help_service = self.get_help_service()

        # Get matching help topics
        help_topics = help_service.get_help_for_context(
            context=page_context,
            selector=selector,
            view_name=view_name,
            form_class=form_class,
            field_name=field_name,
        )

        context.update(
            {
                "help_topics": help_topics,
                "context": page_context,
                "selector": selector,
                "view_name": view_name,
                "has_help": len(help_topics) > 0,
                "widget_id": f"help-widget-{hash(page_context + selector)}",
            }
        )

        return context


@method_decorator(login_required, name="dispatch")
class HelpSearchView(ContextualHelpMixin, TemplateView):
    """HTMX view for searching help topics."""

    template_name = "knowledge/help/partials/help_search_results.html"

    def get_context_data(self, **kwargs) -> Dict[str, Any]:
        context = super().get_context_data(**kwargs)

        query = self.request.GET.get("q", "").strip()
        limit = int(self.request.GET.get("limit", "10"))

        help_service = self.get_help_service()

        if query:
            search_results = help_service.search_help_topics(query=query, limit=limit)
        else:
            search_results = []

        context.update(
            {
                "query": query,
                "results": search_results,
                "result_count": len(search_results),
                "show_more": len(search_results) >= limit,
            }
        )

        return context


@require_http_methods(["POST"])
@login_required
def help_topic_feedback(request: HttpRequest) -> HttpResponse:
    """HTMX endpoint for submitting help topic feedback."""

    topic_id = request.POST.get("topic_id")
    was_helpful = request.POST.get("was_helpful")
    feedback_text = request.POST.get("feedback", "").strip()
    time_spent = request.POST.get("time_spent")

    if not topic_id:
        return render(
            request,
            "knowledge/help/partials/feedback_error.html",
            {"error": _("Missing topic ID")},
        )

    try:
        # Get the help topic
        help_topic = get_object_or_404(
            HelpTopic,
            id=topic_id,
            organization=request.user.organization,
            is_active=True,
        )

        # Find or create the most recent view record
        help_view = (
            HelpTopicView.objects.filter(help_topic=help_topic, user=request.user).order_by("-created_at").first()
        )

        if help_view:
            # Update existing view with feedback
            if was_helpful is not None:
                help_view.was_helpful = was_helpful.lower() == "true"
            if feedback_text:
                help_view.feedback = feedback_text
            if time_spent:
                try:
                    help_view.time_spent_seconds = int(time_spent)
                except ValueError:
                    pass
            help_view.save()
        else:
            # Create new view record with feedback
            HelpTopicView.objects.create(
                help_topic=help_topic,
                user=request.user,
                page_url=request.META.get("HTTP_REFERER", "/"),
                trigger_context="feedback",
                ip_address=request.META.get("REMOTE_ADDR", "127.0.0.1"),
                user_agent=request.META.get("HTTP_USER_AGENT", ""),
                was_helpful=was_helpful.lower() == "true" if was_helpful else None,
                feedback=feedback_text,
                time_spent_seconds=int(time_spent) if time_spent else None,
            )

        return render(
            request,
            "knowledge/help/partials/feedback_success.html",
            {"message": _("Thank you for your feedback!")},
        )

    except Exception as e:
        logger.error(f"Error submitting help feedback: {e}")
        return render(
            request,
            "knowledge/help/partials/feedback_error.html",
            {"error": _("An error occurred while submitting feedback")},
        )


@require_http_methods(["GET"])
@login_required
def help_widget_for_element(request: HttpRequest) -> HttpResponse:
    """HTMX endpoint for getting help widget for specific UI element."""

    # Extract element information from request
    element_id = request.GET.get("element_id", "")
    element_class = request.GET.get("element_class", "")
    page_path = request.GET.get("page_path", "")
    form_name = request.GET.get("form_name", "")
    field_name = request.GET.get("field_name", "")

    help_service = get_contextual_help_service(organization=request.user.organization, user=request.user)

    # Build CSS selector from element info
    selector = ""
    if element_id:
        selector = f"#{element_id}"
    elif element_class:
        selector = f".{element_class}"

    # Get contextual help
    help_topics = help_service.get_help_for_context(
        context=page_path,
        selector=selector,
        form_class=form_name,
        field_name=field_name,
    )

    # Determine display type for first matching topic
    display_type = "tooltip"
    if help_topics:
        display_type = help_topics[0].get("display_type", "tooltip")

    context = {
        "help_topics": help_topics,
        "display_type": display_type,
        "element_id": element_id,
        "element_class": element_class,
        "has_help": len(help_topics) > 0,
        "field_name": field_name,
        "widget_id": f"help-{element_id or element_class or 'generic'}",
    }

    template_name = f"knowledge/help/partials/help_{display_type}.html"

    # Fallback to generic widget if specific template doesn't exist
    try:
        return render(request, template_name, context)
    except (TemplateDoesNotExist, AttributeError) as e:
        import logging

        logging.debug(f"Template {template_name} not found, using fallback: {e}")
        return render(request, "knowledge/help/partials/help_tooltip.html", context)


@require_http_methods(["GET"])
@login_required
def help_quick_access(request: HttpRequest) -> HttpResponse:
    """HTMX endpoint for quick access help panel."""

    get_contextual_help_service(organization=request.user.organization, user=request.user)

    # Get recently viewed help topics
    recent_views = (
        HelpTopicView.objects.filter(user=request.user, help_topic__organization=request.user.organization)
        .select_related("help_topic")
        .order_by("-created_at")[:5]
    )

    # Get popular help topics
    popular_topics = (
        HelpTopic.objects.filter(organization=request.user.organization, is_active=True)
        .annotate(view_count=Count("views"))
        .order_by("-view_count")[:10]
    )

    context = {
        "recent_topics": [view.help_topic for view in recent_views],
        "popular_topics": popular_topics,
        "search_enabled": True,
        "analytics_enabled": request.user.has_perm("knowledge.view_analytics"),
    }

    return render(request, "knowledge/help/partials/quick_access_panel.html", context)


@method_decorator(login_required, name="dispatch")
class HelpAdminDashboardView(ContextualHelpMixin, TemplateView):
    """HTMX view for help administration dashboard."""

    template_name = "knowledge/help/admin/dashboard.html"

    def dispatch(self, request, *args, **kwargs):
        # Check admin permissions
        if not (request.user.is_staff or request.user.has_perm("knowledge.manage_help")):
            return render(request, "knowledge/help/partials/access_denied.html")
        return super().dispatch(request, *args, **kwargs)

    def get_context_data(self, **kwargs) -> Dict[str, Any]:
        context = super().get_context_data(**kwargs)

        help_service = self.get_help_service()

        # Get analytics data
        analytics = help_service.get_help_analytics(days=30)

        # Get all help topics for management
        help_topics = (
            HelpTopic.objects.filter(organization=self.request.user.organization)
            .select_related("article")
            .order_by("priority", "title")
        )

        context.update(
            {
                "analytics": analytics,
                "help_topics": help_topics,
                "can_edit": True,
                "can_sync": True,
                "sync_endpoint": "knowledge:help_sync_mkdocs",
            }
        )

        return context


@require_http_methods(["POST"])
@login_required
def sync_mkdocs_content(request: HttpRequest) -> HttpResponse:
    """HTMX endpoint for syncing MkDocs content."""

    # Check permissions
    if not (request.user.is_staff or request.user.has_perm("knowledge.manage_help")):
        return render(request, "knowledge/help/partials/access_denied.html")

    help_service = get_contextual_help_service(organization=request.user.organization, user=request.user)

    rebuild = request.POST.get("rebuild", "false").lower() == "true"

    try:
        result = help_service.sync_mkdocs_content(rebuild=rebuild)

        context = {
            "success": True,
            "result": result,
            "message": f"Synced {result['synced_count']} help topics from {result['total_files']} files",
        }

        if result["errors"]:
            context["warnings"] = result["errors"]

    except Exception as e:
        logger.error(f"Error syncing MkDocs content: {e}")
        context = {
            "success": False,
            "error": str(e),
            "message": _("Failed to sync MkDocs content"),
        }

    return render(request, "knowledge/help/partials/sync_result.html", context)


# API endpoint for getting help data as JSON (for non-HTMX clients)
@require_http_methods(["GET"])
@login_required
def help_api_endpoint(request: HttpRequest) -> JsonResponse:
    """JSON API endpoint for help content (non-HTMX clients)."""

    topic_id = request.GET.get("topic_id")
    context = request.GET.get("context", "")
    selector = request.GET.get("selector", "")
    search_query = request.GET.get("q", "")

    help_service = get_contextual_help_service(organization=request.user.organization, user=request.user)

    try:
        if topic_id:
            # Get specific topic content
            content = help_service.get_help_topic_content(topic_id, track_view=True)
            return JsonResponse({"content": content})

        elif search_query:
            # Search help topics
            results = help_service.search_help_topics(search_query)
            return JsonResponse({"results": results})

        elif context or selector:
            # Get contextual help
            topics = help_service.get_help_for_context(context=context, selector=selector)
            return JsonResponse({"topics": topics})

        else:
            return JsonResponse({"error": "Missing required parameters"}, status=400)

    except Exception as e:
        logger.error(f"Error in help API endpoint: {e}")
        return JsonResponse({"error": "Internal server error"}, status=500)
