"""
AI-powered API discovery views for CLEAR knowledge management.

Provides intelligent API exploration, documentation generation, and
natural language querying capabilities integrated with the knowledge base.
"""

import json
import logging
import requests
import time
from datetime import datetime, timedelta
from typing import Any, List

from django.db import OperationalError
from django.db.utils import DatabaseError, IntegrityError

from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.http import JsonResponse
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.views.decorators.cache import cache_page
from django.views.decorators.http import require_http_methods
from django.views.generic import TemplateView, View

from apps.common.mixins import (
    HTMXResponseMixin,
    OrganizationAccessMixin,
    RoleRequiredMixin,
)
from apps.knowledge.services.knowledge_analytics_service import (
    create_knowledge_analytics_service,
)
from apps.knowledge.services.knowledge_search_service import (
    create_knowledge_search_service,
)

logger = logging.getLogger(__name__)


class AIServiceDiscoveryView(LoginRequiredMixin, OrganizationAccessMixin, RoleRequiredMixin, TemplateView):
    """AI-powered service discovery interface for exploring CLEAR APIs.

    Provides intelligent recommendations and documentation for API endpoints
    based on user role, experience level, and usage patterns.
    """

    template_name = "knowledge/ai/service_discovery.html"
    required_roles = ["utility-coordinator"]

    def get_context_data(self, **kwargs) -> dict[str, Any]:
        context = super().get_context_data(**kwargs)

        try:
            # Get user context for personalization
            user_role = self.get_user_role()
            experience_level = self.request.GET.get("experience_level", "intermediate")

            # Initialize services
            search_service = create_knowledge_search_service(
                organization=self.get_organization(),
                user=self.request.user,
            )
            analytics_service = create_knowledge_analytics_service(organization=self.get_organization())

            # Get API discovery data
            discovery_data = self._get_api_discovery_data(user_role, experience_level, search_service)

            # Get usage analytics for recommendations
            usage_stats = analytics_service.get_content_analytics(days=30, content_types=["api_documentation"])

            context.update(
                {
                    "page_title": _("AI Service Discovery"),
                    "discovery_data": discovery_data,
                    "usage_stats": usage_stats,
                    "user_role": user_role,
                    "experience_level": experience_level,
                    "featured_endpoints": self._get_featured_endpoints(),
                    "popular_searches": self._get_popular_api_searches(search_service),
                    "recent_discoveries": self._get_recent_discoveries(),
                    "ai_capabilities": self._get_ai_capabilities(),
                },
            )

        except Exception as e:
            logger.exception("Error in AI service discovery")
            messages.error(self.request, _("Error loading service discovery data"))
            context["error"] = str(e)

        return context

    def _get_api_discovery_data(self, user_role: str, experience_level: str, search_service) -> dict[str, Any]:
        """Get personalized API discovery data based on user profile."""
        return {
            "personalized_endpoints": self._get_personalized_endpoints(user_role),
            "learning_path": self._get_learning_path(experience_level),
            "quick_start_guides": self._get_quick_start_guides(user_role),
            "integration_examples": self._get_integration_examples(),
            "endpoint_categories": self._get_endpoint_categories(),
            "recommended_workflows": self._get_recommended_workflows(user_role),
        }

    def _get_personalized_endpoints(self, user_role: str) -> list[dict[str, Any]]:
        """Get API endpoints relevant to user's role."""
        role_endpoints = {
            "executive": [
                {
                    "name": "Dashboard Analytics",
                    "path": "/api/analytics/dashboard/",
                    "priority": "high",
                },
                {
                    "name": "Project Reports",
                    "path": "/api/projects/reports/",
                    "priority": "high",
                },
                {
                    "name": "Financial Summary",
                    "path": "/api/financial/summary/",
                    "priority": "medium",
                },
            ],
            "department-manager": [
                {
                    "name": "Project Management",
                    "path": "/api/projects/",
                    "priority": "high",
                },
                {
                    "name": "Team Analytics",
                    "path": "/api/analytics/teams/",
                    "priority": "high",
                },
                {
                    "name": "Resource Planning",
                    "path": "/api/resources/",
                    "priority": "medium",
                },
            ],
            "utility-coordinator": [
                {
                    "name": "Infrastructure Management",
                    "path": "/api/infrastructure/",
                    "priority": "high",
                },
                {"name": "Asset Tracking", "path": "/api/assets/", "priority": "high"},
                {
                    "name": "Work Orders",
                    "path": "/api/infrastructure/work-orders/",
                    "priority": "medium",
                },
            ],
            "stakeholder": [
                {
                    "name": "Project Status",
                    "path": "/api/projects/status/",
                    "priority": "high",
                },
                {
                    "name": "Public Reports",
                    "path": "/api/reports/public/",
                    "priority": "medium",
                },
                {
                    "name": "Document Access",
                    "path": "/api/documents/public/",
                    "priority": "medium",
                },
            ],
        }
        return role_endpoints.get(user_role, [])

    def _get_learning_path(self, experience_level: str) -> dict[str, Any]:
        """Get structured learning path based on experience level."""
        paths = {
            "beginner": {
                "title": _("Getting Started with CLEAR APIs"),
                "steps": [
                    {
                        "title": _("Authentication"),
                        "endpoint": "/api/auth/",
                        "time": "5 min",
                    },
                    {
                        "title": _("Basic Project Access"),
                        "endpoint": "/api/projects/",
                        "time": "10 min",
                    },
                    {
                        "title": _("Reading Data"),
                        "endpoint": "/api/projects/list/",
                        "time": "15 min",
                    },
                    {
                        "title": _("Error Handling"),
                        "endpoint": "/api/docs/errors/",
                        "time": "10 min",
                    },
                ],
                "estimated_time": "40 minutes",
            },
            "intermediate": {
                "title": _("Advanced API Integration"),
                "steps": [
                    {
                        "title": _("Hypermedia Navigation"),
                        "endpoint": "/api/docs/hypermedia/",
                        "time": "15 min",
                    },
                    {
                        "title": _("Batch Operations"),
                        "endpoint": "/api/docs/batch/",
                        "time": "20 min",
                    },
                    {
                        "title": _("Webhooks Setup"),
                        "endpoint": "/api/webhooks/",
                        "time": "25 min",
                    },
                    {
                        "title": _("Performance Optimization"),
                        "endpoint": "/api/docs/performance/",
                        "time": "20 min",
                    },
                ],
                "estimated_time": "80 minutes",
            },
            "advanced": {
                "title": _("Custom Integration Patterns"),
                "steps": [
                    {
                        "title": _("Custom Authentication"),
                        "endpoint": "/api/docs/custom-auth/",
                        "time": "30 min",
                    },
                    {
                        "title": _("Real-time Integration"),
                        "endpoint": "/api/realtime/",
                        "time": "45 min",
                    },
                    {
                        "title": _("Advanced Querying"),
                        "endpoint": "/api/docs/querying/",
                        "time": "35 min",
                    },
                    {
                        "title": _("Performance Monitoring"),
                        "endpoint": "/api/monitoring/",
                        "time": "40 min",
                    },
                ],
                "estimated_time": "150 minutes",
            },
        }
        return paths.get(experience_level, paths["intermediate"])

    def _get_featured_endpoints(self) -> list[dict[str, Any]]:
        """Get featured API endpoints with usage statistics."""
        return [
            {
                "name": "Projects API",
                "path": "/api/projects/",
                "description": _("Comprehensive project management"),
                "usage_trend": "up",
                "popularity": 95,
                "examples": ["Create project", "List projects", "Update status"],
            },
            {
                "name": "Infrastructure API",
                "path": "/api/infrastructure/",
                "description": _("Utility infrastructure management"),
                "usage_trend": "up",
                "popularity": 88,
                "examples": ["Asset tracking", "Spatial queries", "Work orders"],
            },
            {
                "name": "Analytics API",
                "path": "/api/analytics/",
                "description": _("Business intelligence and reporting"),
                "usage_trend": "stable",
                "popularity": 76,
                "examples": ["Performance metrics", "Custom reports", "Dashboards"],
            },
        ]

    def _get_popular_api_searches(self, search_service) -> list[dict[str, Any]]:
        """Get popular API-related searches from knowledge base."""
        try:
            # Search for API-related articles
            search_service.search_articles(
                query="API",
                organization=self.get_organization(),
                filters={"category": "api-documentation"},
            )

            return [
                {"query": "API authentication", "count": 145, "trend": "up"},
                {"query": "REST endpoints", "count": 132, "trend": "up"},
                {"query": "API rate limits", "count": 98, "trend": "stable"},
                {"query": "Webhook integration", "count": 87, "trend": "up"},
                {"query": "Error handling", "count": 76, "trend": "stable"},
            ]

        except (DatabaseError, IntegrityError, OperationalError) as e:
            logger.warning(f"Error getting popular API searches: {e}")
            return []

    def _get_recent_discoveries(self) -> list[dict[str, Any]]:
        """Get recently discovered or updated API endpoints."""
        return [
            {
                "name": "Real-time Collaboration API",
                "path": "/api/realtime/collaboration/",
                "added_date": timezone.now() - timedelta(days=3),
                "description": _("New WebRTC-based collaboration endpoints"),
                "status": "new",
            },
            {
                "name": "Enhanced Analytics API",
                "path": "/api/analytics/v2/",
                "added_date": timezone.now() - timedelta(days=7),
                "description": _("Updated analytics with AI insights"),
                "status": "updated",
            },
        ]

    def _get_ai_capabilities(self) -> dict[str, Any]:
        """Get available AI-powered API features."""
        return {
            "natural_language_query": True,
            "code_generation": True,
            "usage_analytics": True,
            "personalized_recommendations": True,
            "auto_documentation": True,
            "integration_testing": True,
        }

    def _get_quick_start_guides(self, user_role: str) -> list[dict[str, Any]]:
        """Get role-specific quick start guides."""
        return [
            {
                "title": _("5-Minute API Quickstart"),
                "description": _("Get up and running with basic API calls"),
                "duration": "5 min",
                "difficulty": "beginner",
            },
            {
                "title": _("Authentication Setup"),
                "description": _("Configure secure API authentication"),
                "duration": "10 min",
                "difficulty": "beginner",
            },
            {
                "title": _("Role-based Integration"),
                "description": _(f"API integration guide for {user_role}"),
                "duration": "15 min",
                "difficulty": "intermediate",
            },
        ]

    def _get_integration_examples(self) -> list[dict[str, Any]]:
        """Get code examples for common integration patterns."""
        return [
            {
                "language": "Python",
                "title": _("Python SDK Integration"),
                "complexity": "beginner",
                "use_case": "Data retrieval and basic operations",
            },
            {
                "language": "JavaScript",
                "title": _("Frontend Integration"),
                "complexity": "intermediate",
                "use_case": "Real-time UI updates",
            },
            {
                "language": "curl",
                "title": _("Command Line Testing"),
                "complexity": "beginner",
                "use_case": "Quick API testing and debugging",
            },
        ]

    def _get_endpoint_categories(self) -> dict[str, list[str]]:
        """Get API endpoints organized by category."""
        return {
            "Core": ["projects", "users", "authentication"],
            "Infrastructure": ["assets", "infrastructure", "spatial"],
            "Analytics": ["reports", "dashboards", "metrics"],
            "Integration": ["webhooks", "realtime", "notifications"],
            "Advanced": ["ai", "automation", "custom-workflows"],
        }

    def _get_recommended_workflows(self, user_role: str) -> list[dict[str, Any]]:
        """Get recommended API usage workflows for the user's role."""
        workflows = {
            "executive": [
                {
                    "name": _("Weekly Dashboard Sync"),
                    "steps": [
                        "Fetch analytics",
                        "Generate reports",
                        "Send notifications",
                    ],
                    "frequency": "weekly",
                },
            ],
            "utility-coordinator": [
                {
                    "name": _("Asset Monitoring Pipeline"),
                    "steps": [
                        "Query assets",
                        "Check status",
                        "Update records",
                        "Alert if needed",
                    ],
                    "frequency": "daily",
                },
            ],
        }
        return workflows.get(user_role, [])


class AIAPIExplorerView(
    LoginRequiredMixin,
    RoleRequiredMixin,
    HTMXResponseMixin,
    OrganizationAccessMixin,
    TemplateView,
):
    """Interactive API explorer with AI-powered suggestions and documentation."""

    template_name = "knowledge/ai/api_explorer.html"
    required_roles = ["utility-coordinator"]

    def get_context_data(self, **kwargs) -> dict[str, Any]:
        context = super().get_context_data(**kwargs)

        endpoint = self.request.GET.get("endpoint", "")
        method = self.request.GET.get("method", "GET")

        context.update(
            {
                "page_title": _("API Explorer"),
                "endpoint": endpoint,
                "method": method,
                "available_endpoints": self._get_available_endpoints(),
                "code_examples": self._get_code_examples(endpoint, method),
                "response_examples": self._get_response_examples(endpoint),
                "related_endpoints": self._get_related_endpoints(endpoint),
                "ai_suggestions": self._get_ai_suggestions(endpoint),
            },
        )

        return context

    def _get_available_endpoints(self) -> list[dict[str, Any]]:
        """Get list of available API endpoints with metadata."""
        return [
            {
                "path": "/api/projects/",
                "methods": ["GET", "POST"],
                "description": _("Project management operations"),
                "auth_required": True,
                "rate_limit": "1000/hour",
            },
            {
                "path": "/api/infrastructure/",
                "methods": ["GET", "POST", "PUT", "DELETE"],
                "description": _("Infrastructure asset management"),
                "auth_required": True,
                "rate_limit": "500/hour",
            },
            # Add more endpoints as needed
        ]

    def _get_code_examples(self, endpoint: str, method: str) -> dict[str, str]:
        """Generate code examples for the specified endpoint and method."""
        if not endpoint:
            return {}

        return {
            "python": self._generate_python_example(endpoint, method),
            "javascript": self._generate_javascript_example(endpoint, method),
            "curl": self._generate_curl_example(endpoint, method),
        }

    def _generate_python_example(self, endpoint: str, method: str) -> str:
        """Generate Python code example."""
        return f"""import requests

# Authentication
headers = {{
    'Authorization': 'Bearer YOUR_TOKEN',
    'Content-Type': 'application/json',
    'Accept': 'application/hal+json'
}}

# Make request
response = requests.{method.lower()}(
    'https://your-clear-instance.com{endpoint}',
    headers=headers
)

# Handle response
if response.status_code == 200:
    data = response.json()
    print(data)
else:
    print(f"Error: {{response.status_code}} - {{response.text}}")
"""

    def _generate_javascript_example(self, endpoint: str, method: str) -> str:
        """Generate JavaScript code example."""
        return f"""// Using fetch API
const response = await fetch('https://your-clear-instance.com{endpoint}', {{
    method: '{method}',
    headers: {{
        'Authorization': 'Bearer YOUR_TOKEN',
        'Content-Type': 'application/json',
        'Accept': 'application/hal+json'
    }}
}});

if (response.ok) {{
    const data = await response.json();
    console.log(data);
}} else {{
    console.error('Error:', response.status, response.statusText);
}}
"""

    def _generate_curl_example(self, endpoint: str, method: str) -> str:
        """Generate curl command example."""
        return f"""curl -X {method} \\
  -H "Authorization: Bearer YOUR_TOKEN" \\
  -H "Content-Type: application/json" \\
  -H "Accept: application/hal+json" \\
  "https://your-clear-instance.com{endpoint}"
"""

    def _get_response_examples(self, endpoint: str) -> dict[str, Any]:
        """Get example responses for the endpoint."""
        return {
            "success": {
                "status_code": 200,
                "headers": {"Content-Type": "application/hal+json"},
                "body": {"status": "success", "data": {}, "_links": {}},
            },
            "error": {
                "status_code": 400,
                "headers": {"Content-Type": "application/json"},
                "body": {"status": "error", "message": "Invalid request"},
            },
        }

    def _get_related_endpoints(self, endpoint: str) -> list[dict[str, Any]]:
        """Get endpoints related to the current one."""
        if "projects" in endpoint:
            return [
                {
                    "path": "/api/projects/{id}/",
                    "description": _("Get specific project"),
                },
                {
                    "path": "/api/projects/{id}/tasks/",
                    "description": _("Project tasks"),
                },
                {"path": "/api/projects/{id}/team/", "description": _("Project team")},
            ]
        return []

    def _get_ai_suggestions(self, endpoint: str) -> list[str]:
        """Get AI-powered suggestions for the endpoint."""
        return [
            _("Consider using pagination for large result sets"),
            _("Include error handling for network timeouts"),
            _("Use the Accept header to specify response format"),
            _("Check rate limits in response headers"),
        ]


class AIIntegrationSetupView(LoginRequiredMixin, OrganizationAccessMixin, RoleRequiredMixin, TemplateView):
    """Guided setup for AI-powered API integrations."""

    template_name = "knowledge/ai/integration_setup.html"
    required_roles = ["department-manager"]

    def get_context_data(self, **kwargs) -> dict[str, Any]:
        context = super().get_context_data(**kwargs)

        context.update(
            {
                "page_title": _("AI Integration Setup"),
                "setup_steps": self._get_setup_steps(),
                "integration_types": self._get_integration_types(),
                "configuration_options": self._get_configuration_options(),
                "testing_tools": self._get_testing_tools(),
            },
        )

        return context

    def _get_setup_steps(self) -> list[dict[str, Any]]:
        """Get step-by-step integration setup guide."""
        return [
            {
                "step": 1,
                "title": _("Choose Integration Type"),
                "description": _("Select the type of integration that best fits your needs"),
                "options": ["REST API", "Webhooks", "Real-time", "Batch Processing"],
            },
            {
                "step": 2,
                "title": _("Configure Authentication"),
                "description": _("Set up secure authentication for your integration"),
                "options": ["API Key", "OAuth 2.0", "JWT Token", "Custom"],
            },
            {
                "step": 3,
                "title": _("Test Connection"),
                "description": _("Verify your integration works correctly"),
                "options": ["Automated Tests", "Manual Testing", "Load Testing"],
            },
        ]

    def _get_integration_types(self) -> list[dict[str, Any]]:
        """Get available integration types with descriptions."""
        return [
            {
                "type": "rest_api",
                "name": _("REST API Integration"),
                "description": _("Standard HTTP-based API integration"),
                "complexity": "beginner",
                "use_cases": ["Data retrieval", "CRUD operations", "Reporting"],
            },
            {
                "type": "webhooks",
                "name": _("Webhook Integration"),
                "description": _("Real-time event notifications"),
                "complexity": "intermediate",
                "use_cases": ["Event notifications", "Real-time updates", "Automation"],
            },
            {
                "type": "realtime",
                "name": _("Real-time Integration"),
                "description": _("WebSocket-based live data streams"),
                "complexity": "advanced",
                "use_cases": ["Live dashboards", "Collaboration", "Monitoring"],
            },
        ]

    def _get_configuration_options(self) -> dict[str, Any]:
        """Get configuration options for integrations."""
        return {
            "authentication": {
                "api_key": {"security": "medium", "complexity": "low"},
                "oauth2": {"security": "high", "complexity": "medium"},
                "jwt": {"security": "high", "complexity": "low"},
            },
            "data_format": {
                "json": {"support": "full", "recommended": True},
                "hal_json": {"support": "full", "recommended": True},
                "xml": {"support": "limited", "recommended": False},
            },
            "rate_limiting": {
                "standard": "1000 requests/hour",
                "premium": "5000 requests/hour",
                "enterprise": "unlimited",
            },
        }

    def _get_testing_tools(self) -> list[dict[str, Any]]:
        """Get available testing tools and utilities."""
        return [
            {
                "name": _("API Test Suite"),
                "description": _("Automated testing for API endpoints"),
                "type": "automated",
            },
            {
                "name": _("Integration Validator"),
                "description": _("Validate integration configuration"),
                "type": "validation",
            },
            {
                "name": _("Performance Monitor"),
                "description": _("Monitor API performance and usage"),
                "type": "monitoring",
            },
        ]


class AIServiceHealthView(
    LoginRequiredMixin,
    RoleRequiredMixin,
    HTMXResponseMixin,
    OrganizationAccessMixin,
    View,
):
    """Health monitoring for AI-powered API services."""

    required_roles = ["utility-coordinator"]

    def get(self, request, *args, **kwargs):
        """Get health status of AI services."""
        try:
            health_data = self._get_health_status()

            if request.headers.get("HX-Request"):
                return self.render_htmx_response(
                    "knowledge/ai/partials/health_status.html",
                    {"health_data": health_data},
                )

            return JsonResponse(health_data)

        except Exception as e:
            logger.exception("Error getting AI service health")
            return JsonResponse({"status": "error", "message": str(e)}, status=500)

    def _get_health_status(self) -> dict[str, Any]:
        """Get comprehensive health status of AI services."""
        return {
            "overall_status": "healthy",
            "timestamp": timezone.now().isoformat(),
            "services": {
                "api_discovery": {
                    "status": "operational",
                    "response_time": "45ms",
                    "uptime": "99.9%",
                },
                "natural_language": {
                    "status": "operational",
                    "response_time": "120ms",
                    "uptime": "99.8%",
                },
                "code_generation": {
                    "status": "operational",
                    "response_time": "200ms",
                    "uptime": "99.7%",
                },
                "recommendations": {
                    "status": "operational",
                    "response_time": "80ms",
                    "uptime": "99.9%",
                },
            },
            "metrics": {
                "total_requests_24h": 1247,
                "avg_response_time": "95ms",
                "error_rate": "0.2%",
                "cache_hit_rate": "87%",
            },
        }


class AIUsageAnalyticsView(LoginRequiredMixin, OrganizationAccessMixin, RoleRequiredMixin, TemplateView):
    """Analytics for AI service usage and performance."""

    template_name = "knowledge/ai/usage_analytics.html"
    required_roles = ["department-manager"]

    def get_context_data(self, **kwargs) -> dict[str, Any]:
        context = super().get_context_data(**kwargs)

        days = int(self.request.GET.get("days", 30))

        context.update(
            {
                "page_title": _("AI Usage Analytics"),
                "analytics_period": days,
                "usage_stats": self._get_usage_statistics(days),
                "performance_metrics": self._get_performance_metrics(days),
                "popular_features": self._get_popular_features(days),
                "user_engagement": self._get_user_engagement(days),
                "trends": self._get_usage_trends(days),
            },
        )

        return context

    def _get_usage_statistics(self, days: int) -> dict[str, Any]:
        """Get AI service usage statistics."""
        return {
            "total_requests": 15420,
            "unique_users": 89,
            "avg_daily_requests": 514,
            "peak_usage_hour": "10:00 AM",
            "most_active_day": "Tuesday",
        }

    def _get_performance_metrics(self, days: int) -> dict[str, Any]:
        """Get performance metrics for AI services."""
        return {
            "avg_response_time": "125ms",
            "success_rate": "99.2%",
            "cache_efficiency": "84%",
            "error_rate": "0.8%",
            "timeout_rate": "0.1%",
        }

    def _get_popular_features(self, days: int) -> list[dict[str, Any]]:
        """Get most popular AI features by usage."""
        return [
            {"feature": "API Discovery", "usage": 45, "trend": "up"},
            {"feature": "Code Generation", "usage": 32, "trend": "up"},
            {"feature": "Natural Language Query", "usage": 28, "trend": "stable"},
            {"feature": "Integration Setup", "usage": 18, "trend": "up"},
            {"feature": "Health Monitoring", "usage": 12, "trend": "stable"},
        ]

    def _get_user_engagement(self, days: int) -> dict[str, Any]:
        """Get user engagement metrics."""
        return {
            "daily_active_users": 34,
            "session_duration": "8.5 minutes",
            "feature_adoption_rate": "67%",
            "user_satisfaction": "4.2/5",
            "return_user_rate": "78%",
        }

    def _get_usage_trends(self, days: int) -> dict[str, list[int]]:
        """Get usage trends over time."""
        return {
            "daily_requests": [450, 520, 480, 610, 580, 540, 490, 650, 580, 520],
            "response_times": [120, 115, 130, 125, 118, 140, 135, 122, 128, 125],
            "error_rates": [0.5, 0.8, 0.3, 1.2, 0.6, 0.9, 0.4, 0.7, 0.8, 0.5],
        }


class AIModelComparisonView(LoginRequiredMixin, OrganizationAccessMixin, RoleRequiredMixin, TemplateView):
    """Comparison and evaluation of different AI models and services."""

    template_name = "knowledge/ai/model_comparison.html"
    required_roles = ["executive"]

    def get_context_data(self, **kwargs) -> dict[str, Any]:
        context = super().get_context_data(**kwargs)

        context.update(
            {
                "page_title": _("AI Model Comparison"),
                "available_models": self._get_available_models(),
                "comparison_metrics": self._get_comparison_metrics(),
                "performance_benchmarks": self._get_performance_benchmarks(),
                "cost_analysis": self._get_cost_analysis(),
                "recommendations": self._get_model_recommendations(),
            },
        )

        return context

    def _get_available_models(self) -> list[dict[str, Any]]:
        """Get list of available AI models with capabilities."""
        return [
            {
                "name": "CLEAR Discovery Engine",
                "type": "API Discovery",
                "status": "active",
                "accuracy": "94%",
                "speed": "fast",
                "cost": "low",
            },
            {
                "name": "NLP Query Processor",
                "type": "Natural Language",
                "status": "active",
                "accuracy": "89%",
                "speed": "medium",
                "cost": "medium",
            },
            {
                "name": "Code Generation AI",
                "type": "Code Generation",
                "status": "active",
                "accuracy": "91%",
                "speed": "medium",
                "cost": "medium",
            },
        ]

    def _get_comparison_metrics(self) -> dict[str, list[float]]:
        """Get comparison metrics across models."""
        return {
            "accuracy": [94, 89, 91],
            "response_time": [45, 120, 200],  # milliseconds
            "throughput": [1000, 500, 300],  # requests/hour
            "cost_per_request": [0.001, 0.003, 0.005],  # USD
        }

    def _get_performance_benchmarks(self) -> dict[str, Any]:
        """Get performance benchmarks for AI models."""
        return {
            "latency_p95": "150ms",
            "throughput_peak": "2000 req/min",
            "error_rate": "0.5%",
            "uptime": "99.9%",
            "scalability": "excellent",
        }

    def _get_cost_analysis(self) -> dict[str, Any]:
        """Get cost analysis for AI services."""
        return {
            "monthly_cost": "$245",
            "cost_per_user": "$2.75",
            "roi_estimate": "340%",
            "savings_vs_manual": "$1,850/month",
            "break_even_point": "3 months",
        }

    def _get_model_recommendations(self) -> list[dict[str, Any]]:
        """Get recommendations for model selection and optimization."""
        return [
            {
                "type": "optimization",
                "title": _("Enable Response Caching"),
                "description": _("Implement caching to reduce response times by 40%"),
                "impact": "high",
                "effort": "low",
            },
            {
                "type": "feature",
                "title": _("Add Batch Processing"),
                "description": _("Support batch requests for improved throughput"),
                "impact": "medium",
                "effort": "medium",
            },
            {
                "type": "monitoring",
                "title": _("Enhanced Error Tracking"),
                "description": _("Implement detailed error classification and tracking"),
                "impact": "medium",
                "effort": "low",
            },
        ]


@require_http_methods(["POST"])
@login_required
def natural_language_query(request):
    """Process natural language queries about the API."""
    try:
        import json

        data = json.loads(request.body)
        query = data.get("query", "").strip()

        if not query:
            return JsonResponse({"status": "error", "message": _("Query is required")}, status=400)

        # Process the natural language query
        # This would integrate with actual AI services
        result = {
            "interpreted_query": query,
            "suggested_endpoints": [
                {"path": "/api/projects/", "relevance": 0.9},
                {"path": "/api/infrastructure/", "relevance": 0.7},
            ],
            "code_examples": {
                "python": f"# Query: {query}\n# Suggested implementation\nimport requests\n...",
                "curl": f"# Query: {query}\ncurl -X GET ...",
            },
            "confidence": 0.85,
            "suggestions": [
                _("Try searching for specific endpoint documentation"),
                _("Consider using the API explorer for interactive testing"),
            ],
        }

        return JsonResponse({"status": "success", "query": query, "result": result})

    except Exception:
        logger.exception("Error processing natural language query")
        return JsonResponse({"status": "error", "message": _("Failed to process query")}, status=500)


@require_http_methods(["GET"])
@login_required
@cache_page(60 * 15)  # Cache for 15 minutes
def api_playground_data(request):
    """Provide data for interactive API playground."""
    try:
        playground_data = {
            "featured_endpoints": [
                {
                    "name": "Projects",
                    "path": "/api/projects/",
                    "methods": ["GET", "POST"],
                    "description": _("Project management operations"),
                },
                {
                    "name": "Infrastructure",
                    "path": "/api/infrastructure/",
                    "methods": ["GET", "POST", "PUT", "DELETE"],
                    "description": _("Infrastructure asset management"),
                },
            ],
            "quick_actions": [
                {
                    "name": _("List Projects"),
                    "endpoint": "/api/projects/",
                    "method": "GET",
                },
                {
                    "name": _("Create Project"),
                    "endpoint": "/api/projects/",
                    "method": "POST",
                },
                {"name": _("Get Assets"), "endpoint": "/api/assets/", "method": "GET"},
            ],
            "user_context": {
                "role": getattr(request.user, "role", "user"),
                "permissions": ["read", "write"],  # Simplified
            },
            "ai_features": {
                "natural_language": True,
                "code_generation": True,
                "auto_complete": True,
            },
        }

        return JsonResponse({"status": "success", "data": playground_data})

    except Exception:
        logger.exception("Error generating playground data")
        return JsonResponse(
            {"status": "error", "message": _("Failed to generate playground data")},
            status=500,
        )
