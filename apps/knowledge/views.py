"""Knowledge Base Views Module.

from requests.exceptions import HTTPError, ConnectionError, TimeoutError
from datetime import date
from datetime import datetime
from datetime import timedelta
from django.contrib.auth.decorators import login_required
from django.db import models
from django.db.models import Count
from django.db.models import Q
from django.http import HttpResponse
from django.http import JsonResponse
from django.shortcuts import get_object_or_404
from django.shortcuts import redirect
from django.shortcuts import render
from django.urls import reverse
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from rest_framework import permissions
from rest_framework import status
from rest_framework.response import Response
from typing import Any
import json
import logging
import logging
logger = logging.getLogger(__name__)
import time

This module contains all knowledge base views including legacy function-based views,
modern class-based views, and HTMX endpoints for dynamic interactions. Provides
comprehensive knowledge management functionality with organization-based access control.
"""

import logging
from datetime import timedelta
from typing import Any

from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db.models import Count, Q
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.views.decorators.http import require_http_methods
from django.views.generic import (
    ListView,
    TemplateView,
)

from apps.common.mixins import (
    HTMXResponseMixin,
    OrganizationAccessMixin,
    RoleRequiredMixin,
)
from apps.knowledge.models import (
    ArticleComment,
    ArticleView,
    ArticleVote,
    ArticleWorkflow,
    KnowledgeArticle,
    KnowledgeCategory,
)
from apps.knowledge.models_search import (
    KnowledgeSearchAnalytics,
    SavedKnowledgeSearch,
)
from apps.knowledge.services.knowledge_analytics_service import (
    get_knowledge_analytics_service,
)

logger = logging.getLogger(__name__)


# ========================================
# MODERN CLASS-BASED VIEWS
# ========================================


class KnowledgeManagementHomeView(
    LoginRequiredMixin,
    RoleRequiredMixin,
    HTMXResponseMixin,
    OrganizationAccessMixin,
    TemplateView,
):
    """Modern knowledge management home page with comprehensive overview."""

    template_name = "knowledge/knowledge_home.html"
    required_roles = ["utility-coordinator", "department-manager", "executive"]

    def get_context_data(self, **kwargs) -> dict[str, Any]:
        """Get comprehensive knowledge home context."""
        context = super().get_context_data(**kwargs)

        try:
            # Get analytics service
            analytics_service = get_knowledge_analytics_service(self.request.user, self.get_organization())

            # Get featured articles
            featured_articles = (
                KnowledgeArticle.objects.filter(
                    organization=self.get_organization(),
                    status="published",
                    is_published=True,
                    is_featured=True,
                )
                .select_related("author", "category")
                .order_by("-published_at")[:6]
            )

            # Get recent articles
            recent_articles = (
                KnowledgeArticle.objects.filter(
                    organization=self.get_organization(),
                    status="published",
                    is_published=True,
                )
                .select_related("author", "category")
                .order_by("-published_at")[:8]
            )

            # Get popular articles (last 30 days)
            thirty_days_ago = timezone.now() - timedelta(days=30)
            popular_articles = (
                KnowledgeArticle.objects.filter(
                    organization=self.get_organization(),
                    status="published",
                    is_published=True,
                    views__viewed_at__gte=thirty_days_ago,
                )
                .annotate(recent_views=Count("views"))
                .order_by("-recent_views")[:5]
            )

            # Get top categories
            categories = (
                KnowledgeCategory.objects.filter(
                    organization=self.get_organization(),
                    is_public=True,
                )
                .annotate(
                    article_count=Count(
                        "articles",
                        filter=Q(
                            articles__status="published",
                            articles__is_published=True,
                        ),
                    ),
                )
                .order_by("-article_count")[:8]
            )

            # Get knowledge statistics
            stats = {
                "total_articles": KnowledgeArticle.objects.filter(
                    organization=self.get_organization(),
                    status="published",
                    is_published=True,
                ).count(),
                "total_categories": KnowledgeCategory.objects.filter(
                    organization=self.get_organization(),
                    is_public=True,
                ).count(),
                "total_views": ArticleView.objects.filter(
                    article__organization=self.get_organization(),
                    viewed_at__gte=thirty_days_ago,
                ).count(),
                "total_comments": ArticleComment.objects.filter(
                    article__organization=self.get_organization(),
                    is_approved=True,
                    created_at__gte=thirty_days_ago,
                ).count(),
            }

            # Get user activity summary
            user_activity = {
                "articles_authored": KnowledgeArticle.objects.filter(
                    organization=self.get_organization(),
                    author=self.request.user,
                ).count(),
                "comments_made": ArticleComment.objects.filter(
                    article__organization=self.get_organization(),
                    author=self.request.user,
                    is_approved=True,
                ).count(),
                "saved_searches": SavedKnowledgeSearch.objects.filter(
                    user=self.request.user,
                    organization=self.get_organization(),
                ).count(),
            }

            # Get content health metrics
            content_health = analytics_service.get_system_performance_metrics()

            context.update(
                {
                    "page_title": _("Knowledge Management"),
                    "featured_articles": featured_articles,
                    "recent_articles": recent_articles,
                    "popular_articles": popular_articles,
                    "categories": categories,
                    "stats": stats,
                    "user_activity": user_activity,
                    "content_health": content_health,
                    "organization": self.get_organization(),
                },
            )

        except Exception:
            logger.exception("Error loading knowledge home")
            context["error"] = _("Error loading knowledge base data")

        return context


class KnowledgePublicView(
    LoginRequiredMixin,
    RoleRequiredMixin,
    HTMXResponseMixin,
    OrganizationAccessMixin,
    ListView,
):
    """Public knowledge base view for general users."""

    required_roles = ["stakeholder"]

    model = KnowledgeArticle
    template_name = "knowledge/public_knowledge.html"
    context_object_name = "articles"
    paginate_by = 12

    def get_queryset(self):
        """Get published articles for public viewing."""
        return (
            KnowledgeArticle.objects.filter(
                organization=self.get_organization(),
                status="published",
                is_published=True,
            )
            .select_related("author", "category")
            .prefetch_related("votes")
            .order_by("-published_at")
        )

    def get_context_data(self, **kwargs) -> dict[str, Any]:
        """Get public knowledge context."""
        context = super().get_context_data(**kwargs)

        # Get search query
        search_query = self.request.GET.get("q", "")
        if search_query:
            queryset = self.get_queryset().filter(
                Q(title__icontains=search_query)
                | Q(content__icontains=search_query)
                | Q(summary__icontains=search_query),
            )
            context["articles"] = queryset

        # Get categories for filtering
        categories = (
            KnowledgeCategory.objects.filter(
                organization=self.get_organization(),
                is_public=True,
            )
            .annotate(article_count=Count("articles"))
            .order_by("name")
        )

        context.update(
            {
                "page_title": _("Knowledge Base"),
                "categories": categories,
                "search_query": search_query,
            },
        )

        return context


class KnowledgeSetupDocsView(
    LoginRequiredMixin,
    RoleRequiredMixin,
    HTMXResponseMixin,
    OrganizationAccessMixin,
    TemplateView,
):
    """Developer setup documentation and guides."""

    template_name = "knowledge/setup_docs.html"
    required_roles = ["department-manager", "executive"]

    def get_context_data(self, **kwargs) -> dict[str, Any]:
        """Get setup documentation context."""
        context = super().get_context_data(**kwargs)

        # Get setup and configuration articles
        setup_articles = (
            KnowledgeArticle.objects.filter(
                organization=self.get_organization(),
                status="published",
                is_published=True,
                tags__contains=["setup", "configuration", "installation"],
            )
            .select_related("author", "category")
            .order_by("-published_at")[:10]
        )

        # Get troubleshooting articles
        troubleshooting_articles = (
            KnowledgeArticle.objects.filter(
                organization=self.get_organization(),
                status="published",
                is_published=True,
                tags__contains=["troubleshooting", "debugging", "issues"],
            )
            .select_related("author", "category")
            .order_by("-published_at")[:10]
        )

        context.update(
            {
                "page_title": _("Setup Documentation"),
                "setup_articles": setup_articles,
                "troubleshooting_articles": troubleshooting_articles,
            },
        )

        return context


class KnowledgeIntegrationView(
    LoginRequiredMixin,
    RoleRequiredMixin,
    HTMXResponseMixin,
    OrganizationAccessMixin,
    TemplateView,
):
    """Knowledge base integration with other system components."""

    template_name = "knowledge/integration.html"
    required_roles = ["department-manager", "executive"]

    def get_context_data(self, **kwargs) -> dict[str, Any]:
        """Get integration context with cross-references."""
        context = super().get_context_data(**kwargs)

        try:
            # Get articles that reference projects, documents, etc.
            integration_data = {
                "project_articles": self._get_articles_by_tags(["project", "projects"]),
                "document_articles": self._get_articles_by_tags(["document", "documentation"]),
                "process_articles": self._get_articles_by_tags(["process", "workflow", "procedure"]),
                "compliance_articles": self._get_articles_by_tags(["compliance", "regulation", "standard"]),
            }

            # Get cross-reference statistics
            cross_ref_stats = {
                "total_cross_refs": sum(len(articles) for articles in integration_data.values()),
                "integration_coverage": self._calculate_integration_coverage(),
            }

            context.update(
                {
                    "page_title": _("Knowledge Integration"),
                    "integration_data": integration_data,
                    "cross_ref_stats": cross_ref_stats,
                },
            )

        except Exception:
            logger.exception("Error loading integration data")
            context["error"] = _("Error loading integration data")

        return context

    def _get_articles_by_tags(self, tags: list[str]):
        """Get articles that contain any of the specified tags."""
        return (
            KnowledgeArticle.objects.filter(
                organization=self.get_organization(),
                status="published",
                is_published=True,
            )
            .filter(
                Q(tags__overlap=tags)
                | Q(title__iregex=r"\b(" + "|".join(tags) + r")\b")
                | Q(content__iregex=r"\b(" + "|".join(tags) + r")\b"),
            )
            .select_related("author", "category")
            .order_by("-published_at")[:5]
        )

    def _calculate_integration_coverage(self) -> float:
        """Calculate what percentage of content has integration references."""
        total_articles = KnowledgeArticle.objects.filter(
            organization=self.get_organization(),
            status="published",
            is_published=True,
        ).count()

        if total_articles == 0:
            return 0.0

        integrated_articles = (
            KnowledgeArticle.objects.filter(
                organization=self.get_organization(),
                status="published",
                is_published=True,
                tags__isnull=False,
            )
            .exclude(tags=[])
            .count()
        )

        return (integrated_articles / total_articles) * 100


# ========================================
# FUNCTION-BASED HTMX VIEWS
# ========================================


@login_required
@require_http_methods(["GET"])
def knowledge_quick_stats_htmx(request):
    """HTMX endpoint for quick knowledge statistics."""
    try:
        organization = getattr(request.user, "organization", None)
        if not organization:
            return JsonResponse({"error": "No organization found"}, status=400)

        # Get basic statistics
        thirty_days_ago = timezone.now() - timedelta(days=30)

        stats = {
            "total_articles": KnowledgeArticle.objects.filter(
                organization=organization,
                status="published",
                is_published=True,
            ).count(),
            "draft_articles": KnowledgeArticle.objects.filter(
                organization=organization,
                status="draft",
            ).count(),
            "pending_review": KnowledgeArticle.objects.filter(
                organization=organization,
                status="review",
            ).count(),
            "recent_views": ArticleView.objects.filter(
                article__organization=organization,
                viewed_at__gte=thirty_days_ago,
            ).count(),
            "total_categories": KnowledgeCategory.objects.filter(
                organization=organization,
                is_public=True,
            ).count(),
            "active_searches": KnowledgeSearchAnalytics.objects.filter(
                organization=organization,
                created_at__gte=thirty_days_ago,
            ).count(),
        }

        context = {
            "stats": stats,
        }

        return render(
            request,
            "knowledge/partials/quick_stats.html",
            context,
        )

    except Exception:
        logger.exception("Error loading quick stats")
        return render(
            request,
            "knowledge/partials/error.html",
            {"error": _("Error loading statistics")},
        )


@login_required
@require_http_methods(["GET"])
def knowledge_activity_feed_htmx(request):
    """HTMX endpoint for knowledge activity feed."""
    try:
        organization = getattr(request.user, "organization", None)
        if not organization:
            return JsonResponse({"error": "No organization found"}, status=400)

        limit = int(request.GET.get("limit", 20))

        # Get recent activities
        recent_articles = (
            KnowledgeArticle.objects.filter(
                organization=organization,
                status="published",
                is_published=True,
            )
            .select_related("author", "category")
            .order_by("-published_at")[: limit // 2]
        )

        recent_comments = (
            ArticleComment.objects.filter(
                article__organization=organization,
                is_approved=True,
            )
            .select_related("author", "article")
            .order_by("-created_at")[: limit // 2]
        )

        # Combine activities
        activities = []

        for article in recent_articles:
            activities.append(
                {
                    "type": "article_published",
                    "article": article,
                    "timestamp": article.published_at or article.created_at,
                    "user": article.author,
                },
            )

        for comment in recent_comments:
            activities.append(
                {
                    "type": "comment_added",
                    "comment": comment,
                    "article": comment.article,
                    "timestamp": comment.created_at,
                    "user": comment.author,
                },
            )

        # Sort by timestamp
        activities.sort(key=lambda x: x["timestamp"], reverse=True)
        activities = activities[:limit]

        context = {
            "activities": activities,
        }

        return render(
            request,
            "knowledge/partials/activity_feed.html",
            context,
        )

    except Exception:
        logger.exception("Error loading activity feed")
        return render(
            request,
            "knowledge/partials/error.html",
            {"error": _("Error loading activity feed")},
        )


@login_required
@require_http_methods(["POST"])
def knowledge_article_workflow_htmx(request, article_id):
    """HTMX endpoint for article workflow actions."""
    try:
        organization = getattr(request.user, "organization", None)
        if not organization:
            return JsonResponse({"error": "No organization found"}, status=400)

        article = get_object_or_404(
            KnowledgeArticle,
            id=article_id,
            organization=organization,
        )

        # Check permissions
        if not (
            article.author == request.user
            or request.user.is_staff
            or (
                hasattr(request.user, "has_organization_role")
                and request.user.has_organization_role(["department-manager", "executive"])
            )
        ):
            return HttpResponse("Permission denied", status=403)

        action = request.POST.get("action")
        notes = request.POST.get("notes", "")

        old_status = article.status
        workflow_action = action

        # Handle workflow actions
        if action == "submit_review":
            article.status = "review"
        elif action == "approve":
            if not (
                request.user.is_staff
                or (
                    hasattr(request.user, "has_organization_role")
                    and request.user.has_organization_role(["department-manager", "executive"])
                )
            ):
                return HttpResponse("Permission denied", status=403)
            article.status = "published"
            article.is_published = True
            if not article.published_at:
                article.published_at = timezone.now()
        elif action == "reject":
            if not (
                request.user.is_staff
                or (
                    hasattr(request.user, "has_organization_role")
                    and request.user.has_organization_role(["department-manager", "executive"])
                )
            ):
                return HttpResponse("Permission denied", status=403)
            article.status = "draft"
        elif action == "archive":
            article.status = "archived"
            article.is_published = False
        else:
            return HttpResponse("Invalid action", status=400)

        article.save()

        # Record workflow action
        ArticleWorkflow.objects.create(
            article=article,
            from_status=old_status,
            to_status=article.status,
            action=workflow_action,
            performed_by=request.user,
            notes=notes,
        )

        context = {
            "article": article,
            "success_message": _(f"Article {workflow_action} successfully!"),
        }

        return render(
            request,
            "knowledge/partials/article_status_updated.html",
            context,
        )

    except Exception:
        logger.exception("Error in workflow action")
        return HttpResponse("Error processing workflow action", status=500)


# ========================================
# LEGACY FUNCTION-BASED VIEWS FOR BACKWARD COMPATIBILITY
# ========================================


@login_required
def knowledge_base_view(request):
    """Legacy knowledge base view - redirects to modern view."""
    return redirect("knowledge:home")


@login_required
def knowledge_article_detail_legacy(request, article_id):
    """Legacy article detail view - redirects to modern view."""
    return redirect("knowledge:article-detail", pk=article_id)


@login_required
def knowledge_dashboard_legacy(request):
    """Legacy dashboard view - redirects to modern view."""
    return redirect("knowledge:dashboard")


@login_required
def notebook_view(request):
    """Legacy notebook view - simple implementation."""
    try:
        organization = getattr(request.user, "organization", None)
        if organization:
            # Get user's draft articles as "notes"
            user_notes = KnowledgeArticle.objects.filter(
                organization=organization,
                author=request.user,
                status="draft",
            ).order_by("-created_at")[:20]
        else:
            user_notes = []
    except (ConnectionError, TimeoutError, HTTPError) as e:
        logger.warning(f"Error loading notebook: {e}")
        user_notes = []

    context = {
        "notes": user_notes,
        "page_title": _("My Notebook"),
    }

    return render(request, "knowledge/notebook.html", context)


@login_required
def knowledge_search_api_legacy(request):
    """Legacy API endpoint for knowledge search."""
    try:
        organization = getattr(request.user, "organization", None)
        if not organization:
            return JsonResponse({"error": "No organization found"}, status=400)

        query = request.GET.get("q", "")

        # Simple search for legacy compatibility
        queryset = KnowledgeArticle.objects.filter(
            organization=organization,
            status="published",
            is_published=True,
        )

        if query:
            queryset = queryset.filter(
                Q(title__icontains=query) | Q(content__icontains=query) | Q(summary__icontains=query),
            )

        # Limit results for API
        articles = queryset.select_related("author", "category")[:20]

        results = []
        for article in articles:
            results.append(
                {
                    "id": article.id,
                    "title": article.title,
                    "summary": article.summary,
                    "url": f"/knowledge/articles/{article.pk}/",
                    "author": article.author.get_full_name() or article.author.username,
                    "category": article.category.name if article.category else None,
                    "published_at": (article.published_at.isoformat() if article.published_at else None),
                },
            )

        return JsonResponse(
            {
                "results": results,
                "total": len(results),
                "query": query,
            },
        )

    except Exception as e:
        logger.exception("Error in legacy search API")
        return JsonResponse({"error": str(e)}, status=500)


# ========================================
# ADDITIONAL UTILITY VIEWS
# ========================================


@login_required
@require_http_methods(["GET"])
def knowledge_export_data_htmx(request):
    """HTMX endpoint for exporting knowledge data."""
    try:
        organization = getattr(request.user, "organization", None)
        if not organization:
            return JsonResponse({"error": "No organization found"}, status=400)

        # Check permissions
        if not (
            request.user.is_staff
            or (
                hasattr(request.user, "has_organization_role")
                and request.user.has_organization_role(["department-manager", "executive"])
            )
        ):
            return HttpResponse("Permission denied", status=403)

        export_format = request.GET.get("format", "json")
        include_drafts = request.GET.get("include_drafts", "false").lower() == "true"

        # Get articles to export
        queryset = KnowledgeArticle.objects.filter(
            organization=organization,
        ).select_related("author", "category")

        if not include_drafts:
            queryset = queryset.filter(status="published", is_published=True)

        articles = queryset.order_by("-published_at")

        if export_format == "csv":
            # Create CSV export
            import csv

            from django.http import HttpResponse as CSVResponse

            response = CSVResponse(content_type="text/csv")
            response["Content-Disposition"] = (
                f'attachment; filename="knowledge_export_{timezone.now().strftime("%Y%m%d")}.csv"'
            )

            writer = csv.writer(response)
            writer.writerow(["Title", "Author", "Category", "Status", "Published Date", "Summary"])

            for article in articles:
                writer.writerow(
                    [
                        article.title,
                        article.author.get_full_name() or article.author.username,
                        article.category.name if article.category else "",
                        article.status,
                        (article.published_at.strftime("%Y-%m-%d") if article.published_at else ""),
                        article.summary or "",
                    ],
                )

            return response

        # JSON export
        export_data = {
            "exported_at": timezone.now().isoformat(),
            "organization": (organization.name if hasattr(organization, "name") else str(organization)),
            "total_articles": articles.count(),
            "articles": [],
        }

        for article in articles:
            export_data["articles"].append(
                {
                    "id": article.id,
                    "title": article.title,
                    "summary": article.summary,
                    "content": article.content,
                    "author": article.author.get_full_name() or article.author.username,
                    "category": article.category.name if article.category else None,
                    "status": article.status,
                    "published_at": (article.published_at.isoformat() if article.published_at else None),
                    "created_at": article.created_at.isoformat(),
                    "tags": article.tags or [],
                },
            )

        response = JsonResponse(export_data)
        response["Content-Disposition"] = (
            f'attachment; filename="knowledge_export_{timezone.now().strftime("%Y%m%d")}.json"'
        )
        return response

    except Exception:
        logger.exception("Error exporting knowledge data")
        return HttpResponse("Error exporting data", status=500)


@login_required
@require_http_methods(["GET"])
def knowledge_analytics_summary_htmx(request):
    """HTMX endpoint for knowledge analytics summary."""
    try:
        organization = getattr(request.user, "organization", None)
        if not organization:
            return JsonResponse({"error": "No organization found"}, status=400)

        days = int(request.GET.get("days", 30))
        since_date = timezone.now() - timedelta(days=days)

        # Calculate analytics
        analytics = {
            "content_metrics": {
                "total_articles": KnowledgeArticle.objects.filter(
                    organization=organization,
                    status="published",
                    is_published=True,
                ).count(),
                "new_articles": KnowledgeArticle.objects.filter(
                    organization=organization,
                    status="published",
                    is_published=True,
                    published_at__gte=since_date,
                ).count(),
                "updated_articles": KnowledgeArticle.objects.filter(
                    organization=organization,
                    status="published",
                    is_published=True,
                    updated_at__gte=since_date,
                ).count(),
            },
            "engagement_metrics": {
                "total_views": ArticleView.objects.filter(
                    article__organization=organization,
                    viewed_at__gte=since_date,
                ).count(),
                "unique_viewers": ArticleView.objects.filter(
                    article__organization=organization,
                    viewed_at__gte=since_date,
                )
                .values("user")
                .distinct()
                .count(),
                "total_votes": ArticleVote.objects.filter(
                    article__organization=organization,
                    voted_at__gte=since_date,
                ).count(),
                "total_comments": ArticleComment.objects.filter(
                    article__organization=organization,
                    created_at__gte=since_date,
                    is_approved=True,
                ).count(),
            },
            "search_metrics": {
                "total_searches": KnowledgeSearchAnalytics.objects.filter(
                    organization=organization,
                    created_at__gte=since_date,
                ).count(),
                "unique_searchers": KnowledgeSearchAnalytics.objects.filter(
                    organization=organization,
                    created_at__gte=since_date,
                )
                .values("user")
                .distinct()
                .count(),
            },
        }

        context = {
            "analytics": analytics,
            "days": days,
        }

        return render(
            request,
            "knowledge/partials/analytics_summary.html",
            context,
        )

    except Exception:
        logger.exception("Error loading analytics summary")
        return render(
            request,
            "knowledge/partials/error.html",
            {"error": _("Error loading analytics")},
        )
