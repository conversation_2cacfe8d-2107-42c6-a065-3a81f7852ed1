"""
Timesheet and timer HTMX views for dynamic time tracking interfaces.

This module provides HTMX-powered endpoints for time tracking, timer management,
timesheet visualization, and comprehensive time entry operations with real-time
updates and organization-based access control.
"""

import csv
import json
import logging
from datetime import datetime, timedelta
from decimal import Decimal

try:
    from requests.exceptions import HTTPError, ConnectionError, RequestException
except ImportError:
    # Fallback for development environments
    class HTTPError(Exception):
        pass
    class ConnectionError(Exception):
        pass
    class RequestException(Exception):
        pass

try:
    from urllib3.exceptions import TimeoutError
except ImportError:
    # Fallback for development environments
    class TimeoutError(Exception):
        pass

from django.contrib.auth.decorators import login_required
from django.core.exceptions import ValidationError
from django.db import models, transaction
from django.db.models import Q, Sum
from django.http import HttpRequest, HttpResponse, JsonResponse
from django.shortcuts import render
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.views.decorators.http import require_http_methods

from django.contrib.auth.decorators import login_required
from django.core.exceptions import ValidationError
from django.db.models import Q, Sum
from django.http import HttpRequest, HttpResponse, JsonResponse
from django.shortcuts import render
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.views.decorators.http import require_http_methods

from apps.projects.models import Project, Task

from ...models import TimeEntry, WorkType

logger = logging.getLogger(__name__)


# ========== TIMESHEET SUMMARY HTMX VIEWS ==========


@login_required
@require_http_methods(["GET"])
def timesheet_week_summary(request: HttpRequest) -> HttpResponse:
    """Get comprehensive weekly timesheet summary for HTMX dashboard updates.

    Provides detailed weekly time tracking analytics including daily breakdowns,
    project distribution, billable vs non-billable hours, and organization isolation.
    """
    try:
        organization = getattr(request.user, "organization", None)
        if not organization:
            return HttpResponse(_("Organization membership required"), status=403)

        # Get week date range (default to current week)
        week_offset = int(request.GET.get("week_offset", 0))
        today = timezone.now().date()
        week_start = today - timedelta(days=today.weekday()) + timedelta(weeks=week_offset)
        week_end = week_start + timedelta(days=6)

        # Override with custom dates if provided
        start_date_param = request.GET.get("start_date")
        end_date_param = request.GET.get("end_date")

        if start_date_param and end_date_param:
            try:
                week_start = datetime.strptime(start_date_param, "%Y-%m-%d").date()
                week_end = datetime.strptime(end_date_param, "%Y-%m-%d").date()
            except ValueError:
                # Keep default dates on invalid format
                pass

        # Get time entries for the week with organization validation
        time_entries = (
            TimeEntry.objects.filter(
                user=request.user,
                project__organization=organization,
                start_time__date__range=[week_start, week_end],
            )
            .select_related("project", "task", "work_type", "approved_by")
            .order_by("start_time")
        )

        # Calculate comprehensive statistics
        stats = time_entries.aggregate(
            total_hours=Sum("duration_hours"),
            billable_hours=Sum("duration_hours", filter=Q(is_billable=True)),
            approved_hours=Sum("duration_hours", filter=Q(approval_status="approved")),
            pending_hours=Sum("duration_hours", filter=Q(approval_status="pending")),
        )

        # Convert None values to Decimal defaults
        total_hours = stats["total_hours"] or Decimal("0.00")
        billable_hours = stats["billable_hours"] or Decimal("0.00")
        approved_hours = stats["approved_hours"] or Decimal("0.00")
        pending_hours = stats["pending_hours"] or Decimal("0.00")

        # Calculate daily breakdowns
        daily_summary = {}
        project_summary = {}

        for entry in time_entries:
            # Daily totals
            date_str = entry.start_time.date().strftime("%Y-%m-%d")
            if date_str not in daily_summary:
                daily_summary[date_str] = {
                    "total": Decimal("0.00"),
                    "billable": Decimal("0.00"),
                    "entries": [],
                }

            daily_summary[date_str]["total"] += entry.duration_hours or Decimal("0.00")
            if entry.is_billable:
                daily_summary[date_str]["billable"] += entry.duration_hours or Decimal("0.00")
            daily_summary[date_str]["entries"].append(entry)

            # Project totals
            project_name = entry.project.name if entry.project else _("No Project")
            if project_name not in project_summary:
                project_summary[project_name] = {
                    "total": Decimal("0.00"),
                    "billable": Decimal("0.00"),
                    "entries_count": 0,
                }

            project_summary[project_name]["total"] += entry.duration_hours or Decimal("0.00")
            if entry.is_billable:
                project_summary[project_name]["billable"] += entry.duration_hours or Decimal("0.00")
            project_summary[project_name]["entries_count"] += 1

        # Calculate percentage metrics
        billable_percentage = (
            (billable_hours / total_hours * 100).quantize(Decimal("0.1")) if total_hours > 0 else Decimal("0.0")
        )

        approval_percentage = (
            (approved_hours / total_hours * 100).quantize(Decimal("0.1")) if total_hours > 0 else Decimal("0.0")
        )

        context = {
            "time_entries": time_entries,
            "week_start": week_start,
            "week_end": week_end,
            "week_offset": week_offset,
            "summary_stats": {
                "total_hours": total_hours,
                "billable_hours": billable_hours,
                "approved_hours": approved_hours,
                "pending_hours": pending_hours,
                "billable_percentage": billable_percentage,
                "approval_percentage": approval_percentage,
            },
            "daily_summary": daily_summary,
            "project_summary": project_summary,
            "organization": organization,
        }

        return render(request, "financial/partials/week_summary.html", context)

    except Exception:
        logger.exception("Error loading timesheet week summary for user {request.user.username}")
        return HttpResponse(
            _("Error loading timesheet summary"),
            status=500,
            headers={"HX-Trigger": "timesheet-error"},
        )


@login_required
@require_http_methods(["GET"])
def timesheet_summary_partial(request: HttpRequest) -> HttpResponse:
    """Get real-time timesheet summary with recent entries and daily totals.

    Provides HTMX partial for dashboard display including recent time entries,
    current week progress, and approval status with organization isolation.
    """
    try:
        organization = getattr(request.user, "organization", None)
        if not organization:
            return HttpResponse(_("Organization membership required"), status=403)

        # Get recent time entries (last 10)
        recent_entries = (
            TimeEntry.objects.filter(user=request.user, project__organization=organization)
            .select_related("project", "task", "work_type", "approved_by")
            .order_by("-start_time")[:10]
        )

        # Calculate current week totals
        today = timezone.now().date()
        week_start = today - timedelta(days=today.weekday())

        week_entries = TimeEntry.objects.filter(
            user=request.user,
            project__organization=organization,
            start_time__date__gte=week_start,
        )

        # Daily totals for current week
        daily_totals = {}
        for i in range(7):  # 7 days in a week
            date = week_start + timedelta(days=i)
            date_str = date.strftime("%Y-%m-%d")
            daily_totals[date_str] = {
                "date": date,
                "total": Decimal("0.00"),
                "billable": Decimal("0.00"),
                "day_name": date.strftime("%A"),
            }

        # Populate daily totals
        for entry in week_entries:
            date_str = entry.start_time.date().strftime("%Y-%m-%d")
            if date_str in daily_totals:
                daily_totals[date_str]["total"] += entry.duration_hours or Decimal("0.00")
                if entry.is_billable:
                    daily_totals[date_str]["billable"] += entry.duration_hours or Decimal("0.00")

        # Week summary statistics
        week_stats = week_entries.aggregate(
            total_hours=Sum("duration_hours"),
            billable_hours=Sum("duration_hours", filter=Q(is_billable=True)),
            entries_count=Sum("id"),
        )

        context = {
            "recent_entries": recent_entries,
            "daily_totals": daily_totals,
            "week_stats": {
                "total_hours": week_stats["total_hours"] or Decimal("0.00"),
                "billable_hours": week_stats["billable_hours"] or Decimal("0.00"),
                "entries_count": week_stats["entries_count"] or 0,
            },
            "week_start": week_start,
            "organization": organization,
        }

        return render(request, "financial/partials/timesheet_summary.html", context)

    except Exception:
        logger.exception("Error loading timesheet summary partial for user {request.user.username}")
        return HttpResponse(
            _("Error loading summary"),
            status=500,
            headers={"HX-Trigger": "summary-error"},
        )


# ========== TIMESHEET EXPORT HTMX VIEWS ==========


@login_required
@require_http_methods(["GET"])
def export_timesheet_summary_csv(request: HttpRequest) -> HttpResponse:
    """Export comprehensive timesheet data as CSV with filtering options.

    Supports date range filtering, project filtering, and detailed time
    entry export with organization-based access control.
    """
    try:
        organization = getattr(request.user, "organization", None)
        if not organization:
            return HttpResponse(_("Organization membership required"), status=403)

        # Parse date range parameters
        start_date_param = request.GET.get("start_date")
        end_date_param = request.GET.get("end_date")
        project_id = request.GET.get("project_id")

        if start_date_param and end_date_param:
            try:
                start_date = datetime.strptime(start_date_param, "%Y-%m-%d").date()
                end_date = datetime.strptime(end_date_param, "%Y-%m-%d").date()
            except ValueError:
                return HttpResponse(_("Invalid date format. Please use YYYY-MM-DD."), status=400)
        else:
            # Default to current week
            today = timezone.now().date()
            start_date = today - timedelta(days=today.weekday())
            end_date = start_date + timedelta(days=6)

        # Build query with organization validation
        queryset = TimeEntry.objects.filter(
            user=request.user,
            project__organization=organization,
            start_time__date__range=[start_date, end_date],
        ).select_related("project", "task", "work_type", "approved_by")

        # Apply project filter if specified
        if project_id:
            try:
                queryset = queryset.filter(project_id=project_id)
            except ValueError:
                pass  # Invalid project ID, ignore filter

        time_entries = queryset.order_by("start_time")

        # Create CSV response
        response = HttpResponse(content_type="text/csv; charset=utf-8")
        filename = f"timesheet_{request.user.username}_{start_date}_{end_date}.csv"
        response["Content-Disposition"] = f'attachment; filename="{filename}"'

        writer = csv.writer(response)

        # Write comprehensive header
        writer.writerow(
            [
                _("Date"),
                _("Start Time"),
                _("End Time"),
                _("Project"),
                _("Task"),
                _("Work Type"),
                _("Hours"),
                _("Hourly Rate"),
                _("Total Amount"),
                _("Description"),
                _("Billable"),
                _("Approval Status"),
                _("Approved By"),
                _("Notes"),
            ],
        )

        # Write time entry data
        for entry in time_entries:
            hourly_rate = getattr(entry, "hourly_rate", None) or Decimal("0.00")
            duration = entry.duration_hours or Decimal("0.00")
            total_amount = duration * hourly_rate

            writer.writerow(
                [
                    entry.start_time.date().strftime("%Y-%m-%d"),
                    (entry.start_time.time().strftime("%H:%M:%S") if entry.start_time else ""),
                    (entry.end_time.time().strftime("%H:%M:%S") if entry.end_time else ""),
                    entry.project.name if entry.project else "",
                    entry.task.name if entry.task else "",
                    entry.work_type.name if entry.work_type else "",
                    str(duration),
                    str(hourly_rate),
                    str(total_amount),
                    entry.description or "",
                    _("Yes") if entry.is_billable else _("No"),
                    (
                        entry.get_approval_status_display()
                        if hasattr(entry, "get_approval_status_display")
                        else entry.approval_status
                    ),
                    entry.approved_by.get_full_name() if entry.approved_by else "",
                    getattr(entry, "notes", "") or "",
                ],
            )

        return response

    except Exception:
        logger.exception("Error exporting timesheet CSV for user {request.user.username}")
        return HttpResponse(_("Error exporting timesheet"), status=500)


@login_required
@require_http_methods(["GET"])
def export_timesheet_summary_json(request: HttpRequest) -> HttpResponse:
    """Export comprehensive timesheet data as structured JSON.

    Provides detailed JSON export with metadata, summary statistics,
    and complete time entry data with organization validation.
    """
    try:
        organization = getattr(request.user, "organization", None)
        if not organization:
            return JsonResponse({"error": str(_("Organization membership required"))}, status=403)

        # Parse date range parameters
        start_date_param = request.GET.get("start_date")
        end_date_param = request.GET.get("end_date")
        project_id = request.GET.get("project_id")

        if start_date_param and end_date_param:
            try:
                start_date = datetime.strptime(start_date_param, "%Y-%m-%d").date()
                end_date = datetime.strptime(end_date_param, "%Y-%m-%d").date()
            except ValueError:
                return JsonResponse(
                    {"error": str(_("Invalid date format. Please use YYYY-MM-DD."))},
                    status=400,
                )
        else:
            # Default to current week
            today = timezone.now().date()
            start_date = today - timedelta(days=today.weekday())
            end_date = start_date + timedelta(days=6)

        # Build query with organization validation
        queryset = TimeEntry.objects.filter(
            user=request.user,
            project__organization=organization,
            start_time__date__range=[start_date, end_date],
        ).select_related("project", "task", "work_type", "approved_by")

        # Apply project filter if specified
        if project_id:
            try:
                queryset = queryset.filter(project_id=project_id)
            except ValueError:
                pass  # Invalid project ID, ignore filter

        time_entries = queryset.order_by("start_time")

        # Calculate summary statistics
        summary_stats = time_entries.aggregate(
            total_hours=Sum("duration_hours"),
            billable_hours=Sum("duration_hours", filter=Q(is_billable=True)),
            total_entries=Sum("id"),
            approved_entries=Sum("id", filter=Q(approval_status="approved")),
        )

        # Prepare comprehensive JSON data
        data = {
            "export_metadata": {
                "user": request.user.username,
                "organization": organization.name,
                "export_date": timezone.now().isoformat(),
                "period": {
                    "start": start_date.isoformat(),
                    "end": end_date.isoformat(),
                },
                "filters": {
                    "project_id": project_id,
                },
            },
            "summary_statistics": {
                "total_hours": str(summary_stats["total_hours"] or Decimal("0.00")),
                "billable_hours": str(summary_stats["billable_hours"] or Decimal("0.00")),
                "total_entries": summary_stats["total_entries"] or 0,
                "approved_entries": summary_stats["approved_entries"] or 0,
                "billable_percentage": str(
                    (
                        (summary_stats["billable_hours"] / summary_stats["total_hours"] * 100).quantize(Decimal("0.1"))
                        if summary_stats["total_hours"] and summary_stats["total_hours"] > 0
                        else Decimal("0.0")
                    ),
                ),
            },
            "time_entries": [],
        }

        # Add detailed time entry data
        for entry in time_entries:
            hourly_rate = getattr(entry, "hourly_rate", None) or Decimal("0.00")
            duration = entry.duration_hours or Decimal("0.00")

            entry_data = {
                "id": str(entry.id),
                "date": entry.start_time.date().isoformat(),
                "start_time": (entry.start_time.isoformat() if entry.start_time else None),
                "end_time": entry.end_time.isoformat() if entry.end_time else None,
                "project": {
                    "id": str(entry.project.id) if entry.project else None,
                    "name": entry.project.name if entry.project else None,
                },
                "task": {
                    "id": str(entry.task.id) if entry.task else None,
                    "name": entry.task.name if entry.task else None,
                },
                "work_type": {
                    "id": str(entry.work_type.id) if entry.work_type else None,
                    "name": entry.work_type.name if entry.work_type else None,
                },
                "duration_hours": str(duration),
                "hourly_rate": str(hourly_rate),
                "total_amount": str(duration * hourly_rate),
                "description": entry.description or "",
                "is_billable": entry.is_billable,
                "approval_status": entry.approval_status,
                "approved_by": (entry.approved_by.get_full_name() if entry.approved_by else None),
                "notes": getattr(entry, "notes", "") or "",
                "created_at": (entry.created_at.isoformat() if hasattr(entry, "created_at") else None),
            }
            data["time_entries"].append(entry_data)

        # Create JSON response
        response = HttpResponse(
            json.dumps(data, indent=2, ensure_ascii=False),
            content_type="application/json; charset=utf-8",
        )
        filename = f"timesheet_{request.user.username}_{start_date}_{end_date}.json"
        response["Content-Disposition"] = f'attachment; filename="{filename}"'

        return response

    except Exception:
        logger.exception("Error exporting timesheet JSON for user {request.user.username}")
        return JsonResponse({"error": str(_("Error exporting timesheet"))}, status=500)


# ========== TIME ENTRY MANAGEMENT HTMX VIEWS ==========


@login_required
@require_http_methods(["POST"])
def quick_time_entry_htmx(request: HttpRequest) -> HttpResponse:
    """Create time entry via HTMX with comprehensive validation.

    Provides fast time entry creation with project validation,
    organization access control, and real-time feedback.
    """
    try:
        organization = getattr(request.user, "organization", None)
        if not organization:
            return HttpResponse(
                f'<div class="alert alert-danger">{_("Organization membership required")}</div>',
                status=403,
            )

        # Extract and validate form data
        project_id = request.POST.get("project_id")
        task_id = request.POST.get("task_id")
        work_type_id = request.POST.get("work_type_id")
        hours = request.POST.get("hours")
        description = request.POST.get("description", "").strip()
        date = request.POST.get("date")
        billable = request.POST.get("billable") == "on"

        # Validate required fields
        if not project_id or not hours:
            return HttpResponse(
                f'<div class="alert alert-danger">{_("Project and hours are required")}</div>',
                status=400,
                headers={"HX-Trigger": "validation-error"},
            )

        # Validate hours
        try:
            hours_decimal = Decimal(str(hours))
            if hours_decimal <= 0:
                return HttpResponse(
                    f'<div class="alert alert-danger">{_("Hours must be positive")}</div>',
                    status=400,
                    headers={"HX-Trigger": "validation-error"},
                )
        except (ValueError, TypeError):
            return HttpResponse(
                f'<div class="alert alert-danger">{_("Invalid hours format")}</div>',
                status=400,
                headers={"HX-Trigger": "validation-error"},
            )

        # Get and validate project with organization check
        try:
            project = Project.objects.get(id=project_id, organization=organization)
        except Project.DoesNotExist:
            return HttpResponse(
                f'<div class="alert alert-danger">{_("Project not found or access denied")}</div>',
                status=404,
                headers={"HX-Trigger": "project-error"},
            )

        # Get task if provided (validate it belongs to project)
        task = None
        if task_id:
            try:
                task = Task.objects.get(id=task_id, project=project)
            except Task.DoesNotExist:
                task = None  # Ignore invalid task

        # Get work type (validate it belongs to organization)
        work_type = None
        if work_type_id:
            try:
                work_type = WorkType.objects.get(id=work_type_id, organization=organization)
            except WorkType.DoesNotExist:
                work_type = None

        # Get or create default work type if none specified
        if not work_type:
            work_type, created = WorkType.objects.get_or_create(
                name="General",
                organization=organization,
                defaults={
                    "description": str(_("General work type")),
                    "is_billable": True,
                    "default_rate": Decimal("0.00"),
                },
            )

        # Parse date
        entry_datetime = timezone.now()
        if date:
            try:
                entry_date = datetime.strptime(date, "%Y-%m-%d").date()
                entry_datetime = timezone.make_aware(datetime.combine(entry_date, datetime.min.time()))
            except ValueError:
                # Keep current datetime on invalid format
                pass

        # Create time entry with validation
        try:
            time_entry = TimeEntry.objects.create(
                user=request.user,
                project=project,
                task=task,
                work_type=work_type,
                start_time=entry_datetime,
                duration_hours=hours_decimal.quantize(Decimal("0.01")),
                description=description,
                is_billable=billable,
                hourly_rate=work_type.default_rate or Decimal("0.00"),
                approval_status="pending",
                billing_status="unbilled",
            )

            logger.info(
                f"Quick time entry created: {time_entry.id} by {request.user.username} for project {project.name}",
            )

            context = {
                "time_entry": time_entry,
                "success": True,
                "message": str(_("Time entry created successfully")),
            }

            response = render(request, "financial/partials/time_entry_created.html", context)
            response["HX-Trigger"] = "time-entry-created"
            return response

        except ValidationError as e:
            logger.warning(f"Time entry validation error: {e}")
            return HttpResponse(
                f'<div class="alert alert-danger">{_("Validation error")}: {e}</div>',
                status=400,
                headers={"HX-Trigger": "validation-error"},
            )

    except Exception:
        logger.exception("Error creating quick time entry for user {request.user.username}")
        return HttpResponse(
            f'<div class="alert alert-danger">{_("Error creating time entry")}</div>',
            status=500,
            headers={"HX-Trigger": "time-entry-error"},
        )


# ========== TIMER MANAGEMENT HTMX VIEWS ==========


@login_required
@require_http_methods(["POST"])
def start_timer_htmx(request: HttpRequest) -> HttpResponse:
    """Start time tracking timer via HTMX with session management.

    Manages active timer state in session with project validation
    and organization access control.
    """
    try:
        organization = getattr(request.user, "organization", None)
        if not organization:
            return HttpResponse(
                f'<div class="alert alert-danger">{_("Organization membership required")}</div>',
                status=403,
            )

        project_id = request.POST.get("project_id")
        task_id = request.POST.get("task_id")
        work_type_id = request.POST.get("work_type_id")
        description = request.POST.get("description", "").strip()

        if not project_id:
            return HttpResponse(
                f'<div class="alert alert-danger">{_("Project is required")}</div>',
                status=400,
                headers={"HX-Trigger": "validation-error"},
            )

        # Check for existing active timer
        existing_timer = request.session.get("active_timer")
        if existing_timer:
            return HttpResponse(
                f'<div class="alert alert-warning">{_("Timer already running. Stop current timer first.")}</div>',
                status=400,
                headers={"HX-Trigger": "timer-conflict"},
            )

        # Validate project with organization check
        try:
            project = Project.objects.get(id=project_id, organization=organization)
        except Project.DoesNotExist:
            return HttpResponse(
                f'<div class="alert alert-danger">{_("Project not found or access denied")}</div>',
                status=404,
                headers={"HX-Trigger": "project-error"},
            )

        # Validate task if provided
        task = None
        if task_id:
            try:
                task = Task.objects.get(id=task_id, project=project)
            except Task.DoesNotExist:
                task = None  # Ignore invalid task

        # Validate work type if provided
        work_type = None
        if work_type_id:
            try:
                work_type = WorkType.objects.get(id=work_type_id, organization=organization)
            except WorkType.DoesNotExist:
                work_type = None

        # Start new timer - store comprehensive data in session
        timer_data = {
            "project_id": project_id,
            "project_name": project.name,
            "task_id": task_id,
            "task_name": task.name if task else None,
            "work_type_id": work_type_id,
            "work_type_name": work_type.name if work_type else None,
            "description": description,
            "start_time": timezone.now().isoformat(),
            "user_id": request.user.id,
            "organization_id": organization.id,
        }

        request.session["active_timer"] = timer_data
        request.session.modified = True

        logger.info(f"Timer started by {request.user.username} for project {project.name}")

        context = {
            "timer": timer_data,
            "success": True,
            "message": str(_("Timer started successfully")),
        }

        response = render(request, "financial/partials/timer_started.html", context)
        response["HX-Trigger"] = "timer-started"
        return response

    except Exception:
        logger.exception("Error starting timer for user {request.user.username}")
        return HttpResponse(
            f'<div class="alert alert-danger">{_("Error starting timer")}</div>',
            status=500,
            headers={"HX-Trigger": "timer-error"},
        )


@login_required
@require_http_methods(["POST"])
def stop_timer_htmx(request: HttpRequest) -> HttpResponse:
    """Stop active timer and create time entry via HTMX.

    Calculates elapsed time, creates time entry record,
    and clears timer state with comprehensive validation.
    """
    try:
        organization = getattr(request.user, "organization", None)
        if not organization:
            return HttpResponse(
                f'<div class="alert alert-danger">{_("Organization membership required")}</div>',
                status=403,
            )

        timer_data = request.session.get("active_timer")
        if not timer_data:
            return HttpResponse(
                f'<div class="alert alert-warning">{_("No active timer found")}</div>',
                status=400,
                headers={"HX-Trigger": "no-timer"},
            )

        # Validate timer belongs to current user and organization
        if timer_data.get("user_id") != request.user.id or timer_data.get("organization_id") != organization.id:
            return HttpResponse(
                f'<div class="alert alert-danger">{_("Invalid timer session")}</div>',
                status=403,
                headers={"HX-Trigger": "timer-invalid"},
            )

        # Calculate elapsed time
        try:
            start_time = datetime.fromisoformat(timer_data["start_time"])
            end_time = timezone.now()
            elapsed = end_time - start_time
            hours = Decimal(str(elapsed.total_seconds())) / Decimal("3600")
        except (ValueError, KeyError) as e:
            logger.error(f"Timer calculation error: {e}")
            return HttpResponse(
                f'<div class="alert alert-danger">{_("Timer calculation error")}</div>',
                status=400,
                headers={"HX-Trigger": "timer-error"},
            )

        # Validate minimum time (prevent very short entries)
        min_duration = Decimal("0.01")  # 36 seconds minimum
        if hours < min_duration:
            return HttpResponse(
                f'<div class="alert alert-warning">{_("Timer duration too short. Minimum 1 minute required.")}</div>',
                status=400,
                headers={"HX-Trigger": "timer-too-short"},
            )

        # Get project and validate access
        try:
            project = Project.objects.get(id=timer_data["project_id"], organization=organization)
        except Project.DoesNotExist:
            return HttpResponse(
                f'<div class="alert alert-danger">{_("Project not found or access denied")}</div>',
                status=404,
                headers={"HX-Trigger": "project-error"},
            )

        # Get task if specified
        task = None
        if timer_data.get("task_id"):
            try:
                task = Task.objects.get(id=timer_data["task_id"], project=project)
            except Task.DoesNotExist:
                task = None  # Task may have been deleted

        # Get work type or create default
        work_type = None
        if timer_data.get("work_type_id"):
            try:
                work_type = WorkType.objects.get(id=timer_data["work_type_id"], organization=organization)
            except WorkType.DoesNotExist:
                work_type = None

        if not work_type:
            work_type, created = WorkType.objects.get_or_create(
                name="General",
                organization=organization,
                defaults={
                    "description": str(_("General work type")),
                    "is_billable": True,
                    "default_rate": Decimal("0.00"),
                },
            )

        # Create time entry from timer
        try:
            time_entry = TimeEntry.objects.create(
                user=request.user,
                project=project,
                task=task,
                work_type=work_type,
                start_time=start_time,
                end_time=end_time,
                duration_hours=hours.quantize(Decimal("0.01")),
                description=timer_data.get("description", ""),
                is_billable=work_type.is_billable,
                hourly_rate=work_type.default_rate or Decimal("0.00"),
                approval_status="pending",
                billing_status="unbilled",
                is_timer_based=True,
            )

            logger.info(
                f"Timer stopped and time entry created: {time_entry.id} "
                f"by {request.user.username} for {hours:.2f} hours",
            )

            # Clear timer from session
            if "active_timer" in request.session:
                del request.session["active_timer"]
                request.session.modified = True

            context = {
                "time_entry": time_entry,
                "elapsed_hours": float(hours),
                "success": True,
                "message": str(_("Timer stopped and time entry created")),
            }

            response = render(request, "financial/partials/timer_stopped.html", context)
            response["HX-Trigger"] = "timer-stopped"
            return response

        except ValidationError as e:
            logger.warning(f"Time entry validation error: {e}")
            return HttpResponse(
                f'<div class="alert alert-danger">{_("Validation error")}: {e}</div>',
                status=400,
                headers={"HX-Trigger": "validation-error"},
            )

    except Exception:
        logger.exception("Error stopping timer for user {request.user.username}")
        return HttpResponse(
            f'<div class="alert alert-danger">{_("Error stopping timer")}</div>',
            status=500,
            headers={"HX-Trigger": "timer-error"},
        )


@login_required
@require_http_methods(["GET"])
def timer_status_htmx(request: HttpRequest) -> HttpResponse:
    """Get real-time timer status for HTMX polling updates.

    Provides current timer state including elapsed time calculation
    and timer metadata for dashboard display.
    """
    try:
        timer_data = request.session.get("active_timer")

        if timer_data:
            # Validate timer belongs to current user
            if timer_data.get("user_id") != request.user.id:
                # Clear invalid timer
                if "active_timer" in request.session:
                    del request.session["active_timer"]
                    request.session.modified = True
                timer_data = None

        if timer_data:
            try:
                start_time = datetime.fromisoformat(timer_data["start_time"])
                elapsed = timezone.now() - start_time
                hours = Decimal(str(elapsed.total_seconds())) / Decimal("3600")

                context = {
                    "timer": timer_data,
                    "elapsed_hours": float(hours),
                    "elapsed_formatted": f"{int(elapsed.total_seconds() // 3600):02d}:{int((elapsed.total_seconds() % 3600) // 60):02d}:{int(elapsed.total_seconds() % 60):02d}",
                    "is_running": True,
                }
            except (ValueError, KeyError) as e:
                logger.warning(f"Timer status calculation error: {e}")
                # Clear corrupted timer
                if "active_timer" in request.session:
                    del request.session["active_timer"]
                    request.session.modified = True
                context = {"is_running": False}
        else:
            context = {"is_running": False}

        return render(request, "financial/partials/timer_status.html", context)

    except Exception:
        logger.exception("Error getting timer status for user {request.user.username}")
        return HttpResponse(_("Error loading timer status"), status=500)


# ========== TIMESHEET MAIN VIEWS ==========


@login_required
@require_http_methods(["GET", "POST"])
def timesheet_view(request: HttpRequest) -> HttpResponse:
    """Main timesheet view with period selection and bulk operations."""
    from datetime import datetime, timedelta

    from django.utils import timezone

    from apps.common.mixins import get_user_organization
    from apps.financial.models import TimeEntry

    organization = get_user_organization(request.user)
    if not organization:
        return HttpResponse('<div class="alert alert-warning">No organization found</div>')

    # Get current period or default to current week
    period_start = request.GET.get("period_start")
    if period_start:
        try:
            start_date = datetime.strptime(period_start, "%Y-%m-%d").date()
        except ValueError:
            start_date = timezone.now().date() - timedelta(days=timezone.now().weekday())
    else:
        start_date = timezone.now().date() - timedelta(days=timezone.now().weekday())

    end_date = start_date + timedelta(days=6)

    # Get time entries for the period
    time_entries = (
        TimeEntry.objects.filter(user=request.user, start_time__date__range=[start_date, end_date])
        .select_related("project", "work_type", "task")
        .order_by("start_time")
    )

    # Calculate totals
    total_hours = sum(entry.duration_hours or 0 for entry in time_entries)
    billable_hours = sum(entry.duration_hours or 0 for entry in time_entries if entry.work_type.is_billable)

    context = {
        "time_entries": time_entries,
        "period_start": start_date,
        "period_end": end_date,
        "total_hours": total_hours,
        "billable_hours": billable_hours,
        "week_days": [(start_date + timedelta(days=i)) for i in range(7)],
    }

    if request.htmx:
        return render(request, "financial/partials/timesheet_view.html", context)
    return render(request, "financial/timesheet_view.html", context)


@login_required
@require_http_methods(["POST"])
def save_timesheet(request: HttpRequest) -> HttpResponse:
    """Save timesheet data with bulk operations and validation."""
    import json
    from decimal import Decimal

    from django.db import transaction
    from django.utils import timezone

    from apps.financial.models import TimeEntry, WorkType
    from apps.projects.models import Project

    try:
        # Parse timesheet data from request
        timesheet_data = json.loads(request.body)
        entries_data = timesheet_data.get("entries", [])

        if not entries_data:
            return HttpResponse('<div class="alert alert-warning">No timesheet data provided</div>')

        saved_count = 0
        updated_count = 0
        errors = []

        with transaction.atomic():
            for entry_data in entries_data:
                try:
                    entry_id = entry_data.get("id")
                    project_id = entry_data.get("project_id")
                    work_type_id = entry_data.get("work_type_id")
                    start_time = entry_data.get("start_time")
                    duration_hours = entry_data.get("duration_hours")
                    description = entry_data.get("description", "")

                    # Validate required fields
                    if not all([project_id, work_type_id, start_time, duration_hours]):
                        errors.append(f"Missing required fields for entry {entry_id or 'new'}")
                        continue

                    # Get related objects
                    project = Project.objects.get(id=project_id)
                    work_type = WorkType.objects.get(id=work_type_id)

                    # Parse datetime
                    start_datetime = timezone.datetime.fromisoformat(start_time)
                    duration_decimal = Decimal(str(duration_hours))

                    if entry_id:
                        # Update existing entry
                        entry = TimeEntry.objects.get(id=entry_id, user=request.user)
                        entry.project = project
                        entry.work_type = work_type
                        entry.start_time = start_datetime
                        entry.duration_hours = duration_decimal
                        entry.duration_minutes = int(duration_decimal * 60)
                        entry.description = description
                        entry.save()
                        updated_count += 1
                    else:
                        # Create new entry
                        end_time = start_datetime + timezone.timedelta(hours=float(duration_hours))
                        entry = TimeEntry.objects.create(
                            user=request.user,
                            project=project,
                            work_type=work_type,
                            start_time=start_datetime,
                            end_time=end_time,
                            duration_hours=duration_decimal,
                            duration_minutes=int(duration_decimal * 60),
                            description=description,
                        )
                        saved_count += 1

                except (ConnectionError, TimeoutError, HTTPError) as e:
                    errors.append(f"Error processing entry {entry_id or 'new'}: {e!s}")

        # Prepare response
        if errors:
            error_msg = "<br>".join(errors)
            return HttpResponse(
                f'<div class="alert alert-warning">Partial save completed. Errors:<br>{error_msg}</div>'
            )

        success_msg = f"Timesheet saved successfully. {saved_count} new entries, {updated_count} updated."
        return HttpResponse(f'<div class="alert alert-success">{success_msg}</div>')

    except json.JSONDecodeError:
        return HttpResponse('<div class="alert alert-danger">Invalid JSON data</div>')
    except (json.JSONDecodeError, ValueError) as e:
        return HttpResponse(f'<div class="alert alert-danger">Error saving timesheet: {e!s}</div>')


@login_required
@require_http_methods(["POST"])
def submit_timesheet(request: HttpRequest) -> HttpResponse:
    """Submit timesheet for approval with workflow integration."""
    from datetime import datetime

    from django.db import transaction
    from django.utils import timezone

    from apps.common.mixins import get_user_organization
    from apps.financial.models import TimeEntry, TimesheetEntry, TimesheetPeriod

    try:
        organization = get_user_organization(request.user)
        if not organization:
            return HttpResponse('<div class="alert alert-warning">No organization found</div>')

        # Get period parameters
        period_start = request.POST.get("period_start")
        period_end = request.POST.get("period_end")

        if not period_start or not period_end:
            return HttpResponse('<div class="alert alert-warning">Period dates required</div>')

        start_date = datetime.strptime(period_start, "%Y-%m-%d").date()
        end_date = datetime.strptime(period_end, "%Y-%m-%d").date()

        with transaction.atomic():
            # Get or create timesheet period
            period, created = TimesheetPeriod.objects.get_or_create(
                organization=organization,
                start_date=start_date,
                end_date=end_date,
                defaults={"status": "active", "created_by": request.user},
            )

            # Check if already submitted
            existing_entry = TimesheetEntry.objects.filter(user=request.user, period=period).first()

            if existing_entry and existing_entry.status in ["submitted", "approved"]:
                return HttpResponse(f'<div class="alert alert-warning">Timesheet already {existing_entry.status}</div>')

            # Get time entries for the period
            time_entries = TimeEntry.objects.filter(user=request.user, start_time__date__range=[start_date, end_date])

            if not time_entries.exists():
                return HttpResponse('<div class="alert alert-warning">No time entries found for this period</div>')

            # Calculate totals
            total_hours = sum(entry.duration_hours or 0 for entry in time_entries)
            billable_hours = sum(entry.duration_hours or 0 for entry in time_entries if entry.work_type.is_billable)

            # Create or update timesheet entry
            if existing_entry:
                timesheet_entry = existing_entry
                timesheet_entry.total_hours = total_hours
                timesheet_entry.billable_hours = billable_hours
                timesheet_entry.status = "submitted"
                timesheet_entry.submitted_at = timezone.now()
                timesheet_entry.save()
            else:
                timesheet_entry = TimesheetEntry.objects.create(
                    user=request.user,
                    period=period,
                    total_hours=total_hours,
                    billable_hours=billable_hours,
                    status="submitted",
                    submitted_at=timezone.now(),
                )

            # Update individual time entries to submitted status
            time_entries.update(approval_status="submitted")

        return HttpResponse(
            f'<div class="alert alert-success">Timesheet submitted for approval. '
            f"Total hours: {total_hours}, Billable hours: {billable_hours}</div>"
        )

    except (ConnectionError, TimeoutError, HTTPError) as e:
        return HttpResponse(f'<div class="alert alert-danger">Error submitting timesheet: {e!s}</div>')


@login_required
@require_http_methods(["PATCH"])
def update_time_entry_htmx(request: HttpRequest) -> HttpResponse:
    """Update time entry with HTMX-powered inline editing using PATCH method."""
    import json
    from decimal import Decimal

    from django.utils import timezone

    from apps.financial.models import TimeEntry, WorkType
    from apps.projects.models import Project

    try:
        # Extract entry ID from URL path
        entry_id = request.resolver_match.kwargs.get("entry_id")
        if not entry_id:
            return HttpResponse('<div class="alert alert-warning">Entry ID required</div>', status=400)

        # Get the time entry (ensure user owns it)
        try:
            time_entry = TimeEntry.objects.get(id=entry_id, user=request.user)
        except TimeEntry.DoesNotExist:
            return HttpResponse('<div class="alert alert-danger">Time entry not found</div>', status=404)

        # Check if entry is already approved (can't edit approved entries)
        if time_entry.approval_status == "approved":
            return HttpResponse(
                '<div class="alert alert-warning">Cannot edit approved time entries</div>',
                status=403,
            )

        # Parse JSON body for PATCH request
        try:
            data = json.loads(request.body) if request.body else {}
        except json.JSONDecodeError:
            return HttpResponse('<div class="alert alert-danger">Invalid JSON data</div>', status=400)

        # Get updated fields from JSON or form data
        project_id = data.get("project_id") or request.POST.get("project_id")
        work_type_id = data.get("work_type_id") or request.POST.get("work_type_id")
        start_time = data.get("start_time") or request.POST.get("start_time")
        duration_hours = data.get("duration_hours") or request.POST.get("duration_hours")
        description = data.get("description") or request.POST.get("description", "")

        # Validate and update fields
        updated_fields = []

        if project_id and project_id != str(time_entry.project.id):
            try:
                project = Project.objects.get(id=project_id)
                time_entry.project = project
                updated_fields.append("project")
            except Project.DoesNotExist:
                return HttpResponse('<div class="alert alert-danger">Invalid project</div>', status=400)

        if work_type_id and work_type_id != str(time_entry.work_type.id):
            try:
                work_type = WorkType.objects.get(id=work_type_id)
                time_entry.work_type = work_type
                updated_fields.append("work type")
            except WorkType.DoesNotExist:
                return HttpResponse(
                    '<div class="alert alert-danger">Invalid work type</div>',
                    status=400,
                )

        if start_time:
            try:
                start_datetime = timezone.datetime.fromisoformat(start_time)
                time_entry.start_time = start_datetime
                updated_fields.append("start time")
            except ValueError:
                return HttpResponse(
                    '<div class="alert alert-danger">Invalid start time format</div>',
                    status=400,
                )

        if duration_hours:
            try:
                duration_decimal = Decimal(str(duration_hours))
                if duration_decimal <= 0:
                    return HttpResponse(
                        '<div class="alert alert-danger">Duration must be positive</div>',
                        status=400,
                    )

                time_entry.duration_hours = duration_decimal
                time_entry.duration_minutes = int(duration_decimal * 60)

                # Update end time based on new duration
                if time_entry.start_time:
                    time_entry.end_time = time_entry.start_time + timezone.timedelta(hours=float(duration_hours))

                updated_fields.append("duration")
            except (ValueError, TypeError):
                return HttpResponse(
                    '<div class="alert alert-danger">Invalid duration format</div>',
                    status=400,
                )

        if description != time_entry.description:
            time_entry.description = description
            updated_fields.append("description")

        # Save the changes
        time_entry.save()

        # Prepare success message
        if updated_fields:
            fields_text = ", ".join(updated_fields)
            success_msg = f"Time entry updated: {fields_text}"
        else:
            success_msg = "No changes made"

        # Return updated entry HTML fragment
        context = {
            "time_entry": time_entry,
            "success_message": success_msg,
            "updated_fields": updated_fields,
        }

        response = render(request, "financial/partials/time_entry_updated.html", context)
        response["HX-Trigger"] = "time-entry-updated"
        return response

    except Exception as e:
        logger.exception(f"Error updating time entry {entry_id} for user {request.user.username}")
        return HttpResponse(
            f'<div class="alert alert-danger">Error updating time entry: {str(e)}</div>',
            status=500,
        )


@login_required
@require_http_methods(["GET"])
def time_entry_edit_form_htmx(request: HttpRequest) -> HttpResponse:
    """Get inline edit form for time entry with HTMX."""
    try:
        # Extract entry ID from URL path
        entry_id = request.resolver_match.kwargs.get("entry_id")
        if not entry_id:
            return HttpResponse('<div class="alert alert-warning">Entry ID required</div>', status=400)

        # Get the time entry (ensure user owns it)
        try:
            time_entry = TimeEntry.objects.get(id=entry_id, user=request.user)
        except TimeEntry.DoesNotExist:
            return HttpResponse('<div class="alert alert-danger">Time entry not found</div>', status=404)

        # Check if entry is already approved (can't edit approved entries)
        if time_entry.approval_status == "approved":
            return HttpResponse(
                '<div class="alert alert-warning">Cannot edit approved time entries</div>',
                status=403,
            )

        # Get user's organization for filtering projects and work types
        organization = getattr(request.user, "organization", None)
        if not organization:
            return HttpResponse(
                '<div class="alert alert-danger">Organization membership required</div>',
                status=403,
            )

        # Get available projects and work types for the user's organization
        projects = Project.objects.filter(organization=organization).order_by("name")
        work_types = WorkType.objects.filter(organization=organization, is_active=True).order_by("name")
        tasks = Task.objects.filter(project=time_entry.project).order_by("name") if time_entry.project else []

        context = {
            "time_entry": time_entry,
            "projects": projects,
            "work_types": work_types,
            "tasks": tasks,
            "organization": organization,
        }

        return render(request, "financial/partials/time_entry_edit_form.html", context)

    except Exception:
        logger.exception(f"Error loading edit form for time entry {entry_id} for user {request.user.username}")
        return HttpResponse('<div class="alert alert-danger">Error loading edit form</div>', status=500)


@login_required
@require_http_methods(["POST"])
def update_time_entry(request: HttpRequest) -> HttpResponse:
    """Update time entry with HTMX-powered inline editing (legacy POST method)."""
    from decimal import Decimal

    from django.utils import timezone

    from apps.financial.models import TimeEntry, WorkType
    from apps.projects.models import Project

    try:
        entry_id = request.POST.get("entry_id")
        if not entry_id:
            return HttpResponse('<div class="alert alert-warning">Entry ID required</div>')

        # Get the time entry (ensure user owns it)
        try:
            time_entry = TimeEntry.objects.get(id=entry_id, user=request.user)
        except TimeEntry.DoesNotExist:
            return HttpResponse('<div class="alert alert-danger">Time entry not found</div>')

        # Check if entry is already approved (can't edit approved entries)
        if time_entry.approval_status == "approved":
            return HttpResponse('<div class="alert alert-warning">Cannot edit approved time entries</div>')

        # Get updated fields
        project_id = request.POST.get("project_id")
        work_type_id = request.POST.get("work_type_id")
        start_time = request.POST.get("start_time")
        duration_hours = request.POST.get("duration_hours")
        description = request.POST.get("description", "")

        # Validate and update fields
        updated_fields = []

        if project_id and project_id != str(time_entry.project.id):
            try:
                project = Project.objects.get(id=project_id)
                time_entry.project = project
                updated_fields.append("project")
            except Project.DoesNotExist:
                return HttpResponse('<div class="alert alert-danger">Invalid project</div>')

        if work_type_id and work_type_id != str(time_entry.work_type.id):
            try:
                work_type = WorkType.objects.get(id=work_type_id)
                time_entry.work_type = work_type
                updated_fields.append("work type")
            except WorkType.DoesNotExist:
                return HttpResponse('<div class="alert alert-danger">Invalid work type</div>')

        if start_time:
            try:
                start_datetime = timezone.datetime.fromisoformat(start_time)
                time_entry.start_time = start_datetime
                updated_fields.append("start time")
            except ValueError:
                return HttpResponse('<div class="alert alert-danger">Invalid start time format</div>')

        if duration_hours:
            try:
                duration_decimal = Decimal(str(duration_hours))
                if duration_decimal <= 0:
                    return HttpResponse('<div class="alert alert-danger">Duration must be positive</div>')

                time_entry.duration_hours = duration_decimal
                time_entry.duration_minutes = int(duration_decimal * 60)

                # Update end time based on new duration
                if time_entry.start_time:
                    time_entry.end_time = time_entry.start_time + timezone.timedelta(hours=float(duration_hours))

                updated_fields.append("duration")
            except (ValueError, TypeError):
                return HttpResponse('<div class="alert alert-danger">Invalid duration format</div>')

        if description != time_entry.description:
            time_entry.description = description
            updated_fields.append("description")

        # Save the changes
        time_entry.save()

        # Prepare success message
        if updated_fields:
            fields_text = ", ".join(updated_fields)
            success_msg = f"Time entry updated: {fields_text}"
        else:
            success_msg = "No changes made"

        # Return updated entry HTML fragment
        context = {"time_entry": time_entry}
        if request.htmx:
            return render(request, "financial/partials/time_entry_row.html", context)

        return HttpResponse(f'<div class="alert alert-success">{success_msg}</div>')

    except (ConnectionError, TimeoutError, HTTPError) as e:
        return HttpResponse(f'<div class="alert alert-danger">Error updating time entry: {e!s}</div>')


@login_required
@require_http_methods(["GET"])
def get_time_entry_row(request: HttpRequest) -> HttpResponse:
    """Get single time entry row for HTMX updates."""
    try:
        # Extract entry ID from URL path
        entry_id = request.resolver_match.kwargs.get("entry_id")
        if not entry_id:
            return HttpResponse('<div class="alert alert-warning">Entry ID required</div>', status=400)

        # Get the time entry (ensure user owns it)
        try:
            time_entry = TimeEntry.objects.get(id=entry_id, user=request.user)
        except TimeEntry.DoesNotExist:
            return HttpResponse('<div class="alert alert-danger">Time entry not found</div>', status=404)

        context = {"time_entry": time_entry}
        return render(request, "financial/partials/time_entry_row.html", context)

    except Exception:
        logger.exception(f"Error loading time entry row {entry_id} for user {request.user.username}")
        return HttpResponse('<div class="alert alert-danger">Error loading time entry</div>', status=500)


@login_required
@require_http_methods(["DELETE"])
def delete_time_entry_htmx(request: HttpRequest) -> HttpResponse:
    """Delete time entry with HTMX."""
    try:
        # Extract entry ID from URL path
        entry_id = request.resolver_match.kwargs.get("entry_id")
        if not entry_id:
            return HttpResponse('<div class="alert alert-warning">Entry ID required</div>', status=400)

        # Get the time entry (ensure user owns it)
        try:
            time_entry = TimeEntry.objects.get(id=entry_id, user=request.user)
        except TimeEntry.DoesNotExist:
            return HttpResponse('<div class="alert alert-danger">Time entry not found</div>', status=404)

        # Check if entry can be deleted
        if time_entry.approval_status == "approved":
            return HttpResponse(
                '<div class="alert alert-warning">Cannot delete approved time entries</div>',
                status=403,
            )

        # Delete the entry
        time_entry.delete()

        logger.info(f"Time entry {entry_id} deleted by {request.user.username}")

        # Return empty response (entry will be removed from DOM)
        response = HttpResponse("")
        response["HX-Trigger"] = "time-entry-deleted"
        return response

    except Exception:
        logger.exception(f"Error deleting time entry {entry_id} for user {request.user.username}")
        return HttpResponse(
            '<div class="alert alert-danger">Error deleting time entry</div>',
            status=500,
        )


@login_required
@require_http_methods(["POST"])
def duplicate_time_entry_htmx(request: HttpRequest) -> HttpResponse:
    """Duplicate time entry with HTMX."""
    try:
        # Extract entry ID from URL path
        entry_id = request.resolver_match.kwargs.get("entry_id")
        if not entry_id:
            return HttpResponse('<div class="alert alert-warning">Entry ID required</div>', status=400)

        # Get the time entry (ensure user owns it)
        try:
            original_entry = TimeEntry.objects.get(id=entry_id, user=request.user)
        except TimeEntry.DoesNotExist:
            return HttpResponse('<div class="alert alert-danger">Time entry not found</div>', status=404)

        # Create duplicate with current timestamp
        duplicate_entry = TimeEntry.objects.create(
            user=request.user,
            project=original_entry.project,
            task=original_entry.task,
            work_type=original_entry.work_type,
            start_time=timezone.now(),
            duration_hours=original_entry.duration_hours,
            description=f"Copy of: {original_entry.description}",
            is_billable=original_entry.is_billable,
            hourly_rate=original_entry.hourly_rate,
            approval_status="pending",
            billing_status="unbilled",
        )

        logger.info(f"Time entry {entry_id} duplicated as {duplicate_entry.id} by {request.user.username}")

        context = {
            "time_entry": duplicate_entry,
            "success_message": "Time entry duplicated successfully",
        }

        response = render(request, "financial/partials/time_entry_row.html", context)
        response["HX-Trigger"] = "time-entry-duplicated"
        return response

    except Exception:
        logger.exception(f"Error duplicating time entry {entry_id} for user {request.user.username}")
        return HttpResponse(
            '<div class="alert alert-danger">Error duplicating time entry</div>',
            status=500,
        )


@login_required
@require_http_methods(["PATCH"])
def approve_time_entry_htmx(request: HttpRequest) -> HttpResponse:
    """Approve time entry with HTMX (staff only)."""
    try:
        if not request.user.is_staff:
            return HttpResponse('<div class="alert alert-danger">Permission denied</div>', status=403)

        # Extract entry ID from URL path
        entry_id = request.resolver_match.kwargs.get("entry_id")
        if not entry_id:
            return HttpResponse('<div class="alert alert-warning">Entry ID required</div>', status=400)

        # Get the time entry
        try:
            time_entry = TimeEntry.objects.get(id=entry_id)
        except TimeEntry.DoesNotExist:
            return HttpResponse('<div class="alert alert-danger">Time entry not found</div>', status=404)

        # Check if entry can be approved
        if time_entry.approval_status == "approved":
            return HttpResponse(
                '<div class="alert alert-warning">Time entry already approved</div>',
                status=400,
            )

        # Approve the entry
        time_entry.approval_status = "approved"
        time_entry.approved_by = request.user
        time_entry.approved_at = timezone.now()
        time_entry.save()

        logger.info(f"Time entry {entry_id} approved by {request.user.username}")

        context = {
            "time_entry": time_entry,
            "success_message": "Time entry approved successfully",
        }

        response = render(request, "financial/partials/time_entry_row.html", context)
        response["HX-Trigger"] = "time-entry-approved"
        return response

    except Exception:
        logger.exception(f"Error approving time entry {entry_id} for user {request.user.username}")
        return HttpResponse(
            '<div class="alert alert-danger">Error approving time entry</div>',
            status=500,
        )


@login_required
@require_http_methods(["PATCH"])
def reject_time_entry_htmx(request: HttpRequest) -> HttpResponse:
    """Reject time entry with HTMX (staff only)."""
    try:
        if not request.user.is_staff:
            return HttpResponse('<div class="alert alert-danger">Permission denied</div>', status=403)

        # Extract entry ID from URL path
        entry_id = request.resolver_match.kwargs.get("entry_id")
        if not entry_id:
            return HttpResponse('<div class="alert alert-warning">Entry ID required</div>', status=400)

        # Get the time entry
        try:
            time_entry = TimeEntry.objects.get(id=entry_id)
        except TimeEntry.DoesNotExist:
            return HttpResponse('<div class="alert alert-danger">Time entry not found</div>', status=404)

        # Check if entry can be rejected
        if time_entry.approval_status == "approved":
            return HttpResponse(
                '<div class="alert alert-warning">Cannot reject approved time entry</div>',
                status=400,
            )

        # Reject the entry
        time_entry.approval_status = "rejected"
        time_entry.approved_by = request.user
        time_entry.approved_at = timezone.now()
        time_entry.save()

        logger.info(f"Time entry {entry_id} rejected by {request.user.username}")

        context = {"time_entry": time_entry, "success_message": "Time entry rejected"}

        response = render(request, "financial/partials/time_entry_row.html", context)
        response["HX-Trigger"] = "time-entry-rejected"
        return response

    except Exception:
        logger.exception(f"Error rejecting time entry {entry_id} for user {request.user.username}")
        return HttpResponse(
            '<div class="alert alert-danger">Error rejecting time entry</div>',
            status=500,
        )


@login_required
@require_http_methods(["GET"])
def export_timesheet(request: HttpRequest) -> HttpResponse:
    """Export timesheet with multiple formats and custom filtering."""
    import csv
    import io
    from datetime import datetime, timedelta

    from django.http import JsonResponse
    from django.utils import timezone

    from apps.financial.models import TimeEntry

    try:
        # Get export parameters
        export_format = request.GET.get("format", "csv")
        period_start = request.GET.get("period_start")
        period_end = request.GET.get("period_end")
        include_non_billable = request.GET.get("include_non_billable", "true") == "true"

        # Default to current week if no period specified
        if not period_start or not period_end:
            today = timezone.now().date()
            start_date = today - timedelta(days=today.weekday())
            end_date = start_date + timedelta(days=6)
        else:
            start_date = datetime.strptime(period_start, "%Y-%m-%d").date()
            end_date = datetime.strptime(period_end, "%Y-%m-%d").date()

        # Get time entries
        time_entries = (
            TimeEntry.objects.filter(user=request.user, start_time__date__range=[start_date, end_date])
            .select_related("project", "work_type", "task")
            .order_by("start_time")
        )

        # Filter out non-billable if requested
        if not include_non_billable:
            time_entries = time_entries.filter(work_type__is_billable=True)

        if export_format == "json":
            # JSON export
            data = []
            for entry in time_entries:
                data.append(
                    {
                        "id": str(entry.id),
                        "date": entry.start_time.date().isoformat(),
                        "start_time": entry.start_time.isoformat(),
                        "end_time": (entry.end_time.isoformat() if entry.end_time else None),
                        "duration_hours": float(entry.duration_hours or 0),
                        "project": entry.project.name,
                        "work_type": entry.work_type.name,
                        "task": entry.task.name if entry.task else "",
                        "description": entry.description,
                        "is_billable": entry.work_type.is_billable,
                        "approval_status": entry.approval_status,
                    }
                )

            response_data = {
                "period_start": start_date.isoformat(),
                "period_end": end_date.isoformat(),
                "total_entries": len(data),
                "total_hours": sum(entry["duration_hours"] for entry in data),
                "billable_hours": sum(entry["duration_hours"] for entry in data if entry["is_billable"]),
                "entries": data,
            }

            return JsonResponse(response_data)

        if export_format == "csv":
            # CSV export
            output = io.StringIO()
            writer = csv.writer(output)

            # Write header
            writer.writerow(
                [
                    "Date",
                    "Start Time",
                    "End Time",
                    "Duration (Hours)",
                    "Project",
                    "Work Type",
                    "Task",
                    "Description",
                    "Billable",
                    "Status",
                ]
            )

            # Write data rows
            for entry in time_entries:
                writer.writerow(
                    [
                        entry.start_time.date(),
                        entry.start_time.time(),
                        entry.end_time.time() if entry.end_time else "",
                        entry.duration_hours or 0,
                        entry.project.name,
                        entry.work_type.name,
                        entry.task.name if entry.task else "",
                        entry.description,
                        "Yes" if entry.work_type.is_billable else "No",
                        entry.approval_status.title(),
                    ]
                )

            # Prepare response
            output.seek(0)
            response = HttpResponse(output.getvalue(), content_type="text/csv")
            filename = f"timesheet_{start_date}_{end_date}.csv"
            response["Content-Disposition"] = f'attachment; filename="{filename}"'
            return response

        # Unsupported format
        return HttpResponse('<div class="alert alert-warning">Unsupported export format. Use "csv" or "json".</div>')

    except (FileNotFoundError, PermissionError, OSError) as e:
        return HttpResponse(f'<div class="alert alert-danger">Error exporting timesheet: {e!s}</div>')
