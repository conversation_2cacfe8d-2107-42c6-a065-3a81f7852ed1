"""
Financial admin views for department managers.

This module provides financial oversight capabilities for department managers
including payroll management, project costs, and financial reporting with
Django 5.2 patterns and enhanced security.
"""

import csv
import logging
from datetime import datetime, timedelta
from decimal import Decimal

try:
    from requests.exceptions import HTTPError, ConnectionError, RequestException
except ImportError:
    # Fallback for development environments
    class HTTPError(Exception):
        pass
    class ConnectionError(Exception):
        pass
    class RequestException(Exception):
        pass

try:
    from urllib3.exceptions import TimeoutError
except ImportError:
    # Fallback for development environments
    class TimeoutError(Exception):
        pass

from django.contrib import messages
from django.contrib.auth import get_user_model
from django.contrib.auth.decorators import login_required
from django.core.paginator import Paginator
from django.db.models import Avg, Count, F, Q, Sum
from django.http import HttpRequest, HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.views.decorators.http import require_http_methods

from apps.financial.models import Invoice, TimeEntry, TimesheetPeriod
from apps.projects.models import Project

logger = logging.getLogger(__name__)
User = get_user_model()


def require_department_manager(view_func):
    """
    Decorator to ensure only department managers can access financial admin views.

    Updated for Django 5.2 with new role system and organization context.
    """
    from functools import wraps

    @wraps(view_func)
    def wrapper(request: HttpRequest, *args, **kwargs):
        if not request.user.is_authenticated:
            from django.contrib.auth.views import redirect_to_login

            return redirect_to_login(request.get_full_path())

        # Check if user has organization
        if not hasattr(request.user, "organization") or not request.user.organization:
            return render(
                request,
                "shared/403.html",
                {"message": _("Organization membership required for financial administration")},
                status=403,
            )

        # Check for department manager role or higher
        user_role = request.user.user_roles.filter(organization=request.user.organization).first()

        if not user_role or user_role.role.level > 30:  # Department Manager level or higher
            return render(
                request,
                "shared/403.html",
                {"message": _("Department Manager access required for financial administration")},
                status=403,
            )

        return view_func(request, *args, **kwargs)

    return wrapper


@login_required
@require_department_manager
def financial_admin_dashboard(request: HttpRequest) -> HttpResponse:
    """
    Main financial KPI dashboard for department managers.

    Displays comprehensive financial metrics including revenue, costs,
    payroll statistics, and project financial health indicators.
    """
    # Get current month date range
    today = timezone.now().date()
    month_start = today.replace(day=1)

    # Get organization-filtered data
    organization = request.user.organization

    # Calculate current month financial metrics
    current_month_entries = TimeEntry.objects.filter(
        start_time__date__gte=month_start,
        project__organization=organization,
    )

    # Revenue from billable hours
    current_month_revenue = current_month_entries.filter(is_billable=True, billing_status="billed").aggregate(
        total_revenue=Sum(F("duration_hours") * F("hourly_rate"))
    )["total_revenue"] or Decimal("0.00")

    # Total costs (all time entries)
    current_month_costs = current_month_entries.aggregate(total_costs=Sum(F("duration_hours") * F("hourly_rate")))[
        "total_costs"
    ] or Decimal("0.00")

    # Profit margin calculation
    profit_margin = Decimal("0.00")
    if current_month_revenue > 0:
        profit = current_month_revenue - current_month_costs
        profit_margin = (profit / current_month_revenue * 100).quantize(Decimal("0.01"))

    # Payroll statistics
    timesheet_stats = TimesheetPeriod.objects.filter(organization=organization, start_date__gte=month_start).aggregate(
        total_employees=Count("entries__user", distinct=True),
        submitted_timesheets=Count("entries", filter=Q(entries__status="submitted")),
        approved_timesheets=Count("entries", filter=Q(entries__status="approved")),
        pending_timesheets=Count("entries", filter=Q(entries__status="pending")),
    )

    # Project financial health
    project_stats = (
        Project.objects.filter(organization=organization)
        .annotate(
            total_time_cost=Sum(F("financial_time_entries__duration_hours") * F("financial_time_entries__hourly_rate")),
            total_hours=Sum("financial_time_entries__duration_hours"),
        )
        .aggregate(
            active_projects=Count("id", filter=Q(status="active")),
            over_budget_projects=Count("id", filter=Q(current_cost__gt=F("contract_amount"))),
            at_risk_projects=Count(
                "id",
                filter=Q(
                    current_cost__gte=F("contract_amount") * Decimal("0.8"),
                    current_cost__lt=F("contract_amount"),
                ),
            ),
        )
    )

    # Recent invoices
    recent_invoices = (
        Invoice.objects.filter(project__organization=organization)
        .select_related("project")
        .order_by("-invoice_date")[:5]
    )

    # Overdue invoices
    today = timezone.now().date()
    overdue_invoices = Invoice.objects.filter(
        project__organization=organization, status="sent", due_date__lt=today
    ).aggregate(count=Count("id"), total_amount=Sum("total_amount"))

    context = {
        "page_title": _("Financial Administration Dashboard"),
        "current_month_revenue": current_month_revenue,
        "current_month_costs": current_month_costs,
        "profit_margin": profit_margin,
        "timesheet_stats": timesheet_stats,
        "project_stats": project_stats,
        "recent_invoices": recent_invoices,
        "overdue_invoices": overdue_invoices,
        "month_start": month_start,
    }

    return render(request, "financial/admin/dashboard.html", context)


@login_required
@require_department_manager
def payroll_management(request: HttpRequest) -> HttpResponse:
    """
    Comprehensive payroll management interface for department managers.

    Provides timesheet filtering, approval workflow, and employee
    time tracking analytics with organization isolation.
    """
    organization = request.user.organization

    # Get filter parameters
    period_filter = request.GET.get("period", "current_month")
    user_filter = request.GET.get("user")
    status_filter = request.GET.get("status", "all")

    # Calculate date range based on period filter
    today = timezone.now().date()

    if period_filter == "current_month":
        start_date = today.replace(day=1)
        end_date = today
    elif period_filter == "last_month":
        if today.month == 1:
            start_date = today.replace(year=today.year - 1, month=12, day=1)
            end_date = today.replace(day=1) - timedelta(days=1)
        else:
            start_date = today.replace(month=today.month - 1, day=1)
            end_date = today.replace(day=1) - timedelta(days=1)
    elif period_filter == "current_quarter":
        quarter_start_month = ((today.month - 1) // 3) * 3 + 1
        start_date = today.replace(month=quarter_start_month, day=1)
        end_date = today
    else:  # Default to current month
        start_date = today.replace(day=1)
        end_date = today

    # Override with custom date range if provided
    if request.GET.get("start_date"):
        try:
            start_date = datetime.strptime(request.GET["start_date"], "%Y-%m-%d").date()
        except ValueError:
            messages.warning(request, _("Invalid start date format. Using default."))

    if request.GET.get("end_date"):
        try:
            end_date = datetime.strptime(request.GET["end_date"], "%Y-%m-%d").date()
        except ValueError:
            messages.warning(request, _("Invalid end date format. Using default."))

    # Get timesheet periods for the date range
    timesheet_periods = (
        TimesheetPeriod.objects.filter(
            organization=organization,
            start_date__gte=start_date,
            end_date__lte=end_date,
        )
        .select_related("locked_by")
        .prefetch_related("entries__user")
    )

    # Filter timesheets based on criteria
    timesheets_query = TimeEntry.objects.filter(
        project__organization=organization,
        start_time__date__gte=start_date,
        start_time__date__lte=end_date,
    ).select_related("user", "project", "work_type", "approved_by")

    if user_filter:
        timesheets_query = timesheets_query.filter(user_id=user_filter)

    if status_filter != "all":
        timesheets_query = timesheets_query.filter(approval_status=status_filter)

    # Paginate results
    paginator = Paginator(timesheets_query.order_by("-start_time"), 25)
    page_number = request.GET.get("page")
    timesheets = paginator.get_page(page_number)

    # Calculate statistics
    employee_stats = timesheets_query.aggregate(
        total_employees=Count("user", distinct=True),
        total_hours=Sum("duration_hours"),
        billable_hours=Sum("duration_hours", filter=Q(is_billable=True)),
        total_amount=Sum(F("duration_hours") * F("hourly_rate")),
    )

    # Status distribution
    status_distribution = (
        timesheets_query.values("approval_status").annotate(count=Count("id")).order_by("approval_status")
    )

    # Get all users in organization for filter dropdown
    organization_users = User.objects.filter(organization=organization).order_by("last_name", "first_name")

    context = {
        "page_title": _("Payroll Management"),
        "timesheets": timesheets,
        "timesheet_periods": timesheet_periods,
        "employee_stats": employee_stats,
        "status_distribution": status_distribution,
        "organization_users": organization_users,
        "period_filter": period_filter,
        "user_filter": user_filter,
        "status_filter": status_filter,
        "start_date": start_date,
        "end_date": end_date,
    }

    # Return HTMX partial if requested
    if request.headers.get("HX-Request"):
        return render(request, "financial/admin/partials/payroll_table.html", context)

    return render(request, "financial/admin/payroll_management.html", context)


@login_required
@require_department_manager
def project_costs(request: HttpRequest) -> HttpResponse:
    """
    Project cost analysis and budget monitoring for department managers.

    Provides comprehensive project financial analysis including budget
    utilization, cost tracking, and performance metrics.
    """
    organization = request.user.organization

    # Get filter parameters
    status_filter = request.GET.get("status", "all")
    budget_filter = request.GET.get("budget", "all")

    # Base project queryset with cost calculations
    projects = (
        Project.objects.filter(organization=organization)
        .annotate(
            total_time_cost=Sum(F("financial_time_entries__duration_hours") * F("financial_time_entries__hourly_rate")),
            total_hours=Sum("financial_time_entries__duration_hours"),
            billable_hours=Sum(
                "financial_time_entries__duration_hours",
                filter=Q(financial_time_entries__is_billable=True),
            ),
            budget_utilization=F("current_cost") / F("contract_amount") * 100,
        )
        .select_related("organization")
    )

    # Apply status filter
    if status_filter != "all":
        projects = projects.filter(status=status_filter)

    # Apply budget filter
    if budget_filter == "over_budget":
        projects = projects.filter(current_cost__gt=F("contract_amount"))
    elif budget_filter == "at_risk":
        projects = projects.filter(
            current_cost__gte=F("contract_amount") * Decimal("0.8"),
            current_cost__lt=F("contract_amount"),
        )
    elif budget_filter == "under_budget":
        projects = projects.filter(current_cost__lt=F("contract_amount") * Decimal("0.8"))

    # Order by budget utilization
    projects = projects.order_by("-budget_utilization")

    # Paginate results
    paginator = Paginator(projects, 20)
    page_number = request.GET.get("page")
    projects_page = paginator.get_page(page_number)

    # Calculate summary statistics
    summary_stats = projects.aggregate(
        total_projects=Count("id"),
        total_budget=Sum("contract_amount"),
        total_spent=Sum("current_cost"),
        avg_utilization=Avg("budget_utilization"),
        over_budget_count=Count("id", filter=Q(current_cost__gt=F("contract_amount"))),
        at_risk_count=Count(
            "id",
            filter=Q(
                current_cost__gte=F("contract_amount") * Decimal("0.8"),
                current_cost__lt=F("contract_amount"),
            ),
        ),
    )

    # Cost distribution by category (if available)
    cost_distribution = (
        projects.values("category")
        .annotate(
            project_count=Count("id"),
            total_cost=Sum("current_cost"),
            avg_budget_utilization=Avg("budget_utilization"),
        )
        .order_by("-total_cost")
    )

    context = {
        "page_title": _("Project Cost Analysis"),
        "projects": projects_page,
        "summary_stats": summary_stats,
        "cost_distribution": cost_distribution,
        "status_filter": status_filter,
        "budget_filter": budget_filter,
    }

    # Return HTMX partial if requested
    if request.headers.get("HX-Request"):
        return render(request, "financial/admin/partials/project_costs_table.html", context)

    return render(request, "financial/admin/project_costs.html", context)


@login_required
@require_department_manager
@require_http_methods(["POST"])
def approve_timesheet(request: HttpRequest, timesheet_id: str) -> HttpResponse:
    """
    HTMX-enabled timesheet approval with comprehensive audit logging.

    Approves a timesheet entry and updates the UI dynamically
    with proper validation and organization isolation.
    """
    try:
        timesheet = get_object_or_404(TimeEntry, id=timesheet_id, project__organization=request.user.organization)

        # Validate current status
        if timesheet.approval_status == "approved":
            return HttpResponse(
                _("Timesheet is already approved"),
                status=400,
                headers={"HX-Trigger": "timesheet-error"},
            )

        # Update timesheet status
        timesheet.approval_status = "approved"
        timesheet.approved_by = request.user
        timesheet.approved_at = timezone.now()
        timesheet.save(update_fields=["approval_status", "approved_by", "approved_at"])

        # Log the approval
        logger.info(f"Timesheet {timesheet_id} approved by {request.user.username} for user {timesheet.user.username}")

        messages.success(
            request,
            _("Timesheet approved successfully for %(user)s") % {"user": timesheet.user.get_full_name()},
        )

        # Return updated row for HTMX
        context = {"timesheet": timesheet}
        response = render(request, "financial/admin/partials/timesheet_row.html", context)
        response["HX-Trigger"] = "timesheet-approved"
        return response

    except (ConnectionError, TimeoutError, HTTPError) as e:
        logger.error(f"Error approving timesheet {timesheet_id}: {e}")
        return HttpResponse(
            _("Error approving timesheet: %(error)s") % {"error": str(e)},
            status=500,
            headers={"HX-Trigger": "timesheet-error"},
        )


@login_required
@require_department_manager
@require_http_methods(["POST"])
def reject_timesheet(request: HttpRequest, timesheet_id: str) -> HttpResponse:
    """
    HTMX-enabled timesheet rejection with reason tracking.

    Rejects a timesheet entry with mandatory reason and updates
    the UI dynamically with comprehensive audit logging.
    """
    try:
        timesheet = get_object_or_404(TimeEntry, id=timesheet_id, project__organization=request.user.organization)

        # Get rejection reason
        rejection_reason = request.POST.get("reason", "").strip()
        if not rejection_reason:
            return HttpResponse(
                _("Rejection reason is required"),
                status=400,
                headers={"HX-Trigger": "timesheet-error"},
            )

        # Update timesheet status
        timesheet.approval_status = "rejected"
        timesheet.approved_by = request.user
        timesheet.approved_at = timezone.now()
        # Store rejection reason in notes if field exists
        if hasattr(timesheet, "rejection_reason"):
            timesheet.rejection_reason = rejection_reason
        else:
            # Append to notes if no dedicated field
            current_notes = timesheet.notes or ""
            timesheet.notes = f"{current_notes}\nRejected: {rejection_reason}".strip()

        timesheet.save()

        # Log the rejection
        logger.info(
            f"Timesheet {timesheet_id} rejected by {request.user.username} "
            f"for user {timesheet.user.username}. Reason: {rejection_reason}"
        )

        messages.warning(
            request,
            _("Timesheet rejected for %(user)s. Reason: %(reason)s")
            % {"user": timesheet.user.get_full_name(), "reason": rejection_reason},
        )

        # Return updated row for HTMX
        context = {"timesheet": timesheet}
        response = render(request, "financial/admin/partials/timesheet_row.html", context)
        response["HX-Trigger"] = "timesheet-rejected"
        return response

    except (ConnectionError, TimeoutError, HTTPError) as e:
        logger.error(f"Error rejecting timesheet {timesheet_id}: {e}")
        return HttpResponse(
            _("Error rejecting timesheet: %(error)s") % {"error": str(e)},
            status=500,
            headers={"HX-Trigger": "timesheet-error"},
        )


@login_required
@require_department_manager
def export_payroll_data(request: HttpRequest) -> HttpResponse:
    """
    CSV export of comprehensive payroll data for department managers.

    Exports employee timesheet data with filtering options and
    organization-based access control.
    """
    organization = request.user.organization

    # Get date range parameters
    start_date = request.GET.get("start_date")
    end_date = request.GET.get("end_date")

    if not start_date or not end_date:
        # Default to current month
        today = timezone.now().date()
        start_date = today.replace(day=1)
        end_date = today
    else:
        try:
            start_date = datetime.strptime(start_date, "%Y-%m-%d").date()
            end_date = datetime.strptime(end_date, "%Y-%m-%d").date()
        except ValueError:
            messages.error(request, _("Invalid date format. Please use YYYY-MM-DD."))
            return redirect("financial:payroll_management")

    # Get timesheet data
    timesheets = (
        TimeEntry.objects.filter(
            project__organization=organization,
            start_time__date__gte=start_date,
            start_time__date__lte=end_date,
        )
        .select_related("user", "project", "work_type")
        .order_by("user__last_name", "start_time")
    )

    # Create CSV response
    response = HttpResponse(content_type="text/csv")
    filename = f"payroll_data_{start_date}_to_{end_date}.csv"
    response["Content-Disposition"] = f'attachment; filename="{filename}"'

    writer = csv.writer(response)

    # Write header
    writer.writerow(
        [
            _("Employee"),
            _("Project"),
            _("Work Type"),
            _("Date"),
            _("Hours"),
            _("Rate"),
            _("Amount"),
            _("Billable"),
            _("Status"),
            _("Approved By"),
            _("Approved Date"),
        ]
    )

    # Write data rows
    for timesheet in timesheets:
        writer.writerow(
            [
                timesheet.user.get_full_name(),
                timesheet.project.name,
                timesheet.work_type.name,
                timesheet.start_time.date(),
                timesheet.duration_hours,
                timesheet.hourly_rate,
                timesheet.duration_hours * timesheet.hourly_rate,
                _("Yes") if timesheet.is_billable else _("No"),
                (
                    timesheet.get_approval_status_display()
                    if hasattr(timesheet, "get_approval_status_display")
                    else timesheet.approval_status
                ),
                timesheet.approved_by.get_full_name() if timesheet.approved_by else "",
                (timesheet.approved_at.strftime("%Y-%m-%d %H:%M") if timesheet.approved_at else ""),
            ]
        )

    return response


@login_required
@require_department_manager
def export_project_costs(request: HttpRequest) -> HttpResponse:
    """
    CSV export of project financial data for department managers.

    Exports comprehensive project cost analysis with budget
    utilization and organization-based filtering.
    """
    organization = request.user.organization

    # Get projects with cost calculations
    projects = (
        Project.objects.filter(organization=organization)
        .annotate(
            total_time_cost=Sum(F("financial_time_entries__duration_hours") * F("financial_time_entries__hourly_rate")),
            total_hours=Sum("financial_time_entries__duration_hours"),
            billable_hours=Sum(
                "financial_time_entries__duration_hours",
                filter=Q(financial_time_entries__is_billable=True),
            ),
        )
        .order_by("name")
    )

    # Create CSV response
    response = HttpResponse(content_type="text/csv")
    timestamp = timezone.now().strftime("%Y%m%d_%H%M%S")
    filename = f"project_costs_{timestamp}.csv"
    response["Content-Disposition"] = f'attachment; filename="{filename}"'

    writer = csv.writer(response)

    # Write header
    writer.writerow(
        [
            _("Project Name"),
            _("Status"),
            _("Contract Amount"),
            _("Current Cost"),
            _("Budget Utilization %"),
            _("Total Hours"),
            _("Billable Hours"),
            _("Average Rate"),
            _("Remaining Budget"),
            _("Budget Status"),
        ]
    )

    # Write data rows
    for project in projects:
        contract_amount = getattr(project, "contract_amount", Decimal("0.00")) or Decimal("0.00")
        current_cost = getattr(project, "current_cost", Decimal("0.00")) or Decimal("0.00")

        # Calculate budget utilization
        budget_utilization = Decimal("0.00")
        if contract_amount > 0:
            budget_utilization = (current_cost / contract_amount * 100).quantize(Decimal("0.01"))

        # Determine budget status
        if current_cost > contract_amount:
            budget_status = _("Over Budget")
        elif current_cost >= contract_amount * Decimal("0.8"):
            budget_status = _("At Risk")
        else:
            budget_status = _("On Track")

        # Calculate average rate
        total_hours = project.total_hours or Decimal("0.00")
        avg_rate = Decimal("0.00")
        if total_hours > 0 and project.total_time_cost:
            avg_rate = (project.total_time_cost / total_hours).quantize(Decimal("0.01"))

        writer.writerow(
            [
                project.name,
                (project.get_status_display() if hasattr(project, "get_status_display") else project.status),
                contract_amount,
                current_cost,
                budget_utilization,
                total_hours or 0,
                project.billable_hours or 0,
                avg_rate,
                contract_amount - current_cost,
                budget_status,
            ]
        )

    return response


@login_required
@require_department_manager
def financial_statistics(request: HttpRequest) -> HttpResponse:
    """
    Real-time financial metrics for HTMX dashboard updates.

    Provides live financial statistics with organization isolation
    and dual response format support (HTMX/JSON).
    """
    organization = request.user.organization

    # Get current month date range
    today = timezone.now().date()
    month_start = today.replace(day=1)

    # Calculate financial metrics
    monthly_stats = TimeEntry.objects.filter(
        project__organization=organization, start_time__date__gte=month_start
    ).aggregate(
        total_revenue=Sum(
            F("duration_hours") * F("hourly_rate"),
            filter=Q(is_billable=True, billing_status="billed"),
        ),
        total_costs=Sum(F("duration_hours") * F("hourly_rate")),
        total_hours=Sum("duration_hours"),
        billable_hours=Sum("duration_hours", filter=Q(is_billable=True)),
    )

    # Invoice statistics
    invoice_stats = Invoice.objects.filter(project__organization=organization).aggregate(
        pending_count=Count("id", filter=Q(status="pending")),
        overdue_count=Count("id", filter=Q(status="overdue")),
        pending_amount=Sum("total_amount", filter=Q(status="pending")),
        overdue_amount=Sum("total_amount", filter=Q(status="overdue")),
    )

    # Timesheet statistics
    timesheet_stats = TimeEntry.objects.filter(
        project__organization=organization, start_time__date__gte=month_start
    ).aggregate(
        pending_approval=Count("id", filter=Q(approval_status="pending")),
        approved_count=Count("id", filter=Q(approval_status="approved")),
        rejected_count=Count("id", filter=Q(approval_status="rejected")),
    )

    # Combine all statistics
    stats = {
        "monthly_revenue": monthly_stats["total_revenue"] or Decimal("0.00"),
        "monthly_costs": monthly_stats["total_costs"] or Decimal("0.00"),
        "total_hours": monthly_stats["total_hours"] or 0,
        "billable_hours": monthly_stats["billable_hours"] or 0,
        "pending_invoices": invoice_stats["pending_count"] or 0,
        "overdue_invoices": invoice_stats["overdue_count"] or 0,
        "pending_invoice_amount": invoice_stats["pending_amount"] or Decimal("0.00"),
        "overdue_invoice_amount": invoice_stats["overdue_amount"] or Decimal("0.00"),
        "pending_timesheets": timesheet_stats["pending_approval"] or 0,
        "approved_timesheets": timesheet_stats["approved_count"] or 0,
        "rejected_timesheets": timesheet_stats["rejected_count"] or 0,
    }

    # Return appropriate response format
    if request.headers.get("HX-Request"):
        context = {"stats": stats}
        return render(request, "financial/admin/partials/financial_stats.html", context)
    # Convert Decimal values to strings for JSON serialization
    json_stats = {key: str(value) if isinstance(value, Decimal) else value for key, value in stats.items()}
    return JsonResponse({"stats": json_stats})