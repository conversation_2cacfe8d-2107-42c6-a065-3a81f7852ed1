"""
Enhanced Spatial Collaboration Service for CLEAR

This service provides comprehensive collaborative spatial features including
real-time annotation sharing, collaborative drawing, and spatial communication.

Features:
- Real-time collaborative spatial annotations
- Shared drawing sessions with conflict resolution
- Spatial communication and commenting
- Permission-based collaboration controls
- Live presence tracking for spatial sessions
- Integration with spatial bookmarking system
"""

from __future__ import annotations

import json
import logging
import re
import time
from dataclasses import dataclass
from datetime import datetime
from typing import Any, List, Optional

try:
    from requests.exceptions import HTTPError, ConnectionError, RequestException
except ImportError:
    # Fallback for development environments
    class HTTPError(Exception):
        pass
    class ConnectionError(Exception):
        pass
    class RequestException(Exception):
        pass

try:
    from urllib3.exceptions import TimeoutError
except ImportError:
    # Fallback for development environments
    class TimeoutError(Exception):
        pass

from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.core.exceptions import ValidationError
from django.db import models
from django.utils import timezone
from rest_framework import permissions, status

User = get_user_model()
logger = logging.getLogger(__name__)

import json
import logging
from dataclasses import dataclass
from typing import TYPE_CHECKING, Any

if TYPE_CHECKING:
    from datetime import datetime

    from django.contrib.auth.models import AbstractUser

from django.contrib.auth import get_user_model
from django.contrib.gis.geos import GEOSGeometry
from django.core.cache import cache
from django.utils import timezone

from asgiref.sync import async_to_sync
from channels.layers import get_channel_layer

from apps.infrastructure.models import (
    CollaborationSession,
    CollaborativeDrawing,
    SpatialAnnotation,
)

User = get_user_model()
logger = logging.getLogger(__name__)


@dataclass
class AnnotationData:
    """Spatial annotation data structure"""

    text: str
    annotation_type: str
    geometry: dict[str, Any]
    color: str = "#ff0000"
    is_persistent: bool = True
    metadata: dict[str, Any] | None = None


@dataclass
class DrawingData:
    """Collaborative drawing data structure"""

    drawing_type: str
    geometry: dict[str, Any]
    style: dict[str, Any]
    is_temporary: bool = False


@dataclass
class CollaborationEvent:
    """Collaboration event for real-time updates"""

    event_type: str  # 'annotation_added', 'drawing_updated', 'user_joined', etc.
    user_id: int
    user_name: str
    data: dict[str, Any]
    timestamp: datetime
    session_id: str | None = None


class SpatialCollaborationService:
    """Enhanced spatial collaboration service.

    Provides comprehensive real-time spatial collaboration features including
    session management, annotation sharing, drawing collaboration, and presence tracking.
    """

    CACHE_PREFIX = "spatial_collab"
    CACHE_TIMEOUT = 3600  # 1 hour
    PRESENCE_TIMEOUT = 30  # 30 seconds for presence tracking

    # WebSocket channel groups
    CHANNEL_GROUP_PREFIX = "spatial_collab"

    def __init__(self, user: AbstractUser, project: Any | None = None) -> None:
        """Initialize spatial collaboration service.

        Args:
        ----
            user: User for the collaboration service
            project: Optional project context

        """
        self.user = user
        self.project = project
        self.cache_key_prefix = f"{self.CACHE_PREFIX}:{user.id}"
        if project:
            self.cache_key_prefix += f":{project.id}"

        self.channel_layer = get_channel_layer()

    # ========== COLLABORATION SESSIONS ==========

    def create_collaboration_session(
        self,
        session_name: str,
        description: str = "",
        is_public: bool = False,
        max_participants: int = 10,
    ) -> dict[str, Any]:
        """Create a new collaboration session.

        Args:
        ----
            session_name: Name for the session
            description: Optional description
            is_public: Whether session is public or private
            max_participants: Maximum number of participants

        Returns:
        -------
            Session creation result with status and details

        """
        try:
            session = CollaborationSession.objects.create(
                project=self.project,
                created_by=self.user,
                session_name=session_name,
                description=description,
                is_public=is_public,
                max_participants=max_participants,
            )

            # Add creator as participant
            session.participants.add(self.user)

            # Create WebSocket channel group
            group_name = f"{self.CHANNEL_GROUP_PREFIX}_{session.id}"

            # Broadcast session creation
            self._broadcast_event(
                group_name,
                CollaborationEvent(
                    event_type="session_created",
                    user_id=self.user.id,
                    user_name=self.user.username,
                    data={
                        "session_id": str(session.id),
                        "session_name": session_name,
                        "created_by": self.user.username,
                    },
                    timestamp=timezone.now(),
                ),
            )

            return {
                "success": True,
                "session_id": str(session.id),
                "session": self._serialize_session(session),
                "message": f'Collaboration session "{session_name}" created successfully',
            }

        except (AttributeError, KeyError, ValueError, TypeError) as e:
            logger.error(f"Error creating collaboration session: {e!s}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to create collaboration session",
            }

    def join_collaboration_session(self, session_id: str) -> dict[str, Any]:
        """Join an existing collaboration session.

        Args:
        ----
            session_id: Collaboration session identifier

        Returns:
        -------
            Join result with session details

        """
        try:
            session = CollaborationSession.objects.get(id=session_id, is_active=True)

            # Check if session is full
            current_participants = session.participants.count()
            if current_participants >= session.max_participants:
                return {
                    "success": False,
                    "error": "Session is full",
                    "message": f"Session has reached maximum participants ({session.max_participants})",
                }

            # Check permissions for private sessions
            if not session.is_public and session.created_by != self.user:
                if not session.participants.filter(id=self.user.id).exists():
                    return {
                        "success": False,
                        "error": "Permission denied",
                        "message": "You do not have permission to join this private session",
                    }

            # Add user to session
            session.participants.add(self.user)

            # Update presence
            self._update_user_presence(session_id, True)

            # Broadcast join event
            group_name = f"{self.CHANNEL_GROUP_PREFIX}_{session_id}"
            self._broadcast_event(
                group_name,
                CollaborationEvent(
                    event_type="user_joined",
                    user_id=self.user.id,
                    user_name=self.user.username,
                    data={
                        "session_id": session_id,
                        "participant_count": session.participants.count(),
                    },
                    timestamp=timezone.now(),
                    session_id=session_id,
                ),
            )

            return {
                "success": True,
                "session": self._serialize_session(session),
                "message": f'Joined collaboration session "{session.session_name}"',
            }

        except CollaborationSession.DoesNotExist:
            return {
                "success": False,
                "error": "Session not found",
                "message": "The requested collaboration session does not exist",
            }
        except (ConnectionError, TimeoutError, HTTPError) as e:
            logger.error(f"Error joining collaboration session: {e!s}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to join collaboration session",
            }

    def leave_collaboration_session(self, session_id: str) -> dict[str, Any]:
        """Leave a collaboration session.

        Args:
        ----
            session_id: Collaboration session identifier

        Returns:
        -------
            Leave result with status

        """
        try:
            session = CollaborationSession.objects.get(id=session_id)

            # Remove user from session
            session.participants.remove(self.user)

            # Update presence
            self._update_user_presence(session_id, False)

            # Broadcast leave event
            group_name = f"{self.CHANNEL_GROUP_PREFIX}_{session_id}"
            self._broadcast_event(
                group_name,
                CollaborationEvent(
                    event_type="user_left",
                    user_id=self.user.id,
                    user_name=self.user.username,
                    data={
                        "session_id": session_id,
                        "participant_count": session.participants.count(),
                    },
                    timestamp=timezone.now(),
                    session_id=session_id,
                ),
            )

            return {
                "success": True,
                "message": f'Left collaboration session "{session.session_name}"',
            }

        except CollaborationSession.DoesNotExist:
            return {
                "success": False,
                "error": "Session not found",
                "message": "The requested collaboration session does not exist",
            }
        except (ConnectionError, TimeoutError, HTTPError) as e:
            logger.error(f"Error leaving collaboration session: {e!s}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to leave collaboration session",
            }

    # ========== COLLABORATIVE ANNOTATIONS ==========

    def create_annotation(self, annotation_data: AnnotationData, session_id: str | None = None) -> dict[str, Any]:
        """Create a new spatial annotation.

        Args:
        ----
            annotation_data: Annotation data structure
            session_id: Optional collaboration session ID

        Returns:
        -------
            Annotation creation result with annotation details

        """
        try:
            # Convert geometry to GEOSGeometry
            geometry = GEOSGeometry(json.dumps(annotation_data.geometry))

            annotation = SpatialAnnotation.objects.create(
                project=self.project,
                user=self.user,
                annotation_text=annotation_data.text,
                annotation_type=annotation_data.annotation_type,
                geometry=geometry,
                color=annotation_data.color,
                is_persistent=annotation_data.is_persistent,
                metadata=annotation_data.metadata or {},
            )

            # Broadcast to collaboration session if provided
            if session_id:
                group_name = f"{self.CHANNEL_GROUP_PREFIX}_{session_id}"
                self._broadcast_event(
                    group_name,
                    CollaborationEvent(
                        event_type="annotation_added",
                        user_id=self.user.id,
                        user_name=self.user.username,
                        data={
                            "annotation_id": str(annotation.id),
                            "annotation": self._serialize_annotation(annotation),
                        },
                        timestamp=timezone.now(),
                        session_id=session_id,
                    ),
                )

            return {
                "success": True,
                "annotation_id": str(annotation.id),
                "annotation": self._serialize_annotation(annotation),
                "message": "Annotation created successfully",
            }

        except (AttributeError, KeyError, ValueError, TypeError) as e:
            logger.error(f"Error creating annotation: {e!s}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to create annotation",
            }

    def update_annotation(
        self,
        annotation_id: str,
        annotation_data: AnnotationData,
        session_id: str | None = None,
    ) -> dict[str, Any]:
        """Update an existing annotation.

        Args:
        ----
            annotation_id: Annotation identifier to update
            annotation_data: Updated annotation data
            session_id: Optional collaboration session ID

        Returns:
        -------
            Update result with updated annotation details

        """
        try:
            annotation = SpatialAnnotation.objects.get(id=annotation_id)

            # Check permissions
            if annotation.user != self.user:
                return {
                    "success": False,
                    "error": "Permission denied",
                    "message": "You can only edit your own annotations",
                }

            # Update annotation
            annotation.annotation_text = annotation_data.text
            annotation.annotation_type = annotation_data.annotation_type
            annotation.color = annotation_data.color
            annotation.is_persistent = annotation_data.is_persistent

            if annotation_data.geometry:
                annotation.geometry = GEOSGeometry(json.dumps(annotation_data.geometry))

            if annotation_data.metadata:
                annotation.metadata.update(annotation_data.metadata)

            annotation.save()

            # Broadcast update
            if session_id:
                group_name = f"{self.CHANNEL_GROUP_PREFIX}_{session_id}"
                self._broadcast_event(
                    group_name,
                    CollaborationEvent(
                        event_type="annotation_updated",
                        user_id=self.user.id,
                        user_name=self.user.username,
                        data={
                            "annotation_id": annotation_id,
                            "annotation": self._serialize_annotation(annotation),
                        },
                        timestamp=timezone.now(),
                        session_id=session_id,
                    ),
                )

            return {
                "success": True,
                "annotation": self._serialize_annotation(annotation),
                "message": "Annotation updated successfully",
            }

        except SpatialAnnotation.DoesNotExist:
            return {
                "success": False,
                "error": "Annotation not found",
                "message": "The requested annotation does not exist",
            }
        except (ConnectionError, TimeoutError, HTTPError) as e:
            logger.error(f"Error updating annotation: {e!s}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to update annotation",
            }

    def delete_annotation(self, annotation_id: str, session_id: str | None = None) -> dict[str, Any]:
        """Delete an annotation.

        Args:
        ----
            annotation_id: Annotation identifier to delete
            session_id: Optional collaboration session ID

        Returns:
        -------
            Deletion result with status

        """
        try:
            annotation = SpatialAnnotation.objects.get(id=annotation_id)

            # Check permissions
            if annotation.user != self.user:
                return {
                    "success": False,
                    "error": "Permission denied",
                    "message": "You can only delete your own annotations",
                }

            # Soft delete
            annotation.soft_delete()

            # Broadcast deletion
            if session_id:
                group_name = f"{self.CHANNEL_GROUP_PREFIX}_{session_id}"
                self._broadcast_event(
                    group_name,
                    CollaborationEvent(
                        event_type="annotation_deleted",
                        user_id=self.user.id,
                        user_name=self.user.username,
                        data={"annotation_id": annotation_id},
                        timestamp=timezone.now(),
                        session_id=session_id,
                    ),
                )

            return {"success": True, "message": "Annotation deleted successfully"}

        except SpatialAnnotation.DoesNotExist:
            return {
                "success": False,
                "error": "Annotation not found",
                "message": "The requested annotation does not exist",
            }
        except (ConnectionError, TimeoutError, HTTPError) as e:
            logger.error(f"Error deleting annotation: {e!s}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to delete annotation",
            }

    # ========== COLLABORATIVE DRAWING ==========

    def create_drawing(self, drawing_data: DrawingData, session_id: str | None = None) -> dict[str, Any]:
        """Create a collaborative drawing element.

        Args:
        ----
            drawing_data: Drawing data structure
            session_id: Optional collaboration session ID

        Returns:
        -------
            Drawing creation result with drawing details

        """
        try:
            geometry = GEOSGeometry(json.dumps(drawing_data.geometry))

            drawing = CollaborativeDrawing.objects.create(
                project=self.project,
                user=self.user,
                drawing_type=drawing_data.drawing_type,
                geometry=geometry,
                style=drawing_data.style,
                is_temporary=drawing_data.is_temporary,
            )

            # Broadcast to collaboration session
            if session_id:
                group_name = f"{self.CHANNEL_GROUP_PREFIX}_{session_id}"
                self._broadcast_event(
                    group_name,
                    CollaborationEvent(
                        event_type="drawing_added",
                        user_id=self.user.id,
                        user_name=self.user.username,
                        data={
                            "drawing_id": str(drawing.id),
                            "drawing": self._serialize_drawing(drawing),
                        },
                        timestamp=timezone.now(),
                        session_id=session_id,
                    ),
                )

            return {
                "success": True,
                "drawing_id": str(drawing.id),
                "drawing": self._serialize_drawing(drawing),
                "message": "Drawing created successfully",
            }

        except (AttributeError, KeyError, ValueError, TypeError) as e:
            logger.error(f"Error creating drawing: {e!s}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to create drawing",
            }

    def update_drawing(
        self,
        drawing_id: str,
        drawing_data: DrawingData,
        session_id: str | None = None,
    ) -> dict[str, Any]:
        """Update a collaborative drawing.

        Args:
        ----
            drawing_id: Drawing identifier to update
            drawing_data: Updated drawing data
            session_id: Optional collaboration session ID

        Returns:
        -------
            Update result with updated drawing details

        """
        try:
            drawing = CollaborativeDrawing.objects.get(id=drawing_id)

            # Check if drawing is locked by another user
            if drawing.is_locked and drawing.user != self.user:
                return {
                    "success": False,
                    "error": "Drawing is locked",
                    "message": f"Drawing is currently being edited by {drawing.user.username}",
                }

            # Update drawing
            if drawing_data.geometry:
                drawing.geometry = GEOSGeometry(json.dumps(drawing_data.geometry))

            if drawing_data.style:
                drawing.style.update(drawing_data.style)

            drawing.version += 1
            drawing.save()

            # Broadcast update
            if session_id:
                group_name = f"{self.CHANNEL_GROUP_PREFIX}_{session_id}"
                self._broadcast_event(
                    group_name,
                    CollaborationEvent(
                        event_type="drawing_updated",
                        user_id=self.user.id,
                        user_name=self.user.username,
                        data={
                            "drawing_id": drawing_id,
                            "drawing": self._serialize_drawing(drawing),
                        },
                        timestamp=timezone.now(),
                        session_id=session_id,
                    ),
                )

            return {
                "success": True,
                "drawing": self._serialize_drawing(drawing),
                "message": "Drawing updated successfully",
            }

        except CollaborativeDrawing.DoesNotExist:
            return {
                "success": False,
                "error": "Drawing not found",
                "message": "The requested drawing does not exist",
            }
        except (ConnectionError, TimeoutError, HTTPError) as e:
            logger.error(f"Error updating drawing: {e!s}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to update drawing",
            }

    # ========== PRESENCE TRACKING ==========

    def _update_user_presence(self, session_id: str, is_active: bool) -> None:
        """Update user presence in a collaboration session.

        Args:
        ----
            session_id: Collaboration session identifier
            is_active: Whether user is currently active

        """
        presence_key = f"{self.cache_key_prefix}:presence:{session_id}"

        if is_active:
            presence_data = {
                "user_id": self.user.id,
                "username": self.user.username,
                "last_seen": timezone.now().isoformat(),
                "session_id": session_id,
            }
            cache.set(presence_key, presence_data, self.PRESENCE_TIMEOUT)
        else:
            cache.delete(presence_key)

    def get_active_participants(self, session_id: str) -> list[dict[str, Any]]:
        """Get list of currently active participants in a session.

        Args:
        ----
            session_id: Collaboration session identifier

        Returns:
        -------
            List of active participant data

        """
        try:
            session = CollaborationSession.objects.get(id=session_id)
            active_participants = []

            for participant in session.participants.all():
                presence_key = f"{self.CACHE_PREFIX}:{participant.id}:presence:{session_id}"
                presence_data = cache.get(presence_key)

                if presence_data:
                    active_participants.append(
                        {
                            "user_id": participant.id,
                            "username": participant.username,
                            "last_seen": presence_data.get("last_seen"),
                            "is_active": True,
                        },
                    )
                else:
                    active_participants.append(
                        {
                            "user_id": participant.id,
                            "username": participant.username,
                            "last_seen": None,
                            "is_active": False,
                        },
                    )

            return active_participants

        except CollaborationSession.DoesNotExist:
            return []

    # ========== UTILITY METHODS ==========

    def _serialize_session(self, session: CollaborationSession) -> dict[str, Any]:
        """Serialize collaboration session to dictionary.

        Args:
        ----
            session: Collaboration session to serialize

        Returns:
        -------
            Serialized session data dictionary

        """
        return {
            "id": str(session.id),
            "session_name": session.session_name,
            "description": session.description,
            "is_public": session.is_public,
            "is_active": session.is_active,
            "created_by": {
                "id": session.created_by.id,
                "username": session.created_by.username,
            },
            "participant_count": session.participants.count(),
            "max_participants": session.max_participants,
            "started_at": session.started_at.isoformat(),
            "ended_at": session.ended_at.isoformat() if session.ended_at else None,
        }

    def _serialize_annotation(self, annotation: SpatialAnnotation) -> dict[str, Any]:
        """Serialize spatial annotation to dictionary.

        Args:
        ----
            annotation: Spatial annotation to serialize

        Returns:
        -------
            Serialized annotation data dictionary

        """
        return {
            "id": str(annotation.id),
            "text": annotation.annotation_text,
            "type": annotation.annotation_type,
            "color": annotation.color,
            "geometry": annotation.geometry.geojson if annotation.geometry else None,
            "is_visible": annotation.is_visible,
            "is_persistent": annotation.is_persistent,
            "metadata": annotation.metadata,
            "user": {"id": annotation.user.id, "username": annotation.user.username},
            "created_at": annotation.created_at.isoformat(),
            "updated_at": annotation.updated_at.isoformat(),
        }

    def _serialize_drawing(self, drawing: CollaborativeDrawing) -> dict[str, Any]:
        """Serialize collaborative drawing to dictionary.

        Args:
        ----
            drawing: Collaborative drawing to serialize

        Returns:
        -------
            Serialized drawing data dictionary

        """
        return {
            "id": str(drawing.id),
            "drawing_type": drawing.drawing_type,
            "geometry": drawing.geometry.geojson if drawing.geometry else None,
            "style": drawing.style,
            "is_temporary": drawing.is_temporary,
            "is_locked": drawing.is_locked,
            "version": drawing.version,
            "user": {"id": drawing.user.id, "username": drawing.user.username},
            "created_at": drawing.created_at.isoformat(),
            "updated_at": drawing.updated_at.isoformat(),
        }

    def _broadcast_event(self, group_name: str, event: CollaborationEvent) -> None:
        """Broadcast event to WebSocket group.

        Args:
        ----
            group_name: WebSocket group name
            event: Collaboration event to broadcast

        """
        if self.channel_layer:
            try:
                async_to_sync(self.channel_layer.group_send)(
                    group_name,
                    {
                        "type": "collaboration_event",
                        "event": {
                            "event_type": event.event_type,
                            "user_id": event.user_id,
                            "user_name": event.user_name,
                            "data": event.data,
                            "timestamp": event.timestamp.isoformat(),
                            "session_id": event.session_id,
                        },
                    },
                )
            except (ValidationError, ValueError) as e:
                logger.error(f"Error broadcasting event: {e!s}")

    def get_session_annotations(self, session_id: str) -> list[dict[str, Any]]:
        """Get all annotations created during a collaboration session.

        Args:
        ----
            session_id: Collaboration session identifier

        Returns:
        -------
            List of annotation data from the session

        """
        try:
            session = CollaborationSession.objects.get(id=session_id)

            # Get annotations created during session timeframe
            annotations = SpatialAnnotation.objects.filter(
                project=session.project,
                created_at__gte=session.started_at,
                deleted_at__isnull=True,
            )

            if session.ended_at:
                annotations = annotations.filter(created_at__lte=session.ended_at)

            return [self._serialize_annotation(ann) for ann in annotations]

        except CollaborationSession.DoesNotExist:
            return []

    def get_session_drawings(self, session_id: str) -> list[dict[str, Any]]:
        """Get all drawings created during a collaboration session.

        Args:
        ----
            session_id: Collaboration session identifier

        Returns:
        -------
            List of drawing data from the session

        """
        try:
            session = CollaborationSession.objects.get(id=session_id)

            # Get drawings created during session timeframe
            drawings = CollaborativeDrawing.objects.filter(
                project=session.project,
                created_at__gte=session.started_at,
                deleted_at__isnull=True,
            )

            if session.ended_at:
                drawings = drawings.filter(created_at__lte=session.ended_at)

            return [self._serialize_drawing(drawing) for drawing in drawings]

        except CollaborationSession.DoesNotExist:
            return []


def get_spatial_collaboration_service(user: AbstractUser, project: Any | None = None) -> SpatialCollaborationService:
    """Factory function to get spatial collaboration service.

    Args:
    ----
        user: User for the collaboration service
        project: Optional project context

    Returns:
    -------
        Configured spatial collaboration service instance

    """
    return SpatialCollaborationService(user, project)
