# Unified Augment Code Prompt for CLEAR Project Task Analysis and Implementation

**Analyze Priority Group [X]: [GROUP NAME] tasks [TASK NUMBERS] in `docs/augment_tasks.md` and create comprehensive implementation documentation with analysis, matrix, and subtasks in the correct order.**

## MANDATORY PROCESS - ALL PHASES MUST BE COMPLETED IN ORDER:

### Phase 1: Complete Codebase Inventory and Analysis

1. **Map ALL Django apps**: Use codebase-retrieval to examine `apps/` directory and create complete inventory of all Django apps (25+ apps total)
2. **Map ALL relevant directories**: Document structure of `templates/`, `static/`, `config/`, `tests/`, and other project directories relevant to this task group
3. **Identify existing implementations**: For each app, use codebase-retrieval to understand what's already implemented vs. what's missing for this specific task group
4. **NO shortcuts**: Analyze EVERY app for applicability - no representative examples

### Phase 2: Implementation Matrix Creation

For each task in the priority group, create a comprehensive matrix showing:

**Apps with EXCELLENT implementation:**
- List apps with comprehensive existing implementation
- Specify what aspects are exemplary (with specific file references)
- Include code patterns that should be replicated

**Apps with GOOD implementation:**
- List apps with solid foundation but room for enhancement
- Reference specific files and patterns
- Note what minor improvements are needed

**Apps requiring PARTIAL implementation:**
- List apps with some implementation but significant gaps
- Specify what exists and what's missing
- Reference specific files that need enhancement

**Apps requiring COMPLETE implementation:**
- List apps with little to no implementation
- Specify the full scope of work needed
- Reference where implementation should be added

### Phase 3: Implementation Strategy

Based on the matrix analysis:
- **Priority ordering**: Based on app importance, model count, and dependencies
- **Risk assessment**: Identify high-risk implementations needing special attention
- **Resource estimation**: Complexity assessment for each app (days/weeks)
- **Dependency mapping**: Which apps should be implemented first

### Phase 4: Detailed Subtask Creation

For each main task, create comprehensive subtasks following this structure:

```markdown
- [ ] [TASK NUMBER]. [TASK DESCRIPTION]
  - [ ] [TASK NUMBER].1 **Enhance Apps with EXCELLENT Implementation**:
    - [ ] [TASK NUMBER].1.1 `apps/[app_name]/[specific_file.py]` - [Specific enhancement needed]
    - [ ] [TASK NUMBER].1.2 `apps/[app_name]/[specific_file.py]` - [Specific enhancement needed]
  - [ ] [TASK NUMBER].2 **Upgrade Apps with GOOD Implementation**:
    - [ ] [TASK NUMBER].2.1 `apps/[app_name]/[specific_file.py]` - [Specific upgrade needed]
    - [ ] [TASK NUMBER].2.2 `apps/[app_name]/[specific_file.py]` - [Specific upgrade needed]
  - [ ] [TASK NUMBER].3 **Complete Apps with PARTIAL Implementation**:
    - [ ] [TASK NUMBER].3.1 `apps/[app_name]/[specific_file.py]` - [Specific completion needed]
    - [ ] [TASK NUMBER].3.2 `apps/[app_name]/[specific_file.py]` - [Specific completion needed]
  - [ ] [TASK NUMBER].4 **Implement Apps Requiring COMPLETE Work**:
    - [ ] [TASK NUMBER].4.1 `apps/[app_name]/` - Create [specific_file.py] with [functionality]
    - [ ] [TASK NUMBER].4.2 `apps/[app_name]/` - Create [specific_file.py] with [functionality]
```

## OUTPUT STRUCTURE REQUIREMENTS:

The final document must follow this exact structure:

```markdown
# Priority [X]: [Priority Group Name]

**Priority Level:** [Critical/High/Medium/Low]
**Total Tasks:** [Number]
**Categories:** [Category1, Category2, etc.]

[Brief description of this priority group]

## Implementation Matrix Analysis

**Analysis Date:** [Current Date]
**Total Django Apps Analyzed:** [Number]
**Analysis Scope:** Complete codebase examination for Priority [X] task implementation status

### Apps Inventory
[List all apps organized by category]

## [Category 1 Name] (Tasks [X-Y])

### Task [X]: [Task Name] Implementation Matrix

[Detailed matrix for this specific task with EXCELLENT/GOOD/PARTIAL/COMPLETE classifications]

- [ ] [X]. [Full task description from original]
  [Detailed subtasks based on matrix analysis]

### Task [X+1]: [Task Name] Implementation Matrix

[Detailed matrix for this specific task]

- [ ] [X+1]. [Full task description from original]
  [Detailed subtasks based on matrix analysis]

[Continue for all tasks in category]

## [Category 2 Name] (Tasks [Y-Z])

[Repeat structure for each category]

## Implementation Strategy

### Priority Ordering Based on Analysis
[Phased approach based on matrix findings]

### Risk Assessment
[High/Medium/Low risk apps and why]

### Resource Estimation
[Time estimates based on complexity]

## Summary Statistics

**Total Apps Analyzed:** [Number]
**Apps with Excellent Implementation:** [Number] per task category
**Apps with Good Implementation:** [Number] per task category
**Apps Requiring Partial Implementation:** [Number] per task category
**Apps Requiring Complete Implementation:** [Number] per task category

**Estimated Total Effort:** [X-Y] developer days
**Critical Path:** [Key bottlenecks]
**Success Metrics:** [How to measure completion]

---

*This is part [X] of 6 of the CLEAR Project Improvement Tasks documentation.*
```

## CRITICAL REQUIREMENTS:

1. **Matrix BEFORE Tasks**: Always place the implementation matrix analysis BEFORE each task's checkbox and subtasks
2. **Consistent Structure**: Every task gets its own matrix analysis immediately before its checkbox
3. **Evidence-Based**: Every classification must reference actual files and code found during analysis
4. **Exhaustive Coverage**: ALL 25+ apps must be analyzed for EACH task (no sampling)
5. **Actionable Subtasks**: Each subtask must specify exact file paths and specific changes needed

## QUALITY EXPECTATIONS:

- **Analysis Depth**: Spend significant time examining the codebase before creating matrices
- **No Assumptions**: Base everything on actual code analysis, not theoretical patterns
- **Complete Coverage**: Every app must be categorized for every applicable task
- **Specific References**: Use actual class names, method names, and file paths found
- **Clear Progression**: Structure shows natural implementation flow from best to worst

**TIME INVESTMENT**: This should be a thorough, methodical analysis. Quality over speed.