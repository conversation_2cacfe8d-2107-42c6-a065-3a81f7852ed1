**Analyze and create implementation matrix for in `docs/augment_tasks.md`. Focus ONLY on comprehensive codebase analysis and implementation matrix creation - do not create subtasks.**

**MANDATORY EXHAUSTIVE ANALYSIS PROCESS:**

## **Phase 1: Complete Codebase Inventory**

1. **Map ALL Django apps**: Use codebase-retrieval to examine `apps/` directory and create complete inventory of all Django apps
2. **Map ALL relevant directories**: Document structure of `templates/`, `static/`, `config/`, `tests/`, and other project directories relevant to this task group
3. **Identify existing implementations**: For each app, use codebase-retrieval to understand what's already implemented vs. what's missing for this specific task group
4. **Create implementation matrix**: Document which concerns are implemented in which apps, with specific file references

## **Phase 2: Task Group Analysis**

For the specified task group, perform this complete analysis:

### **2.1 Current State Analysis**

- **Per-app audit**: Use codebase-retrieval to examine EVERY relevant app for existing implementations related to this task group
- **Gap identification**: Document what exists vs. what's missing in each specific app
- **Pattern analysis**: Identify inconsistencies between apps (e.g., some have good implementation, others don't)
- **Dependency mapping**: Understand code dependencies and relationships relevant to this task group

### **2.2 Implementation Matrix Creation**

Create a comprehensive matrix showing:

**Apps with GOOD existing implementation:**

- List apps with strong existing implementation
- Specify what aspects are well-implemented
- Reference specific files and patterns

**Apps requiring PARTIAL implementation:**

- List apps with some implementation but gaps
- Specify what exists and what's missing
- Reference specific files that need enhancement

**Apps requiring COMPLETE implementation:**

- List apps with little to no implementation
- Specify the scope of work needed
- Reference where implementation should be added

### **2.3 Implementation Strategy**

- **Priority ordering**: Based on app importance, model count, and existing infrastructure
- **Dependency considerations**: Which apps should be implemented first due to dependencies
- **Resource estimation**: Complexity assessment for each app
- **Risk assessment**: Identify high-risk implementations that need special attention

## **Phase 3: Evidence-Based Analysis**

- **Use codebase-retrieval extensively** to understand current implementations
- **Reference actual class names, method names, and file structures** found in the codebase
- **Distinguish between "needs to be created" vs. "needs to be enhanced"** based on actual analysis
- **Cross-reference findings** across apps to ensure comprehensive coverage

**ANALYSIS DEPTH EXPECTATION:**

- Spend significant time on codebase analysis before creating any matrix
- Use multiple codebase-retrieval calls to understand different aspects of each app
- Cross-reference findings across apps to ensure comprehensive coverage

**OUTPUT REQUIREMENTS:**

1. Update `docs/augment_tasks.md` with comprehensive implementation matrix for the specified task group
2. Each matrix entry must be traceable to specific codebase analysis
3. Include brief analysis notes explaining why certain apps are categorized as they are
4. Maintain clear organization and provide implementation strategy

**CRITICAL ANALYSIS REQUIREMENTS:**

### **Exhaustive App Coverage**

- **NO representative examples**: Analyze ALL apps for applicability to this task group
- **NO assumptions**: Base every matrix entry on actual codebase analysis
- **NO shortcuts**: Take time to thoroughly analyze each app's current state

### **Evidence-Based Matrix**

- Reference actual class names, method names, and file structures found in the codebase
- Distinguish between existing implementations that need enhancement vs. completely missing functionality
- Provide specific file references for existing implementations

**TIME INVESTMENT EXPECTATION:** This analysis should be thorough and methodical. Take the time needed to properly analyze the codebase rather than making assumptions.

```markdown
### Implementation Matrix for Tasks 1-7

Based on comprehensive codebase analysis of all 23 Django apps:

**Apps with GOOD existing implementation:**
- `apps/core/` - Has type definitions in `types.py`, logging infrastructure in `logging.py`, error handling in `exceptions.py`
- `apps/common/` - Implementation quality report shows excellent coverage, has comprehensive mixins
- `apps/authentication/` - Partial type hints in `models.py`, good error handling in `views.py`

**Apps requiring PARTIAL implementation:**
- `apps/projects/` (15 models - has some type hints in `managers.py` but missing in views and forms)
- `apps/infrastructure/` (43 models - has spatial type hints but missing business logic types)

**Apps requiring COMPLETE implementation:**
- `apps/analytics/` (13 models - no type hints, basic logging only)
- `apps/messaging/` (15 models - no structured logging, generic exceptions)
- `apps/financial/` (10 models - no docstrings, no error handling)
```

---

**Create comprehensive subtasks for Priority Group [X]: [GROUP NAME] tasks [TASK NUMBERS] in `docs/augment_tasks.md` based on the implementation matrix. Focus ONLY on detailed subtask breakdown - the codebase analysis and implementation matrix should already be complete.**

**MANDATORY SUBTASK CREATION PROCESS:**

## **Phase 1: Matrix-Based Subtask Planning**

1. **Use existing implementation matrix**: Reference the implementation matrix already created for this task group
2. **App-specific subtask creation**: Create subtasks for each app based on its matrix classification (GOOD/PARTIAL/COMPLETE)
3. **File-specific targeting**: Create subtasks that target specific files identified in the matrix analysis
4. **Dependency-aware ordering**: Order subtasks based on dependencies identified in the matrix

## **Phase 2: Comprehensive Subtask Creation**

For each task in the group, create subtasks following this structure:

### **2.1 Enhancement Subtasks (for apps with GOOD implementation)**

- Create subtasks to enhance existing good implementations
- Reference specific files and classes that need minor improvements
- Focus on standardization and consistency improvements

### **2.2 Completion Subtasks (for apps with PARTIAL implementation)**

- Create subtasks to complete missing functionality
- Reference existing files that need enhancement
- Reference new files that need to be created
- Ensure integration with existing patterns

### **2.3 Implementation Subtasks (for apps with COMPLETE implementation needed)**

- Create subtasks for full implementation
- Specify exact files that need to be created
- Reference patterns from apps with good implementation
- Account for app-specific requirements and existing architecture

## **Phase 3: Subtask Quality Requirements**

Each subtask must specify:

- **Exact file path**: Not "apps/auth/" but "apps/authentication/forms/login_form.py"
- **Specific implementation**: Not "add logging" but "add structured logging to login_attempt() method in LoginView class"
- **Current state awareness**: Reference what already exists and what needs to be added/modified based on the matrix
- **App-specific context**: Account for each app's unique requirements and existing patterns

## **Phase 4: Cross-Cutting Implementation**

For cross-cutting concerns in this task group:

### **4.1 Standardization Subtasks**

- Create subtasks for EVERY app where the feature should be implemented
- Specify exact files that need modification in each app
- Account for app-specific variations and requirements
- Ensure no app is overlooked based on the matrix

### **4.2 Integration Subtasks**

- Create subtasks for integrating implementations across apps
- Reference shared utilities and common patterns
- Ensure consistency across the entire codebase

## **Phase 5: Verification and Completeness**

Before finalizing subtasks:

1. **Matrix cross-reference**: Ensure all apps identified in the matrix have corresponding subtasks
2. **Implementation gap verification**: Confirm subtasks address actual missing functionality identified in the matrix
3. **Consistency validation**: Ensure similar functionality is addressed consistently across all relevant apps
4. **Dependency validation**: Verify subtask ordering accounts for dependencies identified in the matrix

**SUBTASK CREATION REQUIREMENTS:**

### **Matrix-Driven Approach**

- **Base every subtask on matrix analysis**: Don't create generic subtasks - use the specific findings from the implementation matrix
- **Reference matrix classifications**: Subtasks should clearly correspond to whether an app needs GOOD/PARTIAL/COMPLETE implementation
- **Use matrix evidence**: Reference the specific files and patterns identified in the matrix analysis

### **Comprehensive Coverage**

- **NO representative examples**: If a task applies to multiple apps per the matrix, create subtasks for ALL applicable apps
- **NO assumptions**: Base every subtask on the matrix analysis, not theoretical patterns
- **NO shortcuts**: Create subtasks for every app identified in the matrix as needing work

### **Quality Assurance**

- Each subtask should be verifiable against the implementation matrix
- Subtasks should account for existing code patterns identified in the matrix
- No subtask should duplicate existing functionality without clear justification for enhancement

**OUTPUT REQUIREMENTS:**

1. Update `docs/augment_tasks.md` with exhaustively detailed subtasks for the specified task group
2. Each subtask must be traceable to specific matrix analysis
3. Include brief implementation notes where helpful to explain approach
4. Maintain clear organization and progress tracking format
5. Ensure subtasks follow the dependency order identified in the matrix

**IMPLEMENTATION DEPTH EXPECTATION:**

- Create subtasks at the individual file and method level where appropriate
- Account for the specific needs of each app as identified in the matrix
- Ensure subtasks are actionable and specific enough for implementation

**TIME INVESTMENT EXPECTATION:** Focus on creating comprehensive, actionable subtasks based on the matrix rather than re-analyzing the codebase.

```markdown
- [ ] 1. **Add comprehensive type hints across all Django apps**
  - [ ] 1.1 **Enhance Apps with GOOD Implementation**:
    - [ ] 1.1.1 `apps/core/types.py` - Add missing generic types for spatial operations
    - [ ] 1.1.2 `apps/common/mixins.py` - Add type hints to all mixin methods
  - [ ] 1.2 **Complete Apps with PARTIAL Implementation**:
    - [ ] 1.2.1 `apps/projects/views.py` - Add type hints to all view methods (15 views identified)
    - [ ] 1.2.2 `apps/infrastructure/services/` - Add type hints to spatial service methods
  - [ ] 1.3 **Implement Apps Requiring COMPLETE Work**:
    - [ ] 1.3.1 `apps/analytics/models.py` - Add type hints to all 13 model classes
    - [ ] 1.3.2 `apps/messaging/models.py` - Add type hints to all 15 messaging models
```
