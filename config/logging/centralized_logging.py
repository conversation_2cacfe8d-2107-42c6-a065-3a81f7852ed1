"""
Centralized Logging Configuration for CLEAR Platform

This module provides centralized logging configuration including:
- ELK Stack (Elasticsearch, Logstash, Kibana) integration planning
- Structured logging with JSON formatting
- Security event logging
- Performance monitoring integration
- Production-ready log management
"""

import logging
import logging.config
import os
import time
import uuid
from datetime import datetime
from typing import Any

import structlog
from django.conf import settings


class SecurityEventLogger:
    """Dedicated logger for security events"""

    def __init__(self):
        self.logger = logging.getLogger("security")

    def log_authentication_event(
        self,
        user_id: str,
        event_type: str,
        ip_address: str,
        user_agent: str,
        success: bool = True,
        details: dict[str, Any] | None = None,
    ):
        """Log authentication events for security monitoring"""
        event_data = {
            "event_type": "authentication",
            "event_subtype": event_type,
            "user_id": user_id,
            "ip_address": ip_address,
            "user_agent": user_agent,
            "success": success,
            "timestamp": datetime.utcnow().isoformat(),
            "details": details or {},
        }

        if success:
            self.logger.info("Authentication event", extra=event_data)
        else:
            self.logger.warning("Authentication failure", extra=event_data)

    def log_permission_event(
        self,
        user_id: str,
        resource: str,
        action: str,
        granted: bool,
        details: dict[str, Any] | None = None,
    ):
        """Log permission access attempts"""
        event_data = {
            "event_type": "permission",
            "user_id": user_id,
            "resource": resource,
            "action": action,
            "granted": granted,
            "timestamp": datetime.utcnow().isoformat(),
            "details": details or {},
        }

        if granted:
            self.logger.info("Permission granted", extra=event_data)
        else:
            self.logger.warning("Permission denied", extra=event_data)

    def log_security_incident(
        self,
        incident_type: str,
        severity: str,
        description: str,
        user_id: str | None = None,
        ip_address: str | None = None,
        details: dict[str, Any] | None = None,
    ):
        """Log security incidents"""
        event_data = {
            "event_type": "security_incident",
            "incident_type": incident_type,
            "severity": severity,
            "description": description,
            "user_id": user_id,
            "ip_address": ip_address,
            "timestamp": datetime.utcnow().isoformat(),
            "details": details or {},
        }

        if severity in ["critical", "high"]:
            self.logger.error("Security incident", extra=event_data)
        else:
            self.logger.warning("Security incident", extra=event_data)


class PerformanceLogger:
    """Dedicated logger for performance monitoring"""

    def __init__(self):
        self.logger = logging.getLogger("performance")

    def log_request_performance(
        self,
        request_id: str,
        view_name: str,
        duration_ms: float,
        status_code: int,
        user_id: str | None = None,
        details: dict[str, Any] | None = None,
    ):
        """Log request performance metrics"""
        event_data = {
            "event_type": "request_performance",
            "request_id": request_id,
            "view_name": view_name,
            "duration_ms": duration_ms,
            "status_code": status_code,
            "user_id": user_id,
            "timestamp": datetime.utcnow().isoformat(),
            "details": details or {},
        }

        # Log as warning if request takes longer than 1 second
        if duration_ms > 1000:
            self.logger.warning("Slow request", extra=event_data)
        else:
            self.logger.info("Request performance", extra=event_data)

    def log_database_query(
        self,
        query_type: str,
        table: str,
        duration_ms: float,
        rows_affected: int | None = None,
        query_hash: str | None = None,
    ):
        """Log database query performance"""
        event_data = {
            "event_type": "database_query",
            "query_type": query_type,
            "table": table,
            "duration_ms": duration_ms,
            "rows_affected": rows_affected,
            "query_hash": query_hash,
            "timestamp": datetime.utcnow().isoformat(),
        }

        # Log as warning if query takes longer than 500ms
        if duration_ms > 500:
            self.logger.warning("Slow database query", extra=event_data)
        else:
            self.logger.debug("Database query", extra=event_data)


class HTMXEventLogger:
    """Dedicated logger for HTMX-specific events"""

    def __init__(self):
        self.logger = logging.getLogger("htmx")

    def log_htmx_request(
        self,
        request_id: str,
        trigger: str,
        target: str,
        user_id: str | None = None,
        details: dict[str, Any] | None = None,
    ):
        """Log HTMX request events"""
        event_data = {
            "event_type": "htmx_request",
            "request_id": request_id,
            "trigger": trigger,
            "target": target,
            "user_id": user_id,
            "timestamp": datetime.utcnow().isoformat(),
            "details": details or {},
        }

        self.logger.debug("HTMX request", extra=event_data)

    def log_websocket_event(
        self,
        event_type: str,
        channel: str,
        user_id: str | None = None,
        details: dict[str, Any] | None = None,
    ):
        """Log WebSocket events"""
        event_data = {
            "event_type": "websocket",
            "websocket_event": event_type,
            "channel": channel,
            "user_id": user_id,
            "timestamp": datetime.utcnow().isoformat(),
            "details": details or {},
        }

        self.logger.info("WebSocket event", extra=event_data)


# ELK Stack Configuration Templates
ELK_STACK_CONFIG = {
    "elasticsearch": {
        "hosts": os.getenv("ELASTICSEARCH_HOSTS", "localhost:9200").split(","),
        "use_ssl": os.getenv("ELASTICSEARCH_USE_SSL", "false").lower() == "true",
        "verify_certs": os.getenv("ELASTICSEARCH_VERIFY_CERTS", "true").lower() == "true",
        "username": os.getenv("ELASTICSEARCH_USERNAME"),
        "password": os.getenv("ELASTICSEARCH_PASSWORD"),
        "timeout": int(os.getenv("ELASTICSEARCH_TIMEOUT", "30")),
        "max_retries": int(os.getenv("ELASTICSEARCH_MAX_RETRIES", "3")),
        "retry_on_timeout": True,
    },
    "logstash": {
        "host": os.getenv("LOGSTASH_HOST", "localhost"),
        "port": int(os.getenv("LOGSTASH_PORT", "5044")),
        "use_ssl": os.getenv("LOGSTASH_USE_SSL", "false").lower() == "true",
    },
    "kibana": {
        "host": os.getenv("KIBANA_HOST", "localhost"),
        "port": int(os.getenv("KIBANA_PORT", "5601")),
        "use_ssl": os.getenv("KIBANA_USE_SSL", "false").lower() == "true",
    },
}


def get_centralized_logging_config():
    """Get centralized logging configuration with ELK stack integration"""
    # Base logging configuration
    config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "json": {
                "format": "%(asctime)s %(name)s %(levelname)s %(message)s",
                "class": "pythonjsonlogger.jsonlogger.JsonFormatter",
            },
            "verbose": {
                "format": "{levelname} {asctime} {module} {process:d} {thread:d} {message}",
                "style": "{",
            },
            "simple": {
                "format": "{levelname} {message}",
                "style": "{",
            },
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "formatter": "json" if settings.DEBUG else "verbose",
            },
            "file": {
                "class": "logging.handlers.RotatingFileHandler",
                "filename": os.path.join(settings.BASE_DIR, "logs", "clear.log"),
                "maxBytes": 1024 * 1024 * 50,  # 50MB
                "backupCount": 10,
                "formatter": "json",
            },
            "security_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "filename": os.path.join(settings.BASE_DIR, "logs", "security.log"),
                "maxBytes": 1024 * 1024 * 100,  # 100MB
                "backupCount": 20,
                "formatter": "json",
            },
            "performance_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "filename": os.path.join(settings.BASE_DIR, "logs", "performance.log"),
                "maxBytes": 1024 * 1024 * 50,  # 50MB
                "backupCount": 10,
                "formatter": "json",
            },
            "htmx_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "filename": os.path.join(settings.BASE_DIR, "logs", "htmx.log"),
                "maxBytes": 1024 * 1024 * 25,  # 25MB
                "backupCount": 5,
                "formatter": "json",
            },
        },
        "loggers": {
            "django": {
                "handlers": ["console", "file"],
                "level": "INFO",
                "propagate": False,
            },
            "django.security": {
                "handlers": ["console", "security_file"],
                "level": "WARNING",
                "propagate": False,
            },
            "security": {
                "handlers": ["console", "security_file"],
                "level": "INFO",
                "propagate": False,
            },
            "performance": {
                "handlers": ["console", "performance_file"],
                "level": "DEBUG" if settings.DEBUG else "INFO",
                "propagate": False,
            },
            "htmx": {
                "handlers": ["console", "htmx_file"],
                "level": "DEBUG" if settings.DEBUG else "INFO",
                "propagate": False,
            },
            "CLEAR": {
                "handlers": ["console", "file"],
                "level": "DEBUG" if settings.DEBUG else "INFO",
                "propagate": False,
            },
        },
        "root": {
            "handlers": ["console", "file"],
            "level": "INFO",
        },
    }

    # Add ELK stack handlers in production
    if not settings.DEBUG and ELK_STACK_CONFIG["elasticsearch"]["hosts"]:
        try:
            # Add Elasticsearch handler
            config["handlers"]["elasticsearch"] = {
                "class": "elasticsearch_logger.handlers.ElasticsearchHandler",
                "hosts": ELK_STACK_CONFIG["elasticsearch"]["hosts"],
                "use_ssl": ELK_STACK_CONFIG["elasticsearch"]["use_ssl"],
                "verify_certs": ELK_STACK_CONFIG["elasticsearch"]["verify_certs"],
                "username": ELK_STACK_CONFIG["elasticsearch"]["username"],
                "password": ELK_STACK_CONFIG["elasticsearch"]["password"],
                "index_name": f"clear-logs-{datetime.now().strftime('%Y.%m')}",
                "formatter": "json",
            }

            # Add Logstash handler
            config["handlers"]["logstash"] = {
                "class": "logstash.TCPLogstashHandler",
                "host": ELK_STACK_CONFIG["logstash"]["host"],
                "port": ELK_STACK_CONFIG["logstash"]["port"],
                "version": 1,
                "message_type": "clear-application",
                "formatter": "json",
            }

            # Update loggers to use ELK handlers
            for logger_name in ["django", "security", "performance", "htmx", "CLEAR"]:
                if logger_name in config["loggers"]:
                    config["loggers"][logger_name]["handlers"].extend(["elasticsearch", "logstash"])

        except ImportError:
            # ELK handlers not available, continue with file logging
            pass

    return config


def configure_structlog():
    """Configure structlog for enhanced structured logging"""
    structlog.configure(
        processors=[
            structlog.contextvars.merge_contextvars,
            structlog.processors.add_log_level,
            structlog.processors.add_logger_name,
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.dev.set_exc_info,
            structlog.processors.StackInfoRenderer(),
            (structlog.dev.ConsoleRenderer() if settings.DEBUG else structlog.processors.JSONRenderer()),
        ],
        wrapper_class=structlog.make_filtering_bound_logger(logging.INFO if not settings.DEBUG else logging.DEBUG),
        logger_factory=structlog.PrintLoggerFactory(),
        cache_logger_on_first_use=True,
    )


def setup_logging():
    """Set up centralized logging for the CLEAR platform"""
    # Ensure logs directory exists
    logs_dir = os.path.join(settings.BASE_DIR, "logs")
    os.makedirs(logs_dir, exist_ok=True)

    # Configure logging
    logging_config = get_centralized_logging_config()
    logging.config.dictConfig(logging_config)

    # Configure structlog
    configure_structlog()

    # Create logger instances
    security_logger = SecurityEventLogger()
    performance_logger = PerformanceLogger()
    htmx_logger = HTMXEventLogger()

    return {
        "security": security_logger,
        "performance": performance_logger,
        "htmx": htmx_logger,
    }


# Logging middleware integration
class CentralizedLoggingMiddleware:
    """Middleware to integrate centralized logging with request processing"""

    def __init__(self, get_response):
        self.get_response = get_response
        self.performance_logger = PerformanceLogger()
        self.security_logger = SecurityEventLogger()
        self.htmx_logger = HTMXEventLogger()

    def __call__(self, request):
        # import time (moved to top level)
        # import uuid (moved to top level)

        # Generate request ID
        request_id = str(uuid.uuid4())
        request.request_id = request_id

        # Start timing
        start_time = time.time()

        # Process request
        response = self.get_response(request)

        # Calculate duration
        duration_ms = (time.time() - start_time) * 1000

        # Log performance
        view_name = (
            getattr(request.resolver_match, "view_name", "unknown") if hasattr(request, "resolver_match") else "unknown"
        )
        user_id = str(request.user.id) if hasattr(request, "user") and request.user.is_authenticated else None

        self.performance_logger.log_request_performance(
            request_id=request_id,
            view_name=view_name,
            duration_ms=duration_ms,
            status_code=response.status_code,
            user_id=user_id,
            details={
                "method": request.method,
                "path": request.path,
                "remote_addr": request.META.get("REMOTE_ADDR"),
                "user_agent": request.META.get("HTTP_USER_AGENT"),
            },
        )

        # Log HTMX requests
        if request.headers.get("HX-Request"):
            self.htmx_logger.log_htmx_request(
                request_id=request_id,
                trigger=request.headers.get("HX-Trigger", ""),
                target=request.headers.get("HX-Target", ""),
                user_id=user_id,
                details={
                    "hx_current_url": request.headers.get("HX-Current-URL"),
                    "hx_prompt": request.headers.get("HX-Prompt"),
                    "hx_trigger_name": request.headers.get("HX-Trigger-Name"),
                },
            )

        return response


# Export main components
__all__ = [
    "ELK_STACK_CONFIG",
    "CentralizedLoggingMiddleware",
    "HTMXEventLogger",
    "PerformanceLogger",
    "SecurityEventLogger",
    "get_centralized_logging_config",
    "setup_logging",
]
